import { Minute } from "@shared/utils/time";
import { PluginManager, Hook } from "@server/utils/PluginManager";
import config from "../plugin.json";
import { GitHubIssueProvider } from "./GitHubIssueProvider";
import router from "./api/github";
import env from "./env";
import { GitHub } from "./github";
import { uninstall } from "./uninstall";

const enabled =
  !!env.GITHUB_CLIENT_ID &&
  !!env.GITHUB_CLIENT_SECRET &&
  !!env.GITHUB_APP_NAME &&
  !!env.GITHUB_APP_ID &&
  !!env.GITHUB_APP_PRIVATE_KEY;

if (enabled) {
  PluginManager.add([
    {
      ...config,
      type: Hook.API,
      value: router,
    },
    {
      type: Hook.IssueProvider,
      value: new GitHubIssueProvider(),
    },
    {
      type: Hook.UnfurlProvider,
      value: { unfurl: GitHub.unfurl, cacheExpiry: Minute.seconds },
    },
    {
      type: Hook.Uninstall,
      value: uninstall,
    },
  ]);
}

import * as React from "react";

type Props = {
  /** The size of the icon, 24px is default to match standard icons */
  size?: number;
  /** The color of the icon, defaults to the current text color */
  fill?: string;
  className?: string;
};

function DiscordLogo({ size = 24, fill = "currentColor", className }: Props) {
  return (
    <svg
      fill={fill}
      width={size}
      height={size}
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M17.5535 7.01557C16.5023 6.5343 15.3925 6.19287 14.2526 6C14.0966 6.27886 13.9555 6.56577 13.8298 6.85952C12.6155 6.67655 11.3807 6.67655 10.1664 6.85952C10.0406 6.5658 9.89949 6.27889 9.74357 6C8.60289 6.1945 7.4924 6.53674 6.44013 7.01809C4.3511 10.1088 3.78479 13.1228 4.06794 16.0941C5.29133 16.9979 6.66066 17.6854 8.11639 18.1265C8.44417 17.6856 8.73422 17.2179 8.98346 16.7283C8.51007 16.5515 8.05317 16.3334 7.61804 16.0764C7.73256 15.9934 7.84456 15.9078 7.95279 15.8247C9.21891 16.4202 10.6008 16.7289 12 16.7289C13.3991 16.7289 14.781 16.4202 16.0472 15.8247C16.1566 15.9141 16.2686 15.9997 16.3819 16.0764C15.9459 16.3338 15.4882 16.5523 15.014 16.7296C15.2629 17.2189 15.553 17.6862 15.881 18.1265C17.338 17.6871 18.7084 17 19.932 16.0953C20.2642 12.6497 19.3644 9.66336 17.5535 7.01557ZM9.34212 14.2668C8.55307 14.2668 7.90119 13.5507 7.90119 12.6698C7.90119 11.7889 8.53042 11.0665 9.3396 11.0665C10.1488 11.0665 10.7956 11.7889 10.7818 12.6698C10.7679 13.5507 10.1463 14.2668 9.34212 14.2668ZM14.6578 14.2668C13.8675 14.2668 13.2182 13.5507 13.2182 12.6698C13.2182 11.7889 13.8474 11.0665 14.6578 11.0665C15.4683 11.0665 16.1101 11.7889 16.0962 12.6698C16.0824 13.5507 15.462 14.2668 14.6578 14.2668Z"
      />
    </svg>
  );
}

export default DiscordLogo;

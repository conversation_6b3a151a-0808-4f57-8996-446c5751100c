import { createLazyComponent } from "~/components/LazyLoad";
import { Hook, PluginManager } from "~/utils/PluginManager";
import config from "../plugin.json";
import Icon from "./Icon";

PluginManager.add([
  {
    ...config,
    type: Hook.Settings,
    value: {
      group: "Integrations",
      icon: Icon,
      description:
        "Connect your Outline workspace to Zapier to automate workflows and integrate with thousands of other tools.",
      component: createLazyComponent(() => import("./Settings")),
    },
  },
]);

name: Lint

on:
  pull_request:
    branches: [ main ]

jobs:
  run-linters:
    if: startsWith(github.actor, 'codegen-sh')
    name: Run linters
    runs-on: ubuntu-latest

    permissions:
      # Give the default GITHUB_TOKEN write permission to commit and push the
      # added or changed files to the repository.
      contents: write

    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 20.x
          cache: 'yarn'
      - run: yarn install --frozen-lockfile
      - run: yarn lint --fix

      - name: Commit changes
        uses: stefanzweifel/git-auto-commit-action@v5
        with:
          commit_message: 'Applied automatic fixes'

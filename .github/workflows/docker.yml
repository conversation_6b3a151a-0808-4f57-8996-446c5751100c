name: Docker

on:
  push:
    tags:
      - "v*"

env:
  IMAGE_NAME: outlinewiki/outline
  BASE_IMAGE_NAME: outlinewiki/outline-base

jobs:
  build-arm:
    runs-on: ubicloud-standard-8-arm
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Docker base meta
        id: base_meta
        uses: docker/metadata-action@v5
        with:
          images: |
            ${{ env.BASE_IMAGE_NAME }}
          tags: |
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and push base image
        id: base_build
        uses: docker/build-push-action@v6
        with:
          context: .
          file: Dockerfile.base
          labels: ${{ steps.base_meta.outputs.labels }}
          tags: ${{ env.BASE_IMAGE_NAME }}
          outputs: type=image,push-by-digest=true,name-canonical=true,push=true
          platforms: linux/arm64
          cache-from: type=gha
          cache-to: type=gha,mode=max
          pull: false

      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: |
            ${{ env.IMAGE_NAME }}
          tags: |
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}

      - name: Build and push
        id: build
        uses: docker/build-push-action@v6
        with:
          context: .
          file: Dockerfile
          labels: ${{ steps.meta.outputs.labels }}
          tags: ${{ env.IMAGE_NAME }}
          outputs: type=image,push-by-digest=true,name-canonical=true,push=true
          platforms: linux/arm64
          cache-from: type=gha
          cache-to: type=gha,mode=max
          pull: false
          build-args: |
            BASE_IMAGE=${{ env.BASE_IMAGE_NAME }}@${{ steps.base_build.outputs.digest }}

      - name: Export digest
        run: |
          mkdir -p ${{ runner.temp }}/digests
          digest="${{ steps.build.outputs.digest }}"
          touch "${{ runner.temp }}/digests/${digest#sha256:}"

      - name: Upload digest
        uses: actions/upload-artifact@v4
        with:
          name: digests-linux-arm64
          path: ${{ runner.temp }}/digests/*
          if-no-files-found: error
          retention-days: 1

  build-amd:
    runs-on: ubicloud-standard-8
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Docker base meta
        id: base_meta
        uses: docker/metadata-action@v5
        with:
          images: |
            ${{ env.BASE_IMAGE_NAME }}
          tags: |
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and push base image
        id: base_build
        uses: docker/build-push-action@v6
        with:
          context: .
          file: Dockerfile.base
          labels: ${{ steps.base_meta.outputs.labels }}
          tags: ${{ env.BASE_IMAGE_NAME }}
          outputs: type=image,push-by-digest=true,name-canonical=true,push=true
          platforms: linux/amd64
          cache-from: type=gha
          cache-to: type=gha,mode=max
          pull: false

      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: |
            ${{ env.IMAGE_NAME }}
          tags: |
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}

      - name: Build and push
        id: build
        uses: docker/build-push-action@v6
        with:
          context: .
          file: Dockerfile
          labels: ${{ steps.meta.outputs.labels }}
          tags: ${{ env.IMAGE_NAME }}
          outputs: type=image,push-by-digest=true,name-canonical=true,push=true
          platforms: linux/amd64
          cache-from: type=gha
          cache-to: type=gha,mode=max
          pull: false
          build-args: |
            BASE_IMAGE=${{ env.BASE_IMAGE_NAME }}@${{ steps.base_build.outputs.digest }}

      - name: Export digest
        run: |
          mkdir -p ${{ runner.temp }}/digests
          digest="${{ steps.build.outputs.digest }}"
          touch "${{ runner.temp }}/digests/${digest#sha256:}"

      - name: Upload digest
        uses: actions/upload-artifact@v4
        with:
          name: digests-linux-amd64
          path: ${{ runner.temp }}/digests/*
          if-no-files-found: error
          retention-days: 1

  merge:
    runs-on: ubicloud-standard-8
    needs:
      - build-amd
      - build-arm
    steps:
      - name: Download digests
        uses: actions/download-artifact@v4
        with:
          path: ${{ runner.temp }}/digests
          pattern: digests-*
          merge-multiple: true

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}

      - name: Create manifest list and push
        working-directory: ${{ runner.temp }}/digests
        run: |
          docker buildx imagetools create $(jq -cr '.tags | map("-t " + .) | join(" ")' <<< "$DOCKER_METADATA_OUTPUT_JSON") \
            $(printf '${{ env.IMAGE_NAME }}@sha256:%s ' *)

      - name: Inspect image
        run: |
          docker buildx imagetools inspect ${{ env.IMAGE_NAME }}:${{ steps.meta.outputs.version }}
name: "Close Stale PRs"
on:
  workflow_dispatch:
  schedule:
    - cron: "30 1 * * *"

permissions:
  issues: write
  pull-requests: write

jobs:
  stale:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/stale@v5
        with:
          stale-pr-message: "This PR is stale because it has been open 90 days with no activity. Remove stale label or comment or this will be closed in 5 days"
          stale-issue-message: "This issue is stale because it has been open 90 days with no activity. Remove stale label or comment or this will be closed in 5 days"
          close-pr-message: "Automatically closed due to inactivity"
          close-issue-message: "Automatically closed due to inactivity"
          days-before-issue-stale: 120
          days-before-pr-stale: 60
          days-before-close: 5
          operations-per-run: 60
          stale-issue-label: stale
          stale-pr-label: stale
          exempt-issue-labels: "security,pinned,A1"
      - name: Print outputs
        run: echo ${{ join(steps.stale.outputs.*, ',') }}

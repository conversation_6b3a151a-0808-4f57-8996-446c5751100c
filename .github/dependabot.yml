# To get started with Dependabot version updates, you'll need to specify which
# package ecosystems to update and where the package manifests are located.
# Please see the documentation for all configuration options:
# https://docs.github.com/github/administering-a-repository/configuration-options-for-dependency-updates

version: 2
updates:
  - package-ecosystem: "npm" # See documentation for possible values
    directory: "/" # Location of package manifests
    open-pull-requests-limit: 5
    ignore:
      - dependency-name: "*"
        update-types: ["version-update:semver-major"]
    schedule:
      interval: "weekly"
    groups:
      babel:
        patterns:
          - "@babel/*"
      sentry:
        patterns:
          - "@sentry/*"
      fortawesome:
        patterns:
          - "@fortawesome/*"
      aws:
        patterns:
          - "@aws-sdk/*"

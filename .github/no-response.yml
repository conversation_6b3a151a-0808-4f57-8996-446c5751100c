# Configuration for probot-no-response - https://github.com/probot/no-response

# Number of days of inactivity before an Issue is closed for lack of response
daysUntilClose: 7

# Label requiring a response
responseRequiredLabel: more information needed

# Comment to post when closing an Issue for lack of response. Set to `false` to disable
closeComment: >
  This issue has been automatically closed because there has been no response
  to the request for more information. With only the details that are currently
  in the issue, we don't have enough information to take action.

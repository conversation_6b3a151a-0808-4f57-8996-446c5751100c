{"New API key": "Khóa API mới", "Open collection": "Mở bộ sưu tập", "New collection": "<PERSON><PERSON> s<PERSON>u tập mới", "Create a collection": "<PERSON><PERSON><PERSON> một bộ sưu tập", "Edit": "Chỉnh sửa", "Edit collection": "Chỉnh sửa bộ sưu tập", "Permissions": "<PERSON><PERSON> quyền", "Collection permissions": "<PERSON><PERSON><PERSON><PERSON> thu thập", "Share this collection": "<PERSON><PERSON> sẻ bộ sưu tập này", "Search in collection": "<PERSON><PERSON><PERSON> kiếm trong bộ sưu tập", "Star": "Gán sao", "Unstar": "Bỏ gắn sao", "Subscribe": "<PERSON><PERSON><PERSON> ký theo dõi", "Subscribed to document notifications": "<PERSON><PERSON> đăng ký theo dõi thông báo của tài liệu", "Unsubscribe": "Huỷ đăng ký theo dõi", "Unsubscribed from document notifications": "Huỷ đăng ký theo dõi thông báo của tài liệu", "Archive": "<PERSON><PERSON><PERSON> tr<PERSON>", "Archive collection": "Archive collection", "Collection archived": "Collection archived", "Archiving": "<PERSON><PERSON><PERSON> tr<PERSON>", "Archiving this collection will also archive all documents within it. Documents from the collection will no longer be visible in search results.": "Archiving this collection will also archive all documents within it. Documents from the collection will no longer be visible in search results.", "Restore": "<PERSON><PERSON><PERSON><PERSON>h<PERSON>c", "Collection restored": "Collection restored", "Delete": "Xóa", "Delete collection": "<PERSON><PERSON><PERSON> bộ sưu tập", "New template": "Mẫu mới", "Delete comment": "<PERSON><PERSON><PERSON> b<PERSON>nh luận", "Mark as resolved": "<PERSON><PERSON><PERSON> dấu là đã gi<PERSON>i quyết", "Thread resolved": "Chủ đề đã đư<PERSON><PERSON> g<PERSON><PERSON> quyết", "Mark as unresolved": "<PERSON><PERSON><PERSON> dấu là chưa gi<PERSON>i quyết", "View reactions": "View reactions", "Reactions": "Reactions", "Copy ID": "Sao chép ID", "Clear IndexedDB cache": "Xóa bộ nhớ đệm IndexedDB", "IndexedDB cache cleared": "Đã xóa bộ nhớ đệm IndexedDB", "Toggle debug logging": "<PERSON><PERSON><PERSON>, tắt lưu thông tin sửa lỗi", "Debug logging enabled": "<PERSON><PERSON> bật ghi nhật ký gỡ lỗi", "Debug logging disabled": "<PERSON><PERSON> tắt ghi nhật ký gỡ lỗi", "Development": "<PERSON><PERSON><PERSON> ph<PERSON>t triển", "Open document": "Mở tài liệu", "New document": "<PERSON><PERSON><PERSON> li<PERSON>u mới", "New draft": "New draft", "New from template": "<PERSON><PERSON><PERSON> mới từ bản mẫu", "New nested document": "<PERSON><PERSON><PERSON> liệu mới lồng nhau", "Publish": "<PERSON><PERSON><PERSON>", "Published {{ documentName }}": "<PERSON><PERSON> xu<PERSON>t b<PERSON> {{ documentName }}", "Publish document": "<PERSON><PERSON><PERSON> bản tài li<PERSON>u", "Unpublish": "Bỏ đăng tải", "Unpublished {{ documentName }}": "<PERSON><PERSON><PERSON> xu<PERSON><PERSON> b<PERSON> {{ documentName }}", "Share this document": "<PERSON>a sẻ tài liệu này", "HTML": "HTML", "PDF": "PDF", "Exporting": "<PERSON><PERSON>", "Markdown": "<PERSON><PERSON>", "Download": "<PERSON><PERSON><PERSON> về", "Download document": "<PERSON><PERSON><PERSON> tài li<PERSON>u", "Copy as Markdown": "<PERSON>o chép dư<PERSON>i dạng Markdown", "Markdown copied to clipboard": "Đã sao chép Markdown vào clipboard", "Copy as text": "Copy as text", "Text copied to clipboard": "Text copied to clipboard", "Copy public link": "Copy public link", "Link copied to clipboard": "Đường Link đã sao chép vào bộ nhớ tạm", "Copy link": "Sao chép Link", "Copy": "Sao chép", "Duplicate": "<PERSON><PERSON><PERSON> b<PERSON>n", "Duplicate document": "T<PERSON><PERSON> liệu trùng lặp", "Copy document": "<PERSON>o ch<PERSON>p tài li<PERSON>u", "collection": "bộ s<PERSON>u tập", "Pin to {{collectionName}}": "<PERSON><PERSON> vào {{collectionName}}", "Pinned to collection": "<PERSON><PERSON> ghim vào bộ sưu tập", "Pin to home": "<PERSON><PERSON> vào nhà", "Pinned to home": "<PERSON><PERSON> vào nhà", "Pin": "<PERSON><PERSON>", "Search in document": "<PERSON><PERSON><PERSON> kiếm tài liệu", "Print": "In", "Print document": "In tài liệu", "Import document": "<PERSON><PERSON><PERSON><PERSON> tài li<PERSON>u", "Templatize": "Mẫu", "Create template": "Tạo mẫu", "Open random document": "Mở một tài liệu ngẫu nhiên", "Search documents for \"{{searchQuery}}\"": "<PERSON><PERSON><PERSON> kiếm tài liệu cho \"{{searchQuery}}\"", "Move to workspace": "<PERSON> chuyển đến không gian làm việc", "Move": "<PERSON>", "Move to collection": "<PERSON> chuyển đến bộ sưu tập", "Move {{ documentType }}": "<PERSON> {{ documentType }}", "Are you sure you want to archive this document?": "Bạn có chắc chắn muốn lưu trữ tài liệu này không?", "Document archived": "<PERSON><PERSON> lưu trữ tài liệu", "Archiving this document will remove it from the collection and search results.": "Vi<PERSON><PERSON> lưu trữ tài liệu này sẽ xóa nó khỏi bộ sưu tập và kết quả tìm kiếm.", "Delete {{ documentName }}": "Xóa {{ documentName }}", "Permanently delete": "Xoá vĩnh viễn", "Permanently delete {{ documentName }}": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> vi<PERSON><PERSON> {{ documentName }}", "Empty trash": "<PERSON><PERSON><PERSON> s<PERSON> r<PERSON>c", "Permanently delete documents in trash": "<PERSON><PERSON><PERSON> v<PERSON>nh vi<PERSON><PERSON> các tài liệu trong thùng rác", "Comments": "<PERSON><PERSON><PERSON> lu<PERSON>", "History": "<PERSON><PERSON><PERSON> s<PERSON>", "Insights": "Th<PERSON>ng tin chi tiết", "Disable viewer insights": "<PERSON><PERSON><PERSON> thông tin chi tiết của người xem", "Enable viewer insights": "<PERSON><PERSON><PERSON> thông tin chi tiết về người xem", "Leave document": "Leave document", "You have left the shared document": "You have left the shared document", "Could not leave document": "Could not leave document", "Home": "Trang chủ", "Drafts": "<PERSON><PERSON><PERSON>", "Search": "<PERSON><PERSON><PERSON>", "Trash": "<PERSON><PERSON><PERSON><PERSON>c", "Settings": "Cài đặt", "Profile": "<PERSON><PERSON> sơ", "Templates": "<PERSON><PERSON><PERSON> viện mẫu", "Notifications": "<PERSON><PERSON><PERSON><PERSON> báo", "Preferences": "Cài đặt", "Documentation": "<PERSON><PERSON><PERSON> l<PERSON>", "API documentation": "Tài liệu API", "Toggle sidebar": "Bật/tắt Sidebar", "Send us feedback": "<PERSON><PERSON><PERSON> p<PERSON><PERSON>n hồi", "Report a bug": "Báo cáo lỗi", "Changelog": "<PERSON><PERSON><PERSON> sử thay đổi", "Keyboard shortcuts": "<PERSON><PERSON><PERSON>", "Download {{ platform }} app": "<PERSON><PERSON><PERSON> xu<PERSON>ng <PERSON>ng dụng {{ platform }}", "Log out": "<PERSON><PERSON><PERSON> xu<PERSON>", "Mark notifications as read": "<PERSON><PERSON><PERSON> dấu thông báo là đã đọc", "Archive all notifications": "<PERSON><PERSON><PERSON> trữ tất cả thông báo", "New App": "New App", "New Application": "New Application", "This version of the document was deleted": "This version of the document was deleted", "Link copied": "Đã sao chép Link", "Dark": "Dark", "Light": "Light", "System": "<PERSON><PERSON> th<PERSON>", "Appearance": "<PERSON><PERSON><PERSON>", "Change theme": "<PERSON>hay đổi chủ đề", "Change theme to": "Đổi chủ đề thành", "Switch workspace": "Chuyển đổi không gian làm việc", "Select a workspace": "Chọn workspace", "New workspace": "Workspace mới", "Create a workspace": "Tạo workspace", "Login to workspace": "<PERSON><PERSON><PERSON> nh<PERSON>p v<PERSON><PERSON> không gian làm việc", "Invite people": "<PERSON><PERSON><PERSON> mọi ng<PERSON>ời", "Invite to workspace": "Mời vào không gian làm việc", "Promote to {{ role }}": "<PERSON><PERSON><PERSON><PERSON> chức lên {{ role }}", "Demote to {{ role }}": "<PERSON><PERSON> cấp xu<PERSON>ng {{ role }}", "Update role": "<PERSON><PERSON><PERSON> nhật vai trò", "Delete user": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> dùng", "Collection": "<PERSON><PERSON> s<PERSON>u tập", "Collections": "<PERSON><PERSON> s<PERSON>u tập", "Debug": "Gỡ lỗi", "Document": "<PERSON><PERSON><PERSON> l<PERSON>", "Documents": "<PERSON><PERSON><PERSON> l<PERSON>", "Recently viewed": "<PERSON><PERSON> xem gần đ<PERSON>y", "Revision": "<PERSON><PERSON>", "Navigation": "<PERSON><PERSON><PERSON><PERSON>", "Notification": "<PERSON><PERSON><PERSON><PERSON> báo", "People": "<PERSON><PERSON><PERSON>", "Workspace": "Workspace", "Recent searches": "<PERSON><PERSON><PERSON> ki<PERSON>m gần đ<PERSON>y", "currently editing": "hiện đang chỉnh sửa", "currently viewing": "hiện đang xem", "previously edited": "đã chỉnh sửa trước", "You": "Bạn", "Viewers": "<PERSON><PERSON><PERSON><PERSON> xem", "Collections are used to group documents and choose permissions": "<PERSON><PERSON> sưu tập đư<PERSON>c sử dụng để nhóm các tài liệu và chọn quyền", "Name": "<PERSON><PERSON><PERSON>", "The default access for workspace members, you can share with more users or groups later.": "<PERSON><PERSON><PERSON><PERSON> truy cập mặc định dành cho các thành viên không gian làm việc, bạn có thể chia sẻ với nhiều người dùng hoặc nhóm hơn sau này.", "Public document sharing": "<PERSON>a sẻ tài liệu công khai", "Allow documents within this collection to be shared publicly on the internet.": "Cho phép các tài liệu trong bộ sưu tập này được chia sẻ công khai trên Internet.", "Commenting": "<PERSON><PERSON> b<PERSON>nh lu<PERSON>n", "Allow commenting on documents within this collection.": "Allow commenting on documents within this collection.", "Saving": "<PERSON><PERSON> l<PERSON> l<PERSON>i", "Save": "<PERSON><PERSON><PERSON>", "Creating": "<PERSON><PERSON> t<PERSON>o", "Create": "Tạo", "Collection deleted": "<PERSON><PERSON> sưu tập đã xóa", "I’m sure – Delete": "<PERSON><PERSON><PERSON> ch<PERSON>c chắn - <PERSON><PERSON>", "Deleting": "<PERSON><PERSON>", "Are you sure about that? Deleting the <em>{{collectionName}}</em> collection is permanent and cannot be restored, however all published documents within will be moved to the trash.": "Vi<PERSON><PERSON> xóa bộ sưu tập <em>{{collectionName}}</em> là vĩnh viễn và không thể khôi phục. Mặc dù, tất cả các tài liệu đã xuất bản trong đó sẽ được chuyển vào thùng rác. Bạn có chắc muốn xoá?", "Also, <em>{{collectionName}}</em> is being used as the start view – deleting it will reset the start view to the Home page.": "<PERSON><PERSON><PERSON><PERSON> ra, <em>{{collectionName}}</em> đang được sử dụng làm chế độ xem bắt đầu - việc xóa nó sẽ đặt lại chế độ xem bắt đầu về Trang chủ.", "Type a command or search": "<PERSON><PERSON><PERSON><PERSON> lệnh hoặc tìm kiếm", "Choose a template": "<PERSON><PERSON><PERSON> một kiểu mẫu", "Are you sure you want to permanently delete this entire comment thread?": "Bạn có chắc chắn muốn xóa bình luận này vĩnh viễn?", "Are you sure you want to permanently delete this comment?": "Bạn có chắc chắn muốn xóa bình luận này vĩnh viễn?", "Confirm": "<PERSON><PERSON><PERSON>", "manage access": "manage access", "view and edit access": "xem và chỉnh sửa quyền truy cập", "view only access": "chỉ xem quyền truy cập", "no access": "kh<PERSON><PERSON> có quyền truy cập", "You do not have permission to move {{ documentName }} to the {{ collectionName }} collection": "You do not have permission to move {{ documentName }} to the {{ collectionName }} collection", "Move document": "<PERSON><PERSON><PERSON><PERSON> tài li<PERSON>u", "Moving": "<PERSON><PERSON>", "Moving the document <em>{{ title }}</em> to the {{ newCollectionName }} collection will change permission for all workspace members from <em>{{ prevPermission }}</em> to <em>{{ newPermission }}</em>.": "Vi<PERSON><PERSON> di chuyển tài liệu <em>{{ title }}</em> sang bộ sưu tập {{ newCollectionName }} sẽ thay đổi quyền cho tất cả các thành viên trong không gian làm việc từ <em>{{ prevPermission }}</em> thành <em>{{ newPermission }}</em>.", "Submenu": "<PERSON><PERSON> phụ", "Collections could not be loaded, please reload the app": "<PERSON><PERSON><PERSON><PERSON> thể tải bộ sưu tập, vui lòng tải lại ứng dụng", "Default collection": "<PERSON><PERSON> sưu tập mặc định", "Start view": "<PERSON><PERSON><PERSON> đ<PERSON>u xem", "Install now": "Cài đặt ngay", "Deleted Collection": "<PERSON><PERSON><PERSON>", "Untitled": "Ch<PERSON>a đặt tên", "Unpin": "Bỏ ghim", "{{ minutes }}m read": "{{ minutes }}m read", "Select a location to copy": "Select a location to copy", "Document copied": "Document copied", "Couldn’t copy the document, try again?": "Couldn’t copy the document, try again?", "Include nested documents": "<PERSON><PERSON> gồm các tài liệu lồng nhau", "Copy to <em>{{ location }}</em>": "Copy to <em>{{ location }}</em>", "Search collections & documents": "<PERSON><PERSON><PERSON> kiếm bộ sưu tập và tài liệu", "No results found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kết quả", "New": "<PERSON><PERSON><PERSON>", "Only visible to you": "Chỉ hiển thị với bạn", "Draft": "<PERSON><PERSON><PERSON>", "Template": "Mẫu", "You updated": "<PERSON>ạn đã cập nh<PERSON>t", "{{ userName }} updated": "{{ userName }} <PERSON><PERSON><PERSON><PERSON> c<PERSON> nh<PERSON>t", "You deleted": "Bạn đã xóa", "{{ userName }} deleted": "{{ userName}} đ<PERSON> bị xóa", "You archived": "Bạn đã lưu trữ", "{{ userName }} archived": "{{ userName}} <PERSON><PERSON><PERSON><PERSON> l<PERSON>u trữ", "Imported": "<PERSON><PERSON> nh<PERSON>p", "You created": "Bạn đã tạo", "{{ userName }} created": "{{ userName }} <PERSON><PERSON><PERSON><PERSON> t<PERSON>o", "You published": "Bạn vừa đăng", "{{ userName }} published": "{{ userName}} đã đăng tải", "Never viewed": "<PERSON><PERSON>a bao giờ xem", "Viewed": "Đã xem", "in": "trong", "nested document": "tài li<PERSON>u lồng nhau", "nested document_plural": "tài li<PERSON>u lồng nhau", "{{ total }} task": "{{ total }} nhiệm vụ", "{{ total }} task_plural": "{{ total }} nhiệm vụ", "{{ completed }} task done": "{{ completed }} nhiệm vụ đã hoàn thành", "{{ completed }} task done_plural": "{{ completed }} nhiệm vụ đã hoàn thành", "{{ completed }} of {{ total }} tasks": "{{ completed }} trong số {{ total }} nhiệm vụ", "Currently editing": "Hiện đang chỉnh sửa", "Currently viewing": "<PERSON><PERSON><PERSON> đang xem", "Viewed {{ timeAgo }}": "<PERSON><PERSON> xem {{ timeAgo }}", "Module failed to load": "<PERSON><PERSON><PERSON><PERSON> t<PERSON><PERSON><PERSON><PERSON><PERSON>", "Loading Failed": "<PERSON><PERSON><PERSON> không thành công", "Sorry, part of the application failed to load. This may be because it was updated since you opened the tab or because of a failed network request. Please try reloading.": "<PERSON><PERSON> lỗi, một phần của ứng dụng không tải được. Điều này có thể là do nó đã được cập nhật kể từ khi bạn mở tab hoặc do yêu cầu mạng không thành công. <PERSON><PERSON> lòng thử tải lại.", "Reload": "<PERSON><PERSON><PERSON> l<PERSON>i", "Something Unexpected Happened": "<PERSON><PERSON><PERSON>u gì đó không mong muốn đã xảy ra", "Sorry, an unrecoverable error occurred{{notified}}. Please try reloading the page, it may have been a temporary glitch.": "<PERSON><PERSON> lỗi, đã xảy ra lỗi không thể khôi phục{{notified}}. <PERSON><PERSON> lòng thử tải lại trang, nó có thể là một trục trặc tạm thời.", "our engineers have been notified": "c<PERSON>c kỹ sư của chúng tôi đã đ<PERSON><PERSON><PERSON> thông báo", "Show detail": "<PERSON><PERSON><PERSON> thị chi tiết", "Revision deleted": "Revision deleted", "Current version": "<PERSON><PERSON><PERSON> bản hiện tại", "{{userName}} edited": "{{userName}} đã chỉnh sửa", "{{userName}} archived": "{{userName}} <PERSON><PERSON><PERSON><PERSON> l<PERSON>u trữ", "{{userName}} restored": "{{userName}} kh<PERSON><PERSON> phục", "{{userName}} deleted": "{{userName}} đ<PERSON> bị xóa", "{{userName}} added {{addedUserName}}": "{{userName}} đã thêm {{addedUserName}}", "{{userName}} removed {{removedUserName}}": "{{userName}} đã xóa {{removedUserName}}", "{{userName}} moved from trash": "{{userName}} chuyển từ thùng rác", "{{userName}} published": "{{userName}} đã đăng tải", "{{userName}} unpublished": "{{userName}} bỏ đăng tải", "{{userName}} moved": "{{userName}} đ<PERSON> chuyển", "Export started": "<PERSON><PERSON> bắt đầu xuất", "Your file will be available in {{ location }} soon": "<PERSON><PERSON><PERSON> của bạn sẽ sớm có ở {{ location }}", "View": "<PERSON><PERSON><PERSON> thị", "A ZIP file containing the images, and documents in the Markdown format.": "Tệp ZIP chứa hình ảnh và tài liệu ở định dạng Markdown.", "A ZIP file containing the images, and documents as HTML files.": "Tệp ZIP chứa hình ảnh và tài liệu ở định dạng Markdown.", "Structured data that can be used to transfer data to another compatible {{ appName }} instance.": "Dữ liệu có cấu trúc có thể được sử dụng để truyền dữ liệu sang một phiên bản {{ appName }} tương thích khác.", "Export": "<PERSON><PERSON><PERSON>", "Exporting the collection <em>{{collectionName}}</em> may take some time.": "<PERSON>u<PERSON> trình xuất bộ sưu tập <em>{{collectionName}}</em> có thể mất chút thời gian.", "You will receive an email when it's complete.": "Bạn sẽ nhận đư<PERSON><PERSON> một email khi nó hoàn tất.", "Include attachments": "<PERSON><PERSON> gồm tập tin đ<PERSON>h kèm", "Including uploaded images and files in the exported data": "<PERSON><PERSON> gồm hình <PERSON>nh và tệp đã tải lên trong dữ liệu đã xuất", "{{count}} more user": "{{count}} more user", "{{count}} more user_plural": "{{count}} more users", "Filter": "<PERSON><PERSON> L<PERSON>", "No results": "<PERSON><PERSON><PERSON><PERSON> có kết quả", "{{authorName}} created <3></3>": "{{authorName}} đ<PERSON> t<PERSON>o <3></3>", "{{authorName}} opened <3></3>": "{{authorName}} đã mở <3></3>", "Search emoji": "<PERSON><PERSON><PERSON>", "Search icons": "<PERSON><PERSON><PERSON> kiếm biểu tư<PERSON>", "Choose default skin tone": "<PERSON><PERSON><PERSON> tông màu mặc định", "Show menu": "<PERSON><PERSON><PERSON>", "Icon Picker": "<PERSON><PERSON><PERSON>", "Icons": "<PERSON><PERSON><PERSON><PERSON>", "Emojis": "Emojis", "Remove": "Bỏ", "All": "<PERSON><PERSON><PERSON> c<PERSON>", "Frequently Used": "<PERSON><PERSON><PERSON><PERSON><PERSON> dùng", "Search Results": "<PERSON><PERSON><PERSON> qu<PERSON> tìm kiếm", "Smileys & People": "Mặt cười & Con người", "Animals & Nature": "Động vật & <PERSON><PERSON><PERSON><PERSON>", "Food & Drink": "<PERSON><PERSON><PERSON><PERSON> ăn & <PERSON><PERSON> uống", "Activity": "<PERSON><PERSON><PERSON> đ<PERSON>", "Travel & Places": "<PERSON> & <PERSON><PERSON><PERSON> điể<PERSON>", "Objects": "<PERSON><PERSON><PERSON>", "Symbols": "<PERSON><PERSON><PERSON><PERSON>", "Flags": "Cờ", "Select a color": "<PERSON><PERSON><PERSON>", "Loading": "<PERSON><PERSON> t<PERSON>", "Permission": "<PERSON><PERSON> quyền", "View only": "Chỉ xem", "Can edit": "<PERSON><PERSON> thể chỉnh sửa", "No access": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập", "Default access": "<PERSON><PERSON><PERSON> cập mặc định", "Change Language": "<PERSON><PERSON><PERSON>", "Dismiss": "Bỏ qua", "You’re offline.": "Bạn đang Offline.", "Sorry, an error occurred.": "<PERSON><PERSON> lỗi, đã xảy ra lỗi.", "Click to retry": "<PERSON><PERSON><PERSON><PERSON> để thử lại", "Back": "Quay Lại", "Unknown": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>", "Mark all as read": "<PERSON><PERSON><PERSON> dấu tất cả là đã đọc", "You're all caught up": "<PERSON>ạn đã xem hết", "Icon": "Icon", "My App": "My App", "Tagline": "Tagline", "A short description": "A short description", "Callback URLs": "Callback URLs", "Published": "<PERSON><PERSON> đăng tải", "Allow this app to be installed by other workspaces": "Allow this app to be installed by other workspaces", "{{ username }} reacted with {{ emoji }}": "{{ username }} reacted with {{ emoji }}", "{{ firstUsername }} and {{ secondUsername }} reacted with {{ emoji }}": "{{ firstUsername }} and {{ secondUsername }} reacted with {{ emoji }}", "{{ firstUsername }} and {{ count }} others reacted with {{ emoji }}": "{{ firstUsername }} and {{ count }} other reacted with {{ emoji }}", "{{ firstUsername }} and {{ count }} others reacted with {{ emoji }}_plural": "{{ firstUsername }} and {{ count }} others reacted with {{ emoji }}", "Add reaction": "Add reaction", "Reaction picker": "Reaction picker", "Could not load reactions": "Could not load reactions", "Reaction": "Reaction", "Results": "<PERSON><PERSON><PERSON> qu<PERSON>", "No results for {{query}}": "<PERSON><PERSON><PERSON><PERSON> có kết quả cho {{query}}", "Manage": "<PERSON><PERSON><PERSON><PERSON> lý", "All members": "<PERSON><PERSON><PERSON> cả thành viên", "Everyone in the workspace": "<PERSON>ọi ng<PERSON>ời trong không gian làm việc", "{{ count }} member": "{{$count}} T<PERSON><PERSON><PERSON> viên", "{{ count }} member_plural": "{{$count}} T<PERSON><PERSON><PERSON> viên", "Invite": "<PERSON><PERSON><PERSON>", "{{ userName }} was added to the collection": "{{ userName }} đ<PERSON> đư<PERSON><PERSON> thêm vào bộ sưu tập", "{{ count }} people added to the collection": "{{ count }} ngư<PERSON>i đã đư<PERSON><PERSON> thêm vào bộ sưu tập", "{{ count }} people added to the collection_plural": "{{ count }} ngư<PERSON>i đã đư<PERSON><PERSON> thêm vào bộ sưu tập", "{{ count }} people and {{ count2 }} groups added to the collection": "đã thêm {{ count }} người và {{ count2 }} nhóm vào bộ sưu tập", "{{ count }} people and {{ count2 }} groups added to the collection_plural": "đã thêm {{ count }} người và {{ count2 }} nhóm vào bộ sưu tập", "Add": "<PERSON><PERSON><PERSON><PERSON>", "Add or invite": "Thêm hoặc gửi lời mời", "Viewer": "<PERSON><PERSON><PERSON><PERSON> xem", "Editor": "<PERSON><PERSON><PERSON><PERSON> tập viên", "Suggestions for invitation": "Gợi ý cho lời mời", "No matches": "<PERSON><PERSON><PERSON><PERSON> có kết quả nào phù hợp", "Can view": "<PERSON><PERSON> thể xem", "Everyone in the collection": "<PERSON><PERSON><PERSON> người trong bộ sưu tập", "You have full access": "<PERSON><PERSON>n có toàn quyền", "Created the document": "<PERSON><PERSON><PERSON> tài li<PERSON>u", "Other people": "Ngư<PERSON><PERSON>c", "Other workspace members may have access": "Other workspace members may have access", "This document may be shared with more workspace members through a parent document or collection you do not have access to": "This document may be shared with more workspace members through a parent document or collection you do not have access to", "Access inherited from collection": "Access inherited from collection", "{{ userName }} was removed from the document": "{{ userName }} was removed from the document", "Could not remove user": "<PERSON><PERSON><PERSON><PERSON> thể xóa người dùng", "Permissions for {{ userName }} updated": "Permissions for {{ userName }} updated", "Could not update user": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật người dùng", "Has access through <2>parent</2>": "Has access through <2>parent</2>", "Suspended": "<PERSON><PERSON> tạm ng<PERSON>ng", "Invited": "Đ<PERSON> mời", "Active <1></1> ago": "<PERSON><PERSON><PERSON> đ<PERSON> <1></1> tr<PERSON><PERSON><PERSON> kia", "Never signed in": "<PERSON><PERSON><PERSON> bao giờ đăng nh<PERSON>p", "Leave": "<PERSON><PERSON><PERSON><PERSON>", "Only lowercase letters, digits and dashes allowed": "Chỉ cho phép chữ thường, chữ số và dấu gạch ngang", "Sorry, this link has already been used": "<PERSON><PERSON> lỗi, liên kết này đã được sử dụng", "Public link copied to clipboard": "Public link copied to clipboard", "Web": "Web", "Anyone with the link can access because the parent document, <2>{{documentTitle}}</2>, is shared": "Anyone with the link can access because the parent document, <2>{{documentTitle}}</2>, is shared", "Allow anyone with the link to access": "Allow anyone with the link to access", "Publish to internet": "<PERSON><PERSON><PERSON> tải lên internet", "Search engine indexing": "Search engine indexing", "Disable this setting to discourage search engines from indexing the page": "Disable this setting to discourage search engines from indexing the page", "Show last modified": "Show last modified", "Display the last modified timestamp on the shared page": "Display the last modified timestamp on the shared page", "Nested documents are not shared on the web. Toggle sharing to enable access, this will be the default behavior in the future": "Nested documents are not shared on the web. Toggle sharing to enable access, this will be the default behavior in the future", "{{ userName }} was added to the document": "{{ userName }} was added to the document", "{{ count }} people added to the document": "{{ count }} people added to the document", "{{ count }} people added to the document_plural": "{{ count }} people added to the document", "{{ count }} groups added to the document": "{{ count }} groups added to the document", "{{ count }} groups added to the document_plural": "{{ count }} groups added to the document", "Logo": "Logo", "Archived collections": "Archived collections", "New doc": "<PERSON><PERSON><PERSON> li<PERSON>u mới", "Empty": "<PERSON><PERSON><PERSON><PERSON>", "Collapse": "<PERSON>hu nhỏ", "Expand": "Mở rộng", "Document not supported – try Markdown, Plain text, HTML, or Word": "<PERSON><PERSON><PERSON> liệu không được hỗ trợ - hãy thử Markdown, Plain text, HTML hoặc Word", "Go back": "Quay lại", "Go forward": "<PERSON><PERSON>", "Could not load shared documents": "Could not load shared documents", "Shared with me": "Shared with me", "Show more": "<PERSON><PERSON><PERSON> thị thêm", "Could not load starred documents": "Could not load starred documents", "Starred": "<PERSON><PERSON><PERSON><PERSON> gắn sao", "Up to date": "<PERSON><PERSON> cập nh<PERSON>t", "{{ releasesBehind }} versions behind": "{{ <PERSON><PERSON><PERSON><PERSON> }} phi<PERSON>n bản phía sau", "{{ releasesBehind }} versions behind_plural": "{{ <PERSON><PERSON><PERSON><PERSON> }} phi<PERSON>n bản phía sau", "Change permissions?": "Change permissions?", "{{ documentName }} cannot be moved within {{ parentDocumentName }}": "{{ documentName }} cannot be moved within {{ parentDocumentName }}", "You can't reorder documents in an alphabetically sorted collection": "Bạn không thể sắp xếp lại các tài liệu trong bộ sưu tập được sắp xếp theo thứ tự bảng chữ cái", "The {{ documentName }} cannot be moved here": "The {{ documentName }} cannot be moved here", "Return to App": "Quay lại ứng dụng", "Installation": "Cài đặt", "Unstar document": "Unstar document", "Star document": "Star document", "Template created, go ahead and customize it": "Đã tạo mẫu, hã<PERSON> tiếp tục và tùy chỉnh nó", "Creating a template from <em>{{titleWithDefault}}</em> is a non-destructive action – we'll make a copy of the document and turn it into a template that can be used as a starting point for new documents.": "<PERSON><PERSON> tạo mẫu từ <em>{{titleWithDefault}}</em> là một hành động không phá hủy - chúng tôi sẽ tạo một bản sao của tài liệu và biến nó thành một mẫu có thể được sử dụng làm điểm bắt đầu cho các tài liệu mới.", "Enable other members to use the template immediately": "Enable other members to use the template immediately", "Location": "Location", "Admins can manage the workspace and access billing.": "Admins can manage the workspace and access billing.", "Editors can create, edit, and comment on documents.": "Editors can create, edit, and comment on documents.", "Viewers can only view and comment on documents.": "Viewers can only view and comment on documents.", "Are you sure you want to make {{ userName }} a {{ role }}?": "Are you sure you want to make {{ userName }} a {{ role }}?", "I understand, delete": "<PERSON><PERSON><PERSON>, x<PERSON><PERSON>", "Are you sure you want to permanently delete {{ userName }}? This operation is unrecoverable, consider suspending the user instead.": "Bạn có chắc muốn xóa vĩnh viễn {{ userName }}? Thao tác này không thể khôi phục đư<PERSON>, h<PERSON><PERSON> cân nhắc treo tài khoản đó.", "Are you sure you want to suspend {{ userName }}? Suspended users will be prevented from logging in.": "Bạn có chắc chắn muốn tạm ngưng tài khoản này không? Người dùng bị treo sẽ bị ngăn đăng nhập.", "New name": "<PERSON><PERSON><PERSON> m<PERSON>i", "Name can't be empty": "<PERSON><PERSON><PERSON><PERSON> đư<PERSON><PERSON> để trống tên", "Check your email to verify the new address.": "Check your email to verify the new address.", "The email will be changed once verified.": "The email will be changed once verified.", "You will receive an email to verify your new address. It must be unique in the workspace.": "You will receive an email to verify your new address. It must be unique in the workspace.", "A confirmation email will be sent to the new address before it is changed.": "A confirmation email will be sent to the new address before it is changed.", "New email": "New email", "Email can't be empty": "Email can't be empty", "Your import completed": "Your import completed", "Previous match": "Previous match", "Next match": "Next match", "Find and replace": "<PERSON><PERSON><PERSON> kiếm và thay thế", "Find": "<PERSON><PERSON><PERSON>", "Match case": "Match case", "Enable regex": "Enable regex", "Replace options": "Replace options", "Replacement": "Replacement", "Replace": "<PERSON>hay thế", "Replace all": "<PERSON><PERSON> thế tất cả", "Profile picture": "Ảnh đại diện", "Create a new doc": "<PERSON><PERSON>o một tài liệu mới", "{{ userName }} won't be notified, as they do not have access to this document": "{{ userName }} won't be notified, as they do not have access to this document", "Keep as link": "Keep as link", "Mention": "Mention", "Embed": "Embed", "Add column after": "<PERSON><PERSON><PERSON><PERSON> cột phía sau", "Add column before": "<PERSON><PERSON><PERSON><PERSON> cột phía trước", "Add row after": "<PERSON><PERSON><PERSON><PERSON> hàng bên <PERSON>", "Add row before": "<PERSON><PERSON><PERSON><PERSON> hàng bên trên", "Align center": "<PERSON><PERSON><PERSON>", "Align left": "<PERSON><PERSON><PERSON> t<PERSON>", "Align right": "<PERSON><PERSON><PERSON>", "Default width": "<PERSON><PERSON><PERSON> rộng mặc định", "Full width": "<PERSON><PERSON>ng toàn bộ", "Bulleted list": "<PERSON><PERSON> s<PERSON>ch dấu đầu dòng", "Todo list": "<PERSON><PERSON> s<PERSON>ch tác vụ", "Code block": "Khối Code", "Copied to clipboard": "Chép vào bộ nhớ tạm", "Code": "Code", "Comment": "<PERSON><PERSON><PERSON> lu<PERSON>", "Create link": "Tạo đường link", "Sorry, an error occurred creating the link": "<PERSON><PERSON> lỗi, đã xảy ra lỗi khi tạo đường link", "Create a new child doc": "Create a new child doc", "Delete table": "<PERSON><PERSON><PERSON>", "Delete file": "Delete file", "Width x Height": "Width x Height", "Download file": "Download file", "Replace file": "Replace file", "Delete image": "<PERSON><PERSON><PERSON>", "Download image": "<PERSON><PERSON><PERSON>", "Replace image": "<PERSON><PERSON> thế <PERSON>nh", "Italic": "<PERSON><PERSON><PERSON><PERSON>", "Sorry, that link won’t work for this embed type": "<PERSON><PERSON> lỗi, liên kết đó sẽ không hoạt động cho loại nhúng này", "File attachment": "<PERSON><PERSON><PERSON> li<PERSON> đ<PERSON>", "Enter a link": "Enter a link", "Big heading": "Ti<PERSON>u đề lớn", "Medium heading": "<PERSON>i<PERSON><PERSON> đề vừa", "Small heading": "Ti<PERSON>u đề nhỏ", "Extra small heading": "Extra small heading", "Heading": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "Divider": "<PERSON><PERSON> c<PERSON>", "Image": "Ảnh", "Sorry, an error occurred uploading the file": "<PERSON><PERSON> xảy ra lỗi khi đang tải lên ảnh", "Write a caption": "<PERSON><PERSON><PERSON><PERSON> chú thích", "Info": "Thông tin", "Info notice": "<PERSON>hông báo thông tin", "Link": "Đường link", "Highlight": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>i bật", "Type '/' to insert": "G<PERSON> '/' để chèn", "Keep typing to filter": "<PERSON><PERSON><PERSON><PERSON> tục nhập để lọc", "Open link": "Mở đường Link", "Go to link": "<PERSON><PERSON> đến liên kết", "Sorry, that type of link is not supported": "<PERSON><PERSON> lỗi, lo<PERSON>i liên kết đó không được hỗ trợ", "Ordered list": "<PERSON><PERSON> s<PERSON>ch thứ tự", "Page break": "<PERSON><PERSON><PERSON> trang", "Paste a link": "<PERSON><PERSON> đ<PERSON>ng link", "Paste a {{service}} link…": "<PERSON><PERSON> {{service}}…", "Placeholder": "<PERSON><PERSON><PERSON><PERSON> chỗ", "Quote": "<PERSON><PERSON><PERSON><PERSON> dẫn", "Remove link": "Gỡ bỏ đường Link", "Search or paste a link": "<PERSON><PERSON><PERSON> kiếm hoặc dán một đường Link", "Strikethrough": "<PERSON><PERSON><PERSON> ng<PERSON>", "Bold": "Đậm", "Subheading": "Ti<PERSON><PERSON> đề phụ", "Sort ascending": "Sort ascending", "Sort descending": "Sort descending", "Table": "<PERSON><PERSON><PERSON>", "Export as CSV": "Export as CSV", "Toggle header": "Toggle header", "Math inline (LaTeX)": "<PERSON><PERSON> (LaTeX)", "Math block (LaTeX)": "<PERSON><PERSON><PERSON><PERSON> (LaTeX)", "Merge cells": "Merge cells", "Split cell": "Split cell", "Tip": "Mẹo", "Tip notice": "<PERSON>h<PERSON>ng báo mẹo", "Warning": "<PERSON><PERSON><PERSON> b<PERSON>o", "Warning notice": "<PERSON><PERSON><PERSON><PERSON> báo cảnh báo", "Success": "<PERSON><PERSON><PERSON> t<PERSON>t", "Success notice": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>o hoàn tất", "Current date": "<PERSON><PERSON><PERSON> t<PERSON>i", "Current time": "<PERSON><PERSON><PERSON><PERSON> gian hiện tại", "Current date and time": "<PERSON><PERSON><PERSON> g<PERSON> hiện tại", "Indent": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "Outdent": "<PERSON><PERSON><PERSON> l<PERSON>", "Video": "Video", "None": "None", "Could not import file": "<PERSON><PERSON><PERSON><PERSON> thể nhập file", "Unsubscribed from document": "Unsubscribed from document", "Unsubscribed from collection": "Unsubscribed from collection", "Account": "<PERSON><PERSON><PERSON>", "API & Apps": "API & Apps", "Details": "<PERSON> ti<PERSON>", "Security": "<PERSON><PERSON><PERSON>", "Features": "<PERSON><PERSON><PERSON>", "Members": "<PERSON><PERSON><PERSON><PERSON>", "Groups": "Nhóm", "API Keys": "API Keys", "Applications": "Applications", "Shared Links": "Chia sẻ link", "Import": "<PERSON><PERSON><PERSON><PERSON>", "Install": "Install", "Integrations": "<PERSON><PERSON><PERSON>", "Revoke token": "<PERSON><PERSON> hồi token", "Revoke": "<PERSON><PERSON> h<PERSON>", "Show path to document": "Hiển thị đường dẫn đến tài liệu", "Path to document": "Đường dẫn đến tài liệu", "Group member options": "<PERSON><PERSON><PERSON> chọn thành viên nhóm", "Export collection": "<PERSON><PERSON><PERSON> xu<PERSON><PERSON> bộ sưu tập", "Rename": "<PERSON><PERSON><PERSON> tên", "Sort in sidebar": "<PERSON><PERSON><PERSON> xếp trong sidebar", "A-Z sort": "A-Z sort", "Z-A sort": "Z-A sort", "Manual sort": "<PERSON><PERSON><PERSON> xếp thủ công", "Comment options": "<PERSON><PERSON><PERSON> ch<PERSON> bình luận", "Show document menu": "Show document menu", "{{ documentName }} restored": "{{ documentName }} restored", "Document options": "<PERSON><PERSON><PERSON> chọn tài li<PERSON>u", "Choose a collection": "<PERSON><PERSON><PERSON>", "Subscription inherited from collection": "Subscription inherited from collection", "Apply template": "Apply template", "Enable embeds": "<PERSON><PERSON><PERSON> t<PERSON> n<PERSON>ng nh<PERSON>g", "Export options": "<PERSON><PERSON> chọn xu<PERSON>t", "Group members": "<PERSON><PERSON><PERSON><PERSON> viên trong nhóm", "Edit group": "Chỉnh sửa nhóm", "Delete group": "Xóa nhóm", "Group options": "<PERSON><PERSON><PERSON>", "Cancel": "Hủy bỏ", "Import menu options": "Import menu options", "Member options": "<PERSON><PERSON><PERSON> chọn thành viên", "New document in <em>{{ collectionName }}</em>": "<PERSON><PERSON><PERSON> liệu mới trong <em>{{ collectionName }}</em>", "New child document": "<PERSON><PERSON><PERSON> liệu con mới", "Save in workspace": "Save in workspace", "Notification settings": "Notification settings", "Revoke {{ appName }}": "Revoke {{ appName }}", "Revoking": "<PERSON><PERSON> thu hồi", "Are you sure you want to revoke access?": "Are you sure you want to revoke access?", "Delete app": "Delete app", "Revision options": "<PERSON><PERSON><PERSON> ch<PERSON>n sửa đổi", "Share link revoked": "<PERSON><PERSON> thu hồi Link chia sẻ", "Share link copied": "Đã sao chép Link chia sẻ", "Share options": "<PERSON><PERSON><PERSON> ch<PERSON>n chia sẻ", "Go to document": "<PERSON><PERSON> đến tài liệu", "Revoke link": "Gỡ bỏ Link", "Contents": "<PERSON><PERSON><PERSON>", "Headings you add to the document will appear here": "<PERSON><PERSON><PERSON> tiêu đề bạn thêm vào tài liệu sẽ xuất hiện ở đây", "Table of contents": "<PERSON><PERSON><PERSON>", "Change name": "<PERSON><PERSON><PERSON> tên", "Change email": "Change email", "Suspend user": "<PERSON><PERSON><PERSON> t<PERSON><PERSON>", "An error occurred while sending the invite": "<PERSON><PERSON> xảy ra lỗi khi gửi lời mời", "User options": "<PERSON><PERSON><PERSON> ch<PERSON>n ng<PERSON><PERSON> dùng", "Change role": "Change role", "Resend invite": "<PERSON><PERSON><PERSON> lại lời mời", "Revoke invite": "<PERSON><PERSON> hồi lời mời", "Activate user": "Activate user", "template": "template", "document": "t<PERSON><PERSON> li<PERSON>u", "published": "published", "edited": "đã chỉnh sửa", "created the collection": "created the collection", "mentioned you in": "mentioned you in", "left a comment on": "left a comment on", "resolved a comment on": "resolved a comment on", "shared": "<PERSON><PERSON><PERSON><PERSON> chia sẻ", "invited you to": "invited you to", "Choose a date": "Choose a date", "API key created. Please copy the value now as it will not be shown again.": "API key created. Please copy the value now as it will not be shown again.", "Scopes": "<PERSON><PERSON><PERSON>", "Space-separated scopes restrict the access of this API key to specific parts of the API. Leave blank for full access": "Space-separated scopes restrict the access of this API key to specific parts of the API. Leave blank for full access", "Expiration": "Expiration", "Never expires": "Never expires", "7 days": "7 days", "30 days": "30 days", "60 days": "60 days", "90 days": "90 days", "Custom": "Custom", "No expiration": "No expiration", "The document archive is empty at the moment.": "<PERSON><PERSON> lưu trữ tài liệu hiện đang trống.", "Collection menu": "<PERSON><PERSON> bộ s<PERSON>u tập", "Drop documents to import": "<PERSON><PERSON><PERSON> tài liệu để nhập", "<em>{{ collectionName }}</em> doesn’t contain any\n                    documents yet.": "<em>{{ collectionName }}</em> chưa có\n                    tài liệu nào.", "{{ usersCount }} users and {{ groupsCount }} groups with access": "{{ usersCount }} ngư<PERSON>i dùng và {{ groupsCount }} nhóm có quyền truy cập", "{{ usersCount }} users and {{ groupsCount }} groups with access_plural": "{{ usersCount }} ngư<PERSON>i dùng và {{ groupsCount }} nhóm có quyền truy cập", "{{ usersCount }} users and a group have access": "{{ usersCount }} người dùng và một nhóm có quyền truy cập", "{{ usersCount }} users and a group have access_plural": "{{ usersCount }} người dùng và một nhóm có quyền truy cập", "{{ usersCount }} users with access": "{{ usersCount }} ng<PERSON><PERSON>i dùng có quyền truy cập", "{{ usersCount }} users with access_plural": "{{ usersCount }} ng<PERSON><PERSON>i dùng có quyền truy cập", "{{ groupsCount }} groups with access": "{{ groupsCount }} nh<PERSON>m có quyền truy cập", "{{ groupsCount }} groups with access_plural": "{{ groupsCount }} nh<PERSON>m có quyền truy cập", "Archived by {{userName}}": "<PERSON><PERSON><PERSON><PERSON> lưu trữ bởi {{userName}}", "Sorry, an error occurred saving the collection": "<PERSON><PERSON> lỗi, đã xảy ra lỗi khi lưu bộ sưu tập", "Add a description": "<PERSON><PERSON><PERSON><PERSON> mô tả", "Share": "<PERSON><PERSON> sẻ", "Overview": "Overview", "Recently updated": "<PERSON><PERSON><PERSON><PERSON> cập nhật gần đây", "Recently published": "<PERSON><PERSON><PERSON> tải gần đây", "Least recently updated": "<PERSON><PERSON><PERSON> nhật ít nhất gần đây", "A–Z": "A – Z", "Signing in": "<PERSON><PERSON> đ<PERSON>ng <PERSON>h<PERSON>p", "You can safely close this window once the Outline desktop app has opened": "Bạn có thể đóng cửa sổ này một cách an toàn sau khi ứng dụng Outline trên máy tính đã mở", "Error creating comment": "Lỗi khi tạo bình luận", "Add a comment": "<PERSON><PERSON><PERSON><PERSON> một bình luận", "Add a reply": "<PERSON><PERSON><PERSON><PERSON> một trả lời", "Reply": "<PERSON><PERSON><PERSON> lờ<PERSON>", "Post": "<PERSON><PERSON><PERSON>", "Upload image": "Upload image", "No resolved comments": "No resolved comments", "No comments yet": "<PERSON><PERSON><PERSON> c<PERSON> bình luận", "New comments": "New comments", "Most recent": "Most recent", "Order in doc": "Order in doc", "Resolved": "Resolved", "Sort comments": "Sort comments", "Show {{ count }} reply": "Show {{ count }} reply", "Show {{ count }} reply_plural": "Show {{ count }} replies", "Error updating comment": "Lỗi khi cập nhật nhận xét", "Document is too large": "Tài liệu quá lớn", "This document has reached the maximum size and can no longer be edited": "Tài liệu này đã đạt đến kích thước tối đa và không thể chỉnh sửa được nữa", "Authentication failed": "<PERSON><PERSON><PERSON> thực không thành công", "Please try logging out and back in again": "<PERSON><PERSON> lòng thử đăng xuất và đăng nhập lại", "Authorization failed": "<PERSON><PERSON><PERSON> thực không thành công", "You may have lost access to this document, try reloading": "<PERSON><PERSON>n có thể đã mất quyền truy cập vào tài li<PERSON>u này, hãy thử tải lại", "Too many users connected to document": "<PERSON>u<PERSON> nhiều người dùng kết nối với tài liệu", "Your edits will sync once other users leave the document": "Các chỉnh sửa của bạn sẽ được đồng bộ hóa sau khi người dùng khác rời khỏi tài liệu", "Server connection lost": "<PERSON><PERSON><PERSON>", "Edits you make will sync once you’re online": "Các chỉnh sửa bạn thực hiện sẽ đồng bộ hóa khi bạn Online", "Document restored": "<PERSON><PERSON><PERSON> li<PERSON><PERSON><PERSON><PERSON> hồi", "Images are still uploading.\nAre you sure you want to discard them?": "Hình ảnh vẫn đang tải lên.\nBạn có chắc chắn muốn loại bỏ chúng không?", "{{ count }} comment": "{{ count }} b<PERSON><PERSON> lu<PERSON>n", "{{ count }} comment_plural": "{{ count }} b<PERSON><PERSON> lu<PERSON>n", "Viewed by": "<PERSON><PERSON><PERSON><PERSON> xem bởi", "only you": "chỉ bạn", "person": "cá nhân", "people": "m<PERSON>i ng<PERSON>i", "Last updated": "<PERSON><PERSON><PERSON> nh<PERSON>t gần nhất", "Type '/' to insert, or start writing…": "G<PERSON> '/' để chèn hoặc bắt đầu viết…", "Hide contents": "Ẩn nội dung", "Show contents": "<PERSON><PERSON><PERSON> thị nội dung", "available when headings are added": "available when headings are added", "Edit {{noun}}": "Chỉnh sửa {{noun}}", "Switch to dark": "<PERSON><PERSON><PERSON><PERSON> sang Dark", "Switch to light": "<PERSON><PERSON><PERSON><PERSON> sang Light", "Archived": "<PERSON><PERSON> lưu trữ", "Save draft": "Save draft", "Done editing": "Done editing", "Restore version": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON><PERSON> phiên bản", "No history yet": "<PERSON><PERSON><PERSON> c<PERSON> lịch sử", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Imported from {{ source }}": "Imported from {{ source }}", "Stats": "<PERSON><PERSON><PERSON><PERSON>", "{{ count }} minute read": "{{ count }} phút để đọc", "{{ count }} minute read_plural": "{{ count }} phút để đọc", "{{ count }} words": "{{ count }} từ", "{{ count }} words_plural": "{{ count }} từ", "{{ count }} characters": "{{ count }} ký tự", "{{ count }} characters_plural": "{{ count }} ký tự", "{{ number }} emoji": "{{ number }} bi<PERSON><PERSON> t<PERSON> cảm x<PERSON>c", "No text selected": "<PERSON><PERSON><PERSON><PERSON> có văn bản n<PERSON>o đ<PERSON><PERSON><PERSON> chọn", "{{ count }} words selected": "{{ count }} từ đượ<PERSON> chọn", "{{ count }} words selected_plural": "{{ count }} từ đượ<PERSON> chọn", "{{ count }} characters selected": "{{ count }} ký tự đượ<PERSON> ch<PERSON>n", "{{ count }} characters selected_plural": "{{ count }} ký tự đượ<PERSON> ch<PERSON>n", "Contributors": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> g<PERSON>p", "Created": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "Creator": "Tạo bởi", "Last edited": "<PERSON><PERSON><PERSON> l<PERSON>n <PERSON>", "Previously edited": "Chỉnh sửa trước đó", "No one else has viewed yet": "Chưa có ai khác xem", "Viewed {{ count }} times by {{ teamMembers }} people": "<PERSON><PERSON> xem {{ count }} lần bởi {{ teamMembers }} ng<PERSON><PERSON>i", "Viewed {{ count }} times by {{ teamMembers }} people_plural": "<PERSON><PERSON> xem {{ count }} lần bởi {{ teamMembers }} ng<PERSON><PERSON>i", "Viewer insights are disabled.": "Viewer insights are disabled.", "Sorry, the last change could not be persisted – please reload the page": "<PERSON><PERSON> lỗi, kh<PERSON><PERSON> thể tiếp tục thay đổi cuối cùng - vui lòng tải lại trang", "{{ count }} days": "{{ count }} day", "{{ count }} days_plural": "{{ count }} days", "This template will be permanently deleted in <2></2> unless restored.": "Mẫu này sẽ bị xóa vĩnh viễn sau <2></2> trừ khi được khôi phục.", "This document will be permanently deleted in <2></2> unless restored.": "T<PERSON><PERSON> liệu này sẽ bị xóa vĩnh viễn sau <2></2> trừ khi được khôi phục.", "Highlight some text and use the <1></1> control to add placeholders that can be filled out when creating new documents": "<PERSON><PERSON><PERSON> dấu văn bản và dùng <1></1> để thêm khoảng giữ chỗ mà sau này có thể điền thông tin khi tạo tài liệu mới", "You’re editing a template": "Bạn đang chỉnh sửa mẫu", "Deleted by {{userName}}": "Đã xóa bởi {{userName}}", "Observing {{ userName }}": "<PERSON>uan s<PERSON>t {{ userName }}", "Backlinks": "<PERSON><PERSON><PERSON>", "Close": "Đ<PERSON><PERSON>", "This document is large which may affect performance": "This document is large which may affect performance", "{{ teamName }} is using {{ appName }} to share documents, please login to continue.": "{{ teamName }} đang sử dụng {{ appName }} để chia sẻ tài liệu, vui lòng đăng nhập để tiếp tục.", "Are you sure you want to delete the <em>{{ documentTitle }}</em> template?": "Bạn có chắc chắn muốn xóa mẫu <em>{{ documentTitle }}</em> không?", "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history</em>.": "Bạn có chắc chắn không? Xóa văn bản <em>{{ documentTitle }}</em> sẽ xóa tất cả lịch sử của nó</em>.", "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history and <em>{{ any }} nested document</em>.": "Bạn có chắc chắn về điều đó không? Xóa tài liệu <em>{{ documentTitle }}</em> sẽ xóa tất cả lịch sử của nó và <em>{{ any }} tài liệu lồng nhau</em>.", "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history and <em>{{ any }} nested document</em>._plural": "Bạn có chắc chắn về điều đó không? Xóa tài liệu <em>{{ documentTitle }}</em> sẽ xóa tất cả lịch sử của nó và <em>{{ any }} tài liệu lồng nhau</em>.", "If you’d like the option of referencing or restoring the {{noun}} in the future, consider archiving it instead.": "<PERSON><PERSON>u bạn muốn tùy chọn tham chiếu hoặc khôi phục số {{noun}} trong tương lai, hãy xem xét lưu trữ nó thay thế.", "Select a location to move": "<PERSON><PERSON><PERSON> vị trí để di chuyển", "Document moved": "<PERSON>ài liệu đã đư<PERSON><PERSON> di chuyển", "Couldn’t move the document, try again?": "<PERSON><PERSON><PERSON>ng thể di chuyển tài li<PERSON>u, hãy thử lại?", "Move to <em>{{ location }}</em>": "<PERSON> chuyển đến <em>{{ location }}</em>", "Couldn’t create the document, try again?": "<PERSON><PERSON><PERSON><PERSON> thể tạo tài li<PERSON>, h<PERSON><PERSON> thử lại?", "Document permanently deleted": "<PERSON><PERSON><PERSON> liệu đã bị xóa vĩnh viễn", "Are you sure you want to permanently delete the <em>{{ documentTitle }}</em> document? This action is immediate and cannot be undone.": "Bạn có chắc chắn muốn xóa vĩnh viễn tài liệu <em>{{ documentTitle }}</em> ? Hành động này là ngay lập tức và không thể hoàn tác.", "Select a location to publish": "<PERSON><PERSON><PERSON> một vị trí để xuất bản", "Document published": "<PERSON><PERSON><PERSON> li<PERSON>u đ<PERSON><PERSON><PERSON> công khai", "Couldn’t publish the document, try again?": "<PERSON><PERSON><PERSON><PERSON> thể xuất bản tài li<PERSON>, h<PERSON><PERSON> thử lại?", "Publish in <em>{{ location }}</em>": "<PERSON><PERSON><PERSON> bản trong <em>{{ location }}</em>", "Search documents": "<PERSON><PERSON><PERSON> kiếm tài liệu", "No documents found for your filters.": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tài liệu nào cho bộ lọc của bạn.", "You’ve not got any drafts at the moment.": "Bạn không có bất kỳ bản nháp nào vào lúc này.", "Payment Required": "Payment Required", "No access to this doc": "No access to this doc", "It doesn’t look like you have permission to access this document.": "It doesn’t look like you have permission to access this document.", "Please request access from the document owner.": "Please request access from the document owner.", "Not found": "Not found", "The page you’re looking for cannot be found. It might have been deleted or the link is incorrect.": "The page you’re looking for cannot be found. It might have been deleted or the link is incorrect.", "Offline": "Offline", "We were unable to load the document while offline.": "<PERSON><PERSON><PERSON> tôi không thể tải tài liệu khi ngoại tuyến.", "Your account has been suspended": "<PERSON><PERSON><PERSON> k<PERSON>n của bạn đã bị vô hiệu hóa", "Warning Sign": "<PERSON><PERSON><PERSON> hi<PERSON> cảnh b<PERSON>o", "A workspace admin (<em>{{ suspendedContactEmail }}</em>) has suspended your account. To re-activate your account, please reach out to them directly.": "A workspace admin (<em>{{ suspendedContactEmail }}</em>) has suspended your account. To re-activate your account, please reach out to them directly.", "Created by me": "<PERSON><PERSON><PERSON><PERSON> tạo bởi tôi", "Weird, this shouldn’t ever be empty": "<PERSON><PERSON><PERSON> nà<PERSON> không bao giờ được để trống", "You haven’t created any documents yet": "Bạn chưa tạo bất kỳ tài liệu nào", "Documents you’ve recently viewed will be here for easy access": "<PERSON><PERSON><PERSON> tài liệu bạn đã xem gần đây sẽ ở đây để dễ dàng truy cập", "We sent out your invites!": "<PERSON>úng tôi đã gửi lời mời của bạn!", "Those email addresses are already invited": "Những địa chỉ email đó đã được mời", "Sorry, you can only send {{MAX_INVITES}} invites at a time": "Xin lỗi, bạn chỉ có thể gửi {{MAX_INVITES}} lời mời cùng một lúc", "Invited {{roleName}} will receive access to": "Invited {{roleName}} will receive access to", "{{collectionCount}} collections": "{{collectionCount}} collections", "Admin": "<PERSON><PERSON><PERSON><PERSON> trị viên", "Can manage all workspace settings": "Can manage all workspace settings", "Can create, edit, and delete documents": "Can create, edit, and delete documents", "Can view and comment": "Can view and comment", "Invite people to join your workspace. They can sign in with {{signinMethods}} or use their email address.": "Invite people to join your workspace. They can sign in with {{signinMethods}} or use their email address.", "Invite members to join your workspace. They will need to sign in with {{signinMethods}}.": "<PERSON><PERSON><PERSON> các thành viên tham gia không gian làm việc của bạn. Họ sẽ cần đăng nhập bằng {{signinMethods}}.", "As an admin you can also <2>enable email sign-in</2>.": "<PERSON><PERSON><PERSON> tư cách là quản trị viên, bạ<PERSON> cũng có thể <2> bật đ<PERSON><PERSON> nh<PERSON>p email</2>.", "Invite as": "Invite as", "Role": "<PERSON>ai trò", "Email": "Email", "Add another": "<PERSON><PERSON><PERSON><PERSON>", "Inviting": "<PERSON><PERSON><PERSON>", "Send Invites": "<PERSON><PERSON><PERSON> mời", "Open command menu": "Mở menu lệnh", "Forward": "Forward", "Edit current document": "Chỉnh sửa tài liệu hiện tại", "Move current document": "<PERSON> chuyển tài liệu hiện tại", "Open document history": "Mở lịch sử tài liệu", "Jump to search": "<PERSON><PERSON><PERSON><PERSON> đến tìm kiếm", "Jump to home": "Chuyển đến Home", "Focus search input": "Tập trung đầu vào tìm kiếm", "Open this guide": "Mở hướng dẫn này", "Enter": "Enter", "Publish document and exit": "<PERSON><PERSON><PERSON> tải tài liệu và thoát", "Save document": "<PERSON><PERSON><PERSON> tài li<PERSON>u", "Cancel editing": "<PERSON><PERSON>y chỉnh sửa", "Collaboration": "Collaboration", "Formatting": "<PERSON><PERSON><PERSON> d<PERSON>ng", "Paragraph": "<PERSON><PERSON><PERSON><PERSON> văn", "Large header": "Ti<PERSON>u đề lớn", "Medium header": "Ti<PERSON>u đề trung bình", "Small header": "Ti<PERSON>u đề nhỏ", "Underline": "<PERSON><PERSON><PERSON>", "Undo": "<PERSON><PERSON><PERSON>", "Redo": "<PERSON><PERSON><PERSON>", "Move block up": "Move block up", "Move block down": "Move block down", "Lists": "<PERSON><PERSON>", "Toggle task list item": "Toggle task list item", "Tab": "Tab", "Indent list item": "<PERSON><PERSON><PERSON> danh s<PERSON>ch thụt l<PERSON>", "Outdent list item": "<PERSON><PERSON><PERSON> danh s<PERSON>ch cũ", "Move list item up": "<PERSON> chuyển danh sách lên trên", "Move list item down": "<PERSON> chuyển danh sách xuống dư<PERSON>i", "Tables": "Tables", "Insert row": "Insert row", "Next cell": "Next cell", "Previous cell": "Previous cell", "Space": "Space", "Numbered list": "<PERSON><PERSON> s<PERSON>ch đ<PERSON><PERSON><PERSON> đ<PERSON>h số", "Blockquote": "<PERSON><PERSON><PERSON><PERSON> dẫn", "Horizontal divider": "<PERSON><PERSON><PERSON> ph<PERSON> cách ngang", "LaTeX block": "Khối LaTeX", "Inline code": "<PERSON><PERSON> nội tuyến", "Inline LaTeX": "LaTeX nội tuyến", "Triggers": "Triggers", "Mention users and more": "Mention users and more", "Emoji": "<PERSON><PERSON><PERSON>", "Insert block": "Insert block", "Sign In": "<PERSON><PERSON><PERSON>", "Continue with Email": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> với email", "Continue with {{ authProviderName }}": "<PERSON><PERSON><PERSON><PERSON> tụ<PERSON> với {{ authProviderName }}", "Back to home": "Quay lại trang chủ", "The workspace could not be found": "The workspace could not be found", "To continue, enter your workspace’s subdomain.": "To continue, enter your workspace’s subdomain.", "subdomain": "tên miền phụ", "Continue": "<PERSON><PERSON><PERSON><PERSON>", "The domain associated with your email address has not been allowed for this workspace.": "<PERSON><PERSON><PERSON>n email c<PERSON><PERSON> bạn không đ<PERSON><PERSON><PERSON> chấp nhận cho không gian làm vi<PERSON><PERSON> này.", "Unable to sign-in. Please navigate to your workspace's custom URL, then try to sign-in again.<1></1>If you were invited to a workspace, you will find a link to it in the invite email.": "Unable to sign-in. Please navigate to your workspace's custom URL, then try to sign-in again.<1></1>If you were invited to a workspace, you will find a link to it in the invite email.", "Sorry, a new account cannot be created with a personal Gmail address.<1></1>Please use a Google Workspaces account instead.": "Sorry, a new account cannot be created with a personal Gmail address.<1></1>Please use a Google Workspaces account instead.", "The workspace associated with your user is scheduled for deletion and cannot be accessed at this time.": "The workspace associated with your user is scheduled for deletion and cannot be accessed at this time.", "The workspace you authenticated with is not authorized on this installation. Try another?": "The workspace you authenticated with is not authorized on this installation. Try another?", "We could not read the user info supplied by your identity provider.": "We could not read the user info supplied by your identity provider.", "Your account uses email sign-in, please sign-in with email to continue.": "Your account uses email sign-in, please sign-in with email to continue.", "An email sign-in link was recently sent, please check your inbox or try again in a few minutes.": "An email sign-in link was recently sent, please check your inbox or try again in a few minutes.", "Authentication failed – we were unable to sign you in at this time. Please try again.": "Authentication failed – we were unable to sign you in at this time. Please try again.", "Authentication failed – you do not have permission to access this workspace.": "Authentication failed – you do not have permission to access this workspace.", "Sorry, it looks like that sign-in link is no longer valid, please try requesting another.": "Sorry, it looks like that sign-in link is no longer valid, please try requesting another.", "Your account has been suspended. To re-activate your account, please contact a workspace admin.": "Your account has been suspended. To re-activate your account, please contact a workspace admin.", "This workspace has been suspended. Please contact support to restore access.": "This workspace has been suspended. Please contact support to restore access.", "Authentication failed – this login method was disabled by a workspace admin.": "Authentication failed – this login method was disabled by a workspace admin.", "The workspace you are trying to join requires an invite before you can create an account.<1></1>Please request an invite from your workspace admin and try again.": "The workspace you are trying to join requires an invite before you can create an account.<1></1>Please request an invite from your workspace admin and try again.", "Sorry, an unknown error occurred.": "Sorry, an unknown error occurred.", "Choose a workspace": "Choose a workspace", "Choose an {{ appName }} workspace or login to continue connecting this app": "Choose an {{ appName }} workspace or login to continue connecting this app", "Create workspace": "Create workspace", "Setup your workspace by providing a name and details for admin login. You can change these later.": "Setup your workspace by providing a name and details for admin login. You can change these later.", "Workspace name": "<PERSON><PERSON><PERSON> không gian làm vi<PERSON>c", "Admin name": "Admin name", "Admin email": "Admin email", "Login": "<PERSON><PERSON><PERSON>", "Error": "Lỗi", "Failed to load configuration.": "<PERSON><PERSON><PERSON><PERSON> tải đ<PERSON><PERSON><PERSON> cấu hình.", "Check the network requests and server logs for full details of the error.": "<PERSON><PERSON><PERSON> tra các yêu cầu mạng và nhật ký máy chủ để biết chi tiết đầy đủ về lỗi.", "Custom domain setup": "<PERSON><PERSON><PERSON><PERSON> lập tên miền tùy chỉnh", "Almost there": "<PERSON><PERSON><PERSON> xong", "Your custom domain is successfully pointing at Outline. To complete the setup process please contact support.": "Miền tùy chỉnh của bạn đang trỏ thành công vào Outline. <PERSON><PERSON> hoàn tất quá trình thiết lập, vui lòng liên hệ với bộ phận hỗ trợ.", "Choose workspace": "<PERSON><PERSON><PERSON> không gian làm vi<PERSON>c", "This login method requires choosing your workspace to continue": "<PERSON><PERSON><PERSON><PERSON> thức đăng nhập này yêu cầu chọn không gian làm việc của bạn để tiếp tục", "Check your email": "<PERSON><PERSON><PERSON> tra <PERSON> c<PERSON><PERSON> b<PERSON>n", "A magic sign-in link has been sent to the email <em>{{ emailLinkSentTo }}</em> if an account exists.": "<PERSON><PERSON><PERSON> liên kết đăng nhập kỳ diệu đã được gửi tới email <em>{{ emailLinkSentTo }}</em> nếu có tài kho<PERSON>n.", "Back to login": "Quay lại đăng nh<PERSON>p", "Get started by choosing a sign-in method for your new workspace below…": "B<PERSON>t đầu bằng cách chọn phương thức đăng nhập cho không gian làm việc mới của bạn dưới…", "Login to {{ authProviderName }}": "<PERSON><PERSON><PERSON> nhập vào {{ authProviderName }}", "You signed in with {{ authProviderName }} last time.": "Bạn đã đăng nhập bằng {{ authProviderName }} lần trước.", "Or": "Hoặc", "Already have an account? Go to <1>login</1>.": "Bạn đã có tài khoản? <1><PERSON><PERSON><PERSON> nhập</1>.", "An error occurred": "An error occurred", "The OAuth client could not be found, please check the provided client ID": "The OAuth client could not be found, please check the provided client ID", "The OAuth client could not be loaded, please check the redirect URI is valid": "The OAuth client could not be loaded, please check the redirect URI is valid", "Required OAuth parameters are missing": "Required OAuth parameters are missing", "Authorize": "Authorize", "{{ appName }} wants to access {{ teamName }}": "{{ appName }} wants to access {{ teamName }}", "By <em>{{ developerName }}</em>": "By <em>{{ developerName }}</em>", "{{ appName }} will be able to access your account and perform the following actions": "{{ appName }} will be able to access your account and perform the following actions", "read": "read", "write": "write", "read and write": "read and write", "API keys": "API keys", "attachments": "attachments", "collections": "collections", "comments": "comments", "documents": "documents", "events": "events", "groups": "groups", "integrations": "integrations", "notifications": "notifications", "reactions": "reactions", "pins": "pins", "shares": "shares", "users": "users", "teams": "teams", "workspace": "workspace", "Read all data": "Read all data", "Write all data": "Write all data", "Any collection": "<PERSON><PERSON><PERSON> cả bộ sưu tập", "All time": "All time", "Past day": "<PERSON><PERSON><PERSON> hô<PERSON> qua", "Past week": "<PERSON><PERSON><PERSON> qua", "Past month": "<PERSON><PERSON><PERSON><PERSON>", "Past year": "<PERSON><PERSON><PERSON> qua", "Any time": "<PERSON><PERSON><PERSON> cứ lúc nào", "Remove document filter": "Remove document filter", "Any status": "Any status", "Remove search": "<PERSON><PERSON><PERSON> t<PERSON>m k<PERSON>m", "Any author": "<PERSON><PERSON><PERSON> t<PERSON>c gi<PERSON>", "Search titles only": "Chỉ tìm tiêu đề", "Something went wrong": "Something went wrong", "Please try again or contact support if the problem persists": "Please try again or contact support if the problem persists", "No documents found for your search filters.": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tài liệu nào cho bộ lọc tìm kiếm của bạn.", "Create personal API keys to authenticate with the API and programatically control\n      your workspace's data. For more details see the <em>developer documentation</em>.": "Create personal API keys to authenticate with the API and programatically control\n      your workspace's data. For more details see the <em>developer documentation</em>.", "API keys have been disabled by an admin for your account": "API keys have been disabled by an admin for your account", "Application access": "Application access", "Manage which third-party and internal applications have been granted access to your {{ appName }} account.": "Manage which third-party and internal applications have been granted access to your {{ appName }} account.", "API": "API", "API keys can be used to authenticate with the API and programatically control\n          your workspace's data. For more details see the <em>developer documentation</em>.": "API keys can be used to authenticate with the API and programatically control\n          your workspace's data. For more details see the <em>developer documentation</em>.", "Application published": "Application published", "Application updated": "Application updated", "Client secret rotated": "Client secret rotated", "Rotate secret": "Rotate secret", "Rotating the client secret will invalidate the current secret. Make sure to update any applications using these credentials.": "Rotating the client secret will invalidate the current secret. Make sure to update any applications using these credentials.", "Displayed to users when authorizing": "Displayed to users when authorizing", "Developer information shown to users when authorizing": "Developer information shown to users when authorizing", "Developer name": "Developer name", "Developer URL": "Developer URL", "Allow users from other workspaces to authorize this app": "Allow users from other workspaces to authorize this app", "Credentials": "Credentials", "OAuth client ID": "OAuth client ID", "The public identifier for this app": "The public identifier for this app", "OAuth client secret": "OAuth client secret", "Store this value securely, do not expose it publicly": "Store this value securely, do not expose it publicly", "Where users are redirected after authorizing this app": "Where users are redirected after authorizing this app", "Authorization URL": "Authorization URL", "Where users are redirected to authorize this app": "Where users are redirected to authorize this app", "Applications allow you to build internal or public integrations with Outline and provide secure access via OAuth. For more details see the <em>developer documentation</em>.": "Applications allow you to build internal or public integrations with Outline and provide secure access via OAuth. For more details see the <em>developer documentation</em>.", "by {{ name }}": "by {{ name }}", "Last used": "Last used", "No expiry": "No expiry", "Restricted scope": "Restricted scope", "API key copied to clipboard": "API key copied to clipboard", "Copied": "\u001dĐã sao chép", "Are you sure you want to revoke the {{ tokenName }} token?": "<PERSON>ạn có chắc chắn muốn thu hồi <PERSON> {{ tokenName }} không?", "Disconnect integration": "Disconnect integration", "Connected": "<PERSON><PERSON> kết nối", "Disconnect": "<PERSON><PERSON><PERSON> kế<PERSON> n<PERSON>", "Disconnecting": "Disconnecting", "Allowed domains": "<PERSON><PERSON><PERSON> tên miền cho phép", "The domains which should be allowed to create new accounts using SSO. Changing this setting does not affect existing user accounts.": "<PERSON><PERSON><PERSON> miền được phép tạo tài khoản mới bằng SSO. Việc thay đổi cài đặt này không ảnh hưởng đến các tài khoản người dùng hiện có.", "Remove domain": "<PERSON><PERSON><PERSON> tên <PERSON>n", "Add a domain": "<PERSON><PERSON><PERSON><PERSON> tên <PERSON>n", "Save changes": "<PERSON><PERSON><PERSON> thay đổi", "Please choose a single file to import": "<PERSON><PERSON> lòng chọn một tệp duy nhất để nhập", "Your import is being processed, you can safely leave this page": "<PERSON><PERSON><PERSON> trình nhập của bạn đang được xử lý, bạn có thể rời khỏi trang này một cách an toàn", "File not supported – please upload a valid ZIP file": "Tệ<PERSON> không được hỗ trợ - vui lòng tải lên tệp ZIP hợp lệ", "Set the default permission level for collections created from the import": "Set the default permission level for collections created from the import", "Uploading": "<PERSON><PERSON> t<PERSON> lên", "Start import": "Start import", "Processing": "<PERSON>ang xử lý", "Expired": "<PERSON><PERSON><PERSON>", "Completed": "<PERSON><PERSON><PERSON> th<PERSON>", "Failed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "All collections": "<PERSON><PERSON><PERSON> cả bộ sưu tập", "Import deleted": "Import deleted", "Export deleted": "<PERSON><PERSON><PERSON> xuất đã bị xóa", "Are you sure you want to delete this import?": "Are you sure you want to delete this import?", "Deleting this import will also delete all collections and documents that were created from it. This cannot be undone.": "Deleting this import will also delete all collections and documents that were created from it. This cannot be undone.", "Check server logs for more details.": "Check server logs for more details.", "{{userName}} requested": "{{userName}} đã yêu c<PERSON>u", "Groups are for organizing your team. They work best when centered around a function or a responsibility — Support or Engineering for example.": "Nhóm là để tổ chức nhóm của bạn. Chúng hoạt động tốt nhất khi tập trung vào một chức năng hoặc một trách nhiệm - chẳng hạn như Hỗ trợ hoặc Kỹ thuật.", "You’ll be able to add people to the group next.": "Bạn sẽ có thể thêm mọi người vào nhóm tiếp theo.", "You can edit the name of this group at any time, however doing so too often might confuse your team mates.": "Bạn có thể chỉnh sửa tên của nhóm này bất kỳ lúc nào, tuy nhiên làm như vậy quá thường xuyên có thể gây nhầm lẫn cho các thành viên trong nhóm của bạn.", "Are you sure about that? Deleting the <em>{{groupName}}</em> group will cause its members to lose access to collections and documents that it is associated with.": "Bạn có chắc chắn về điều đó không? Xóa nhóm <em>{{groupName}}</em> sẽ khiến các thành viên của nhóm đó mất quyền truy cập vào các bộ sưu tập và tài liệu được liên kết với nó.", "Add people to {{groupName}}": "<PERSON><PERSON><PERSON><PERSON> ng<PERSON> vào {{groupName}}", "{{userName}} was removed from the group": "{{userName}} đã bị xóa khỏi nhóm", "Add and remove members to the <em>{{groupName}}</em> group. Members of the group will have access to any collections this group has been added to.": "Thêm và xóa thành viên trong nhóm <em>{{groupName}}</em>. <PERSON><PERSON><PERSON> thành viên của nhóm sẽ có quyền truy cập vào bất kỳ bộ sưu tập nào mà nhóm này đã được thêm vào.", "Add people": "<PERSON><PERSON><PERSON><PERSON>", "Listing members of the <em>{{groupName}}</em> group.": "Li<PERSON><PERSON> kê các thành viên của nhóm <em>{{groupName}}</em>.", "This group has no members.": "Nhóm này không có thành viên.", "{{userName}} was added to the group": "{{userName}} đ<PERSON> đ<PERSON><PERSON><PERSON> thêm vào nhóm", "Could not add user": "<PERSON><PERSON><PERSON><PERSON> thể thêm người dùng", "Add members below to give them access to the group. Need to add someone who’s not yet a member?": "Thêm thành viên bên dưới để cấp cho họ quyền truy cập vào nhóm. C<PERSON>n thêm một người chưa phải là thành viên?", "Invite them to {{teamName}}": "<PERSON><PERSON><PERSON> họ vào {{teamName}}", "Ask an admin to invite them first": "Ask an admin to invite them first", "Search by name": "<PERSON><PERSON><PERSON> theo tên", "Search people": "<PERSON><PERSON><PERSON> ki<PERSON>m mọi ng<PERSON>i", "No people matching your search": "<PERSON><PERSON><PERSON><PERSON> có người nào phù hợp với tìm kiếm của bạn", "No people left to add": "<PERSON><PERSON><PERSON><PERSON> còn người để thêm", "Date created": "Date created", "Crop Image": "Crop Image", "Crop image": "Crop image", "How does this work?": "Nó hoạt động như thế nào?", "You can import a zip file that was previously exported from the JSON option in another instance. In {{ appName }}, open <em>Export</em> in the Settings sidebar and click on <em>Export Data</em>.": "Bạn có thể nhập tệp zip đã được xuất trước đó từ tùy chọn JSON trong một phiên bản khác. Trong {{ appName }}, mở <em>Xuất</em> trong thanh bên <PERSON>à<PERSON> đặt và nhấp vào <em>Xuất dữ liệu</em>.", "Drag and drop the zip file from the JSON export option in {{appName}}, or click to upload": "<PERSON><PERSON><PERSON> và thả tệp zip từ tùy chọn xuất JSO<PERSON> trong {{appName}} hoặc nhấp để tải lên", "Canceled": "Canceled", "Import canceled": "Import canceled", "Are you sure you want to cancel this import?": "Are you sure you want to cancel this import?", "Canceling": "Canceling", "Canceling this import will discard any progress made. This cannot be undone.": "Canceling this import will discard any progress made. This cannot be undone.", "{{ count }} document imported": "{{ count }} document imported", "{{ count }} document imported_plural": "{{ count }} documents imported", "You can import a zip file that was previously exported from an Outline installation – collections, documents, and images will be imported. In Outline, open <em>Export</em> in the Settings sidebar and click on <em>Export Data</em>.": "Bạn có thể nhập tệp zip đã được xuất trước đó từ cài đặt Outline - các bộ sưu tập, tài liệu và hình ảnh sẽ được nhập. Trong Outline, mở <em><PERSON><PERSON><PERSON></em> trong thanh bên Cài đặt và nhấp vào <em>Xuất dữ liệu</em>.", "Drag and drop the zip file from the Markdown export option in {{appName}}, or click to upload": "<PERSON><PERSON><PERSON> và thả tệp zip từ tùy chọn xuất Markdown trong {{appName}} hoặc nhấp để tải lên", "Configure": "Configure", "Connect": "<PERSON><PERSON><PERSON>", "Last active": "<PERSON><PERSON><PERSON> động lần cu<PERSON>i", "Guest": "Guest", "Never used": "Never used", "Are you sure you want to delete the {{ appName }} application? This cannot be undone.": "Are you sure you want to delete the {{ appName }} application? This cannot be undone.", "Shared by": "Shared by", "Date shared": "<PERSON><PERSON><PERSON> chia sẻ", "Last accessed": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> lầ<PERSON>", "Domain": "Domain", "Views": "<PERSON><PERSON><PERSON><PERSON> xem", "All roles": "All roles", "Admins": "<PERSON><PERSON><PERSON><PERSON> trị viên", "Editors": "Editors", "All status": "All status", "Active": "<PERSON><PERSON><PERSON> đ<PERSON>", "Left": "Left", "Right": "Right", "Settings saved": "<PERSON><PERSON> lưu cài đặt", "Logo updated": "Đ<PERSON> cập nhật logo", "Unable to upload new logo": "<PERSON><PERSON><PERSON><PERSON> thể tải lên biểu trưng mới", "Delete workspace": "Delete workspace", "These settings affect the way that your workspace appears to everyone on the team.": "These settings affect the way that your workspace appears to everyone on the team.", "Display": "<PERSON><PERSON><PERSON> thị", "The logo is displayed at the top left of the application.": "<PERSON><PERSON> đư<PERSON><PERSON> hiển thị ở trên cùng bên trái của ứng dụng.", "The workspace name, usually the same as your company name.": "Tên workspace, thường giống với tên công ty của bạn.", "Description": "Description", "A short description of your workspace.": "A short description of your workspace.", "Theme": "Chủ đề", "Customize the interface look and feel.": "<PERSON><PERSON><PERSON> chỉnh giao diện và cảm nhận.", "Reset theme": "Đặt lại giao di<PERSON>n", "Accent color": "<PERSON><PERSON><PERSON>", "Accent text color": "<PERSON><PERSON><PERSON> v<PERSON>n bản n<PERSON>n", "Public branding": "<PERSON><PERSON><PERSON><PERSON><PERSON> hiệu công cộng", "Show your workspace logo, description, and branding on publicly shared pages.": "Show your workspace logo, description, and branding on publicly shared pages.", "Table of contents position": "Table of contents position", "The side to display the table of contents in relation to the main content.": "The side to display the table of contents in relation to the main content.", "Behavior": "Hành vi", "Subdomain": "<PERSON><PERSON><PERSON>n phụ", "Your workspace will be accessible at": "Your workspace will be accessible at", "Choose a subdomain to enable a login page just for your team.": "<PERSON><PERSON><PERSON> một miền phụ để kích hoạt trang đăng nhập chỉ dành cho nhóm của bạn.", "This is the screen that workspace members will first see when they sign in.": "<PERSON><PERSON>y là màn hình mà các thành viên không gian làm việc sẽ nhìn thấy đầu tiên khi họ đăng nhập.", "Danger": "<PERSON><PERSON>", "You can delete this entire workspace including collections, documents, and users.": "You can delete this entire workspace including collections, documents, and users.", "Export data": "<PERSON><PERSON><PERSON> dữ liệu", "A full export might take some time, consider exporting a single document or collection. You may leave this page once the export has started – if you have notifications enabled, we will email a link to <em>{{ userEmail }}</em> when it’s complete.": "A full export might take some time, consider exporting a single document or collection. You may leave this page once the export has started – if you have notifications enabled, we will email a link to <em>{{ userEmail }}</em> when it’s complete.", "Recent exports": "<PERSON><PERSON><PERSON> xu<PERSON>t gần đ<PERSON>y", "Manage optional and beta features. Changing these settings will affect the experience for all members of the workspace.": "<PERSON><PERSON><PERSON>n lý các tính năng tùy chọn và beta. Vi<PERSON>c thay đổi các cài đặt này sẽ ảnh hưởng đến trải nghiệm của tất cả thành viên trong không gian làm việc.", "Separate editing": "Separate editing", "When enabled documents have a separate editing mode by default instead of being always editable. This setting can be overridden by user preferences.": "When enabled documents have a separate editing mode by default instead of being always editable. This setting can be overridden by user preferences.", "When enabled team members can add comments to documents.": "<PERSON><PERSON> đ<PERSON><PERSON><PERSON> bật, các thành viên trong nhóm có thể thêm nhận xét vào tài liệu.", "Create a group": "Tạo nhóm", "Could not load groups": "Could not load groups", "New group": "<PERSON>h<PERSON><PERSON> mới", "Groups can be used to organize and manage the people on your team.": "<PERSON><PERSON><PERSON><PERSON> có thể được sử dụng để tổ chức và quản lý những người trong nhóm của bạn.", "No groups have been created yet": "Chưa có nhóm nào đ<PERSON><PERSON><PERSON> tạo", "Import a zip file of Markdown documents (exported from version 0.67.0 or earlier)": "<PERSON><PERSON><PERSON><PERSON> tệp zip của tài liệu <PERSON> (xuất từ phiên bản 0.67.0 trở về trước)", "Import data": "<PERSON><PERSON><PERSON><PERSON> liệu", "Import a JSON data file exported from another {{ appName }} instance": "<PERSON><PERSON><PERSON><PERSON> tệp dữ liệu JSON được xuất từ một phiên bản {{ appName }} kh<PERSON>c", "Import pages from a Confluence instance": "<PERSON><PERSON><PERSON><PERSON> các trang từ Confluence", "Enterprise": "<PERSON><PERSON><PERSON>", "Quickly transfer your existing documents, pages, and files from other tools and services into {{appName}}. You can also drag and drop any HTML, Markdown, and text documents directly into Collections in the app.": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>h các tà<PERSON>, trang và tệp hiện có của bạn từ các công cụ và dịch vụ kh<PERSON> sang {{appName}}. Bạn cũng có thể kéo và thả bất kỳ tài liệu HTML, Markdown và văn bản nào trực tiếp vào Bộ sưu tập trong ứng dụng.", "Recent imports": "<PERSON><PERSON><PERSON> xu<PERSON>t gần đ<PERSON>y", "Configure a variety of integrations with third-party services.": "Configure a variety of integrations with third-party services.", "Could not load members": "Could not load members", "Everyone that has signed into {{appName}} is listed here. It’s possible that there are other users who have access through {{signinMethods}} but haven’t signed in yet.": "Everyone that has signed into {{appName}} is listed here. It’s possible that there are other users who have access through {{signinMethods}} but haven’t signed in yet.", "Receive a notification whenever a new document is published": "<PERSON><PERSON><PERSON><PERSON> thông báo bất cứ khi nào một tài liệu mới được xuất bản", "Document updated": "<PERSON><PERSON><PERSON> li<PERSON><PERSON><PERSON> c<PERSON>h<PERSON>t", "Receive a notification when a document you are subscribed to is edited": "<PERSON><PERSON><PERSON>n thông báo khi tài liệu bạn đã đăng ký được chỉnh sửa", "Comment posted": "<PERSON>hận xét đã đăng", "Receive a notification when a document you are subscribed to or a thread you participated in receives a comment": "Nhận thông báo khi tài liệu bạn đã đăng ký hoặc chủ đề bạn tham gia nhận được nhận xét", "Mentioned": "<PERSON><PERSON><PERSON><PERSON> đến", "Receive a notification when someone mentions you in a document or comment": "Nhận thông báo khi ai đó đề cập đến bạn trong tài liệu hoặc nhận xét", "Receive a notification when a comment thread you were involved in is resolved": "Receive a notification when a comment thread you were involved in is resolved", "Collection created": "<PERSON><PERSON> sưu tập đã đư<PERSON><PERSON> tạo", "Receive a notification whenever a new collection is created": "<PERSON><PERSON><PERSON><PERSON> thông báo bất cứ khi nào một bộ sưu tập mới đư<PERSON><PERSON> tạo", "Invite accepted": "<PERSON><PERSON> chấp nhận L<PERSON> mời", "Receive a notification when someone you invited creates an account": "<PERSON><PERSON><PERSON><PERSON> thông báo khi ai đó bạn đã mời tạo tài khoản", "Invited to document": "Invited to document", "Receive a notification when a document is shared with you": "Receive a notification when a document is shared with you", "Invited to collection": "Invited to collection", "Receive a notification when you are given access to a collection": "Receive a notification when you are given access to a collection", "Export completed": "<PERSON><PERSON><PERSON> hoàn tất", "Receive a notification when an export you requested has been completed": "<PERSON><PERSON><PERSON><PERSON> thông báo khi quá trình xuất mà bạn yêu cầu đã hoàn tất", "Getting started": "<PERSON><PERSON><PERSON> đ<PERSON>u", "Tips on getting started with features and functionality": "Mẹo bắt đầu với các tính năng và chức năng", "New features": "<PERSON><PERSON><PERSON> n<PERSON>ng mới", "Receive an email when new features of note are added": "Nhận email khi các tính năng mới của ghi chú được thêm vào", "Notifications saved": "<PERSON><PERSON> lưu thông báo", "Unsubscription successful. Your notification settings were updated": "Hủy đăng ký thành công. Cài đặt thông báo của bạn đã được cập nhật", "Manage when and where you receive email notifications.": "<PERSON><PERSON><PERSON><PERSON> lý thời gian và địa điểm bạn nhận đ<PERSON><PERSON><PERSON> thông báo qua email.", "The email integration is currently disabled. Please set the associated environment variables and restart the server to enable notifications.": "<PERSON><PERSON><PERSON> h<PERSON><PERSON> email hiện đã bị vô hiệu hóa. <PERSON><PERSON> lòng đặt các biến môi trường được liên kết và khởi động lại máy chủ để bật thông báo.", "Preferences saved": "<PERSON><PERSON><PERSON>", "Delete account": "<PERSON><PERSON><PERSON> tài <PERSON>n", "Manage settings that affect your personal experience.": "<PERSON><PERSON><PERSON>n lý cài đặt ảnh hưởng đến trải nghiệm cá nhân của bạn.", "Language": "<PERSON><PERSON><PERSON>", "Choose the interface language. Community translations are accepted though our <2>translation portal</2>.": "<PERSON>ọn ngôn ngữ giao diện. <PERSON><PERSON><PERSON> dịch của cộng đồng đư<PERSON><PERSON> chấp nhận qua <2>cổng dịch thuật của chúng tôi</2>.", "Choose your preferred interface color scheme.": "Choose your preferred interface color scheme.", "Use pointer cursor": "Sử dụng con trỏ con trỏ", "Show a hand cursor when hovering over interactive elements.": "Hiển thị con trỏ tay khi di chuột qua các phần tử tương tác.", "Show line numbers": "<PERSON><PERSON><PERSON> thị dòng số", "Show line numbers on code blocks in documents.": "<PERSON><PERSON><PERSON> thị số dòng trên các khối mã trong tài liệu.", "When enabled, documents have a separate editing mode. When disabled, documents are always editable when you have permission.": "When enabled, documents have a separate editing mode. When disabled, documents are always editable when you have permission.", "Remember previous location": "Ghi nhớ vị trí trước đó", "Automatically return to the document you were last viewing when the app is re-opened.": "Tự động quay lại tài liệu bạn xem lần cuối khi mở lại ứng dụng.", "Smart text replacements": "Smart text replacements", "Auto-format text by replacing shortcuts with symbols, dashes, smart quotes, and other typographical elements.": "Auto-format text by replacing shortcuts with symbols, dashes, smart quotes, and other typographical elements.", "You may delete your account at any time, note that this is unrecoverable": "Bạn có thể xóa tài khoản của mình bất kỳ lúc nào, lưu ý rằng điều này không thể khôi phục được", "Profile saved": "<PERSON><PERSON> sơ đã lưu", "Profile picture updated": "<PERSON><PERSON> cập nh<PERSON>t <PERSON><PERSON> hồ sơ", "Unable to upload new profile picture": "<PERSON><PERSON><PERSON><PERSON> thể tải lên <PERSON>nh hồ sơ mới", "Manage how you appear to other members of the workspace.": "<PERSON><PERSON><PERSON><PERSON> lý cách bạn xuất hiện với các thành viên khác trong không gian làm việc.", "Photo": "Ảnh", "Choose a photo or image to represent yourself.": "<PERSON><PERSON><PERSON> một bức ảnh hoặc hình ảnh để đại diện cho ch<PERSON>h bạn.", "This could be your real name, or a nickname — however you’d like people to refer to you.": "<PERSON><PERSON><PERSON> có thể là tên thật của bạn hoặc biệt hiệu mà bạn muốn mọi người nhắc đến mình.", "Email address": "Địa chỉ email", "Are you sure you want to require invites?": "Bạn có chắc chắn muốn yêu cầu lời mời không?", "New users will first need to be invited to create an account. <em>Default role</em> and <em>Allowed domains</em> will no longer apply.": "Người dùng mới trước tiên sẽ cần được mời để tạo tài khoản. <em>Vai trò mặc định</em> và <em><PERSON><PERSON><PERSON> miền được phép</em> sẽ không còn áp dụng.", "Settings that impact the access, security, and content of your workspace.": "Settings that impact the access, security, and content of your workspace.", "Allow members to sign-in with {{ authProvider }}": "Cho phép thành viên đăng nhập bằng {{ authProvider }}", "Disabled": "<PERSON><PERSON> hi<PERSON> h<PERSON>a", "Allow members to sign-in using their email address": "Cho phép thành viên đăng nhập bằng địa chỉ email của họ", "The server must have SMTP configured to enable this setting": "<PERSON><PERSON>y chủ phải được định cấu hình SMTP để bật cài đặt này", "Access": "<PERSON><PERSON><PERSON><PERSON>", "Allow users to send invites": "Allow users to send invites", "Allow editors to invite other people to the workspace": "Allow editors to invite other people to the workspace", "Require invites": "<PERSON><PERSON><PERSON> c<PERSON>u lời mời", "Require members to be invited to the workspace before they can create an account using SSO.": "<PERSON><PERSON><PERSON> cầu các thành viên được mời vào không gian làm việc trước khi họ có thể tạo tài khoản bằng SSO.", "Default role": "<PERSON>ai trò mặc định", "The default user role for new accounts. Changing this setting does not affect existing user accounts.": "<PERSON>ai trò người dùng mặc định cho các tài khoản mới. Việc thay đổi cài đặt này không ảnh hưởng đến các tài khoản người dùng hiện có.", "When enabled, documents can be shared publicly on the internet by any member of the workspace": "<PERSON><PERSON> đượ<PERSON> bật, tà<PERSON> liệu có thể được chia sẻ công khai trên internet bởi bất kỳ thành viên nào trong không gian làm việc", "Viewer document exports": "<PERSON><PERSON>t tài liệu của người xem", "When enabled, viewers can see download options for documents": "<PERSON><PERSON> đ<PERSON><PERSON><PERSON> b<PERSON>, ng<PERSON><PERSON><PERSON> xem có thể thấy các tùy chọn tải xuống tài liệu", "Users can delete account": "Users can delete account", "When enabled, users can delete their own account from the workspace": "When enabled, users can delete their own account from the workspace", "Rich service embeds": "<PERSON><PERSON><PERSON> vụ nhúng phong phú", "Links to supported services are shown as rich embeds within your documents": "<PERSON><PERSON><PERSON> đường Link đến các dịch vụ được hỗ trợ được hiển thị dưới dạng nhúng phong phú trong tài liệu của bạn", "Collection creation": "<PERSON><PERSON><PERSON> bộ s<PERSON>u tập", "Allow editors to create new collections within the workspace": "Allow editors to create new collections within the workspace", "Workspace creation": "Workspace creation", "Allow editors to create new workspaces": "Allow editors to create new workspaces", "Could not load shares": "Could not load shares", "Sharing is currently disabled.": "<PERSON><PERSON> sẻ hiện đang bị vô hiệu hóa.", "You can globally enable and disable public document sharing in the <em>security settings</em>.": "Bạn có thể bật và tắt chia sẻ tài liệu công khai trên toàn cầu trong <em>cài đặt bảo mật</em>.", "Documents that have been shared are listed below. Anyone that has the public link can access a read-only version of the document until the link has been revoked.": "<PERSON><PERSON><PERSON> tài liệu đã được chia sẻ được liệt kê dưới đây. Bất kỳ ai có liên kết công khai đều có thể truy cập phiên bản chỉ đọc của tài liệu cho đến khi liên kết bị thu hồi.", "You can create templates to help your team create consistent and accurate documentation.": "Bạn có thể tạo các mẫu để giúp nhóm của mình tạo tài liệu nhất quán và chính xác.", "Alphabetical": "Bảng chữ cái", "There are no templates just yet.": "<PERSON><PERSON><PERSON><PERSON> có mẫu nào được nêu ra.", "A confirmation code has been sent to your email address, please enter the code below to permanently destroy this workspace.": "A confirmation code has been sent to your email address, please enter the code below to permanently destroy this workspace.", "Confirmation code": "Confirmation code", "Deleting the <1>{{workspaceName}}</1> workspace will destroy all collections, documents, users, and associated data. You will be immediately logged out of {{appName}}.": "Deleting the <1>{{workspaceName}}</1> workspace will destroy all collections, documents, users, and associated data. You will be immediately logged out of {{appName}}.", "Please note that workspaces are completely separated. They can have a different domain, settings, users, and billing.": "Please note that workspaces are completely separated. They can have a different domain, settings, users, and billing.", "You are creating a new workspace using your current account — <em>{{email}}</em>": "You are creating a new workspace using your current account — <em>{{email}}</em>", "To create a workspace under another email please sign up from the homepage": "To create a workspace under another email please sign up from the homepage", "Trash emptied": "Trash emptied", "Are you sure you want to permanently delete all the documents in Trash? This action is immediate and cannot be undone.": "Are you sure you want to permanently delete all the documents in Trash? This action is immediate and cannot be undone.", "Recently deleted": "Recently deleted", "Trash is empty at the moment.": "<PERSON><PERSON><PERSON><PERSON> rác hiện đang trống.", "A confirmation code has been sent to your email address, please enter the code below to permanently destroy your account.": "A confirmation code has been sent to your email address, please enter the code below to permanently destroy your account.", "Are you sure? Deleting your account will destroy identifying data associated with your user and cannot be undone. You will be immediately logged out of {{appName}} and all your API tokens will be revoked.": "Bạn có chắc không? Xóa tài khoản của bạn sẽ hủy dữ liệu nhận dạng được liên kết với người dùng của bạn và không thể hoàn tác. Bạn sẽ bị đăng xuất khỏi {{appName}} ngay lập tức và tất cả mã thông báo API của bạn sẽ bị thu hồi.", "Delete my account": "Delete my account", "Today": "<PERSON><PERSON><PERSON> nay", "Yesterday": "<PERSON><PERSON><PERSON> qua", "Last week": "<PERSON><PERSON><PERSON> tr<PERSON>", "This month": "<PERSON><PERSON><PERSON><PERSON>", "Last month": "<PERSON><PERSON><PERSON><PERSON>", "This year": "<PERSON><PERSON><PERSON> nay", "Expired yesterday": "Expired yesterday", "Expired {{ date }}": "Expired {{ date }}", "Expires today": "Expires today", "Expires tomorrow": "Expires tomorrow", "Expires {{ date }}": "Expires {{ date }}", "Whoops, you need to accept the permissions in GitHub to connect {{appName}} to your workspace. Try again?": "Whoops, you need to accept the permissions in GitHub to connect {{appName}} to your workspace. Try again?", "Something went wrong while authenticating your request. Please try logging in again.": "Something went wrong while authenticating your request. Please try logging in again.", "The owner of GitHub account has been requested to install the {{githubAppName}} GitHub app. Once approved, previews will be shown for respective links.": "The owner of GitHub account has been requested to install the {{githubAppName}} GitHub app. Once approved, previews will be shown for respective links.", "Enable previews of GitHub issues and pull requests in documents by connecting a GitHub organization or specific repositories to {appName}.": "Enable previews of GitHub issues and pull requests in documents by connecting a GitHub organization or specific repositories to {appName}.", "Enabled by {{integrationCreatedBy}}": "Enabled by {{integrationCreated<PERSON>y}}", "Disconnecting will prevent previewing GitHub links from this organization in documents. Are you sure?": "Disconnecting will prevent previewing GitHub links from this organization in documents. Are you sure?", "The GitHub integration is currently disabled. Please set the associated environment variables and restart the server to enable the integration.": "The GitHub integration is currently disabled. Please set the associated environment variables and restart the server to enable the integration.", "Google Analytics": "Google Analytics", "Add a Google Analytics 4 measurement ID to send document views and analytics from the workspace to your own Google Analytics account.": "Thêm ID đo lường Google Analytics 4 để gửi lượt xem tài liệu và số liệu phân tích từ không gian làm việc đến tà<PERSON> k<PERSON>n Google Analytics của riêng bạn.", "Measurement ID": "<PERSON> đo l<PERSON>", "Create a \"Web\" stream in your Google Analytics admin dashboard and copy the measurement ID from the generated code snippet to install.": "Tạ<PERSON> luồng \"Web\" trong bảng điều khiển quản trị Google Analytics của bạn và sao chép ID đo lường từ đoạn mã đã tạo để cài đặt.", "Whoops, you need to accept the permissions in Linear to connect {{appName}} to your workspace. Try again?": "Whoops, you need to accept the permissions in Linear to connect {{appName}} to your workspace. Try again?", "Something went wrong while processing your request. Please try again.": "Something went wrong while processing your request. Please try again.", "Enable previews of Linear issues in documents by connecting a Linear workspace to {appName}.": "Enable previews of Linear issues in documents by connecting a Linear workspace to {appName}.", "Disconnecting will prevent previewing Linear links from this workspace in documents. Are you sure?": "Disconnecting will prevent previewing Linear links from this workspace in documents. Are you sure?", "The Linear integration is currently disabled. Please set the associated environment variables and restart the server to enable the integration.": "The Linear integration is currently disabled. Please set the associated environment variables and restart the server to enable the integration.", "Configure a Matomo installation to send views and analytics from the workspace to your own Matomo instance.": "Configure a Matomo installation to send views and analytics from the workspace to your own Matomo instance.", "Instance URL": "Instance URL", "The URL of your Matomo instance. If you are using Matomo Cloud it will end in matomo.cloud/": "The URL of your Matomo instance. If you are using Matomo Cloud it will end in matomo.cloud/", "Site ID": "Site ID", "An ID that uniquely identifies the website in your Matomo instance.": "An ID that uniquely identifies the website in your Matomo instance.", "Whoops, you need to accept the permissions in Notion to connect {{ appName }} to your workspace. Try again?": "Whoops, you need to accept the permissions in Notion to connect {{ appName }} to your workspace. Try again?", "Import pages from Notion": "Import pages from Notion", "Add to Slack": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON>", "document published": "tài liệu đ<PERSON><PERSON><PERSON> đăng tải", "document updated": "tà<PERSON> li<PERSON><PERSON> đ<PERSON><PERSON><PERSON> c<PERSON>h<PERSON>t", "Posting to the <em>{{ channelName }}</em> channel on": "<PERSON><PERSON><PERSON> lên kênh <em>{{ channelName }}</em>", "These events should be posted to Slack": "Những sự kiện này nên đ<PERSON><PERSON><PERSON> đăng lên <PERSON>ck", "This will prevent any future updates from being posted to this Slack channel. Are you sure?": "This will prevent any future updates from being posted to this Slack channel. Are you sure?", "Whoops, you need to accept the permissions in Slack to connect {{appName}} to your workspace. Try again?": "Whoops, you need to accept the permissions in Slack to connect {{appName}} to your workspace. Try again?", "Personal account": "Personal account", "Link your {{appName}} account to Slack to enable searching and previewing the documents you have access to, directly within chat.": "Link your {{appName}} account to Slack to enable searching and previewing the documents you have access to, directly within chat.", "Disconnecting your personal account will prevent searching for documents from Slack. Are you sure?": "Disconnecting your personal account will prevent searching for documents from S<PERSON>ck. Are you sure?", "Slash command": "Slash command", "Get rich previews of {{ appName }} links shared in Slack and use the <em>{{ command }}</em> slash command to search for documents without leaving your chat.": "<PERSON><PERSON><PERSON><PERSON> các bản xem trước phong phú của {{ appName }} liên kết được chia sẻ trong Slack và sử dụng lệnh gạch chéo <em>{{ command }}</em> để tìm kiếm tài liệu mà không cần rời khỏi cuộc trò chuyện của bạn.", "This will remove the Outline slash command from your Slack workspace. Are you sure?": "This will remove the Outline slash command from your Slack workspace. Are you sure?", "Connect {{appName}} collections to Slack channels. Messages will be automatically posted to Slack when documents are published or updated.": "Kết nối {{appName}} bộ sưu tập với các kênh Slack. Tin nhắn sẽ tự động được đăng lên Slack khi tài liệu được xuất bản hoặc cập nhật.", "Comment by {{ author }} on \"{{ title }}\"": "Comment by {{ author }} on \"{{ title }}\"", "How to use {{ command }}": "How to use {{ command }}", "To search your workspace use {{ command }}. \nType {{ command2 }} help to display this help text.": "To search your workspace use {{ command }}. \nType {{ command2 }} help to display this help text.", "Post to Channel": "<PERSON><PERSON><PERSON> l<PERSON>", "This is what we found for \"{{ term }}\"": "<PERSON><PERSON><PERSON> là những gì chúng tôi tìm thấy cho \"{{ term }}\"", "No results for \"{{ term }}\"": "<PERSON><PERSON><PERSON><PERSON> có kết quả cho \"{{ term }}\"", "It looks like you haven’t linked your {{ appName }} account to Slack yet": "It looks like you haven’t linked your {{ appName }} account to Slack yet", "Link your account": "Link your account", "Link your account in {{ appName }} settings to search from Slack": "Link your account in {{ appName }} settings to search from Slack", "Configure a Umami installation to send views and analytics from the workspace to your own Umami instance.": "Configure a Umami installation to send views and analytics from the workspace to your own Umami instance.", "The URL of your Umami instance. If you are using Umami Cloud it will begin with {{ url }}": "The URL of your Umami instance. If you are using Umami Cloud it will begin with {{ url }}", "Script name": "Script name", "The name of the script file that Umami uses to track analytics.": "The name of the script file that <PERSON><PERSON> uses to track analytics.", "An ID that uniquely identifies the website in your Umami instance.": "An ID that uniquely identifies the website in your Umami instance.", "Are you sure you want to delete the {{ name }} webhook?": "Bạn có chắc chắn muốn xóa webhook {{ name }} không?", "Webhook updated": "Webhook đã cập nh<PERSON>t", "Update": "<PERSON><PERSON><PERSON>", "Updating": "<PERSON><PERSON> cập nh<PERSON>t", "Provide a descriptive name for this webhook and the URL we should send a POST request to when matching events are created.": "<PERSON><PERSON> cấp tên mô tả cho webhook này và URL mà chúng tôi sẽ gửi yêu cầu POST khi tạo các sự kiện phù hợp.", "A memorable identifer": "<PERSON>ột nhận dạng đáng nhớ", "URL": "Đường Dẫn URL", "Signing secret": "<PERSON><PERSON> ký bí mật", "Subscribe to all events, groups, or individual events. We recommend only subscribing to the minimum amount of events that your application needs to function.": "<PERSON>ăng ký tất cả các sự kiện, nhóm hoặc sự kiện riêng lẻ. Chúng tôi khuyên bạn chỉ nên đăng ký số lượng sự kiện tối thiểu mà ứng dụng của bạn cần để hoạt động.", "All events": "<PERSON><PERSON><PERSON> c<PERSON> các sự kiện", "All {{ groupName }} events": "<PERSON><PERSON><PERSON> c<PERSON> sự kiện {{ groupName }}", "Delete webhook": "Xóa Webhook", "Subscribed events": "<PERSON><PERSON><PERSON> sự kiện đã đăng ký", "Edit webhook": "Chỉnh sửa webhook", "Webhook created": "Đã tạo webhook", "Webhooks": "Webhook", "New webhook": "Webhook mới", "Webhooks can be used to notify your application when events happen in {{appName}}. Events are sent as a https request with a JSON payload in near real-time.": "Webhook có thể được sử dụng để thông báo cho ứng dụng của bạn khi các sự kiện xảy ra trong {{appName}}. Các sự kiện được gửi dưới dạng yêu cầu https với tải trọng JSON gần thời gian thực.", "Inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "Create a webhook": "<PERSON><PERSON><PERSON> Webhook", "Zapier is a platform that allows {{appName}} to easily integrate with thousands of other business tools. Automate your workflows, sync data, and more.": "<PERSON><PERSON><PERSON> là một nền tảng cho phép {{appName}} dễ dàng tích hợp với hàng nghìn công cụ kinh doanh khác. Tự động hóa quy trình công vi<PERSON><PERSON> c<PERSON><PERSON> bạn, đồ<PERSON> bộ hóa dữ liệu, v. v.", "Never logged in": "Never logged in", "Online now": "Online now", "Online {{ timeAgo }}": "Online {{ timeAgo }}", "Viewed just now": "Viewed just now", "You updated {{ timeAgo }}": "You updated {{ timeAgo }}", "{{ user }} updated {{ timeAgo }}": "{{ user }} updated {{ timeAgo }}", "You created {{ timeAgo }}": "You created {{ timeAgo }}", "{{ user }} created {{ timeAgo }}": "{{ user }} created {{ timeAgo }}", "Error loading data": "Error loading data"}
{"New API key": "New API key", "Open collection": "<PERSON><PERSON> k<PERSON>i", "New collection": "<PERSON>leksi baru", "Create a collection": "<PERSON><PERSON><PERSON> kole<PERSON>i", "Edit": "Sunting", "Edit collection": "<PERSON><PERSON> koleksi", "Permissions": "Perizinan", "Collection permissions": "Perizinan k<PERSON>i", "Share this collection": "Bagikan koleksi ini", "Search in collection": "Cari di koleksi", "Star": "Bintangi", "Unstar": "<PERSON><PERSON><PERSON><PERSON>", "Subscribe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Subscribed to document notifications": "Berlangganan pemberitahuan dokumen", "Unsubscribe": "<PERSON><PERSON><PERSON><PERSON>", "Unsubscribed from document notifications": "Langganan pemberitahuan dokumen telah dihentikan", "Archive": "<PERSON><PERSON><PERSON><PERSON>", "Archive collection": "Archive collection", "Collection archived": "Collection archived", "Archiving": "Mengarsipkan", "Archiving this collection will also archive all documents within it. Documents from the collection will no longer be visible in search results.": "Archiving this collection will also archive all documents within it. Documents from the collection will no longer be visible in search results.", "Restore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Collection restored": "Collection restored", "Delete": "Hapus", "Delete collection": "<PERSON><PERSON> k<PERSON>", "New template": "Templat baru", "Delete comment": "<PERSON><PERSON> komentar", "Mark as resolved": "<PERSON> as resolved", "Thread resolved": "Thread resolved", "Mark as unresolved": "<PERSON> as unresolved", "View reactions": "View reactions", "Reactions": "<PERSON><PERSON><PERSON>", "Copy ID": "Salin ID", "Clear IndexedDB cache": "Clear IndexedDB cache", "IndexedDB cache cleared": "IndexedDB cache cleared", "Toggle debug logging": "Toggle debug logging", "Debug logging enabled": "Debug logging enabled", "Debug logging disabled": "Debug logging disabled", "Development": "Pengembangan", "Open document": "Buka dokumen", "New document": "<PERSON><PERSON><PERSON> baru", "New draft": "New draft", "New from template": "<PERSON>u dari templat", "New nested document": "Buat dokumen bercabang", "Publish": "Terbitkan", "Published {{ documentName }}": "Published {{ documentName }}", "Publish document": "Terbitkan dokumen", "Unpublish": "Batalkan penerbitan", "Unpublished {{ documentName }}": "Unpublished {{ documentName }}", "Share this document": "Bagikan dokumen ini", "HTML": "HTML", "PDF": "PDF", "Exporting": "Mengekspor", "Markdown": "<PERSON><PERSON>", "Download": "<PERSON><PERSON><PERSON>", "Download document": "Unduh dokumen", "Copy as Markdown": "<PERSON><PERSON> sebagai <PERSON>", "Markdown copied to clipboard": "Markdown copied to clipboard", "Copy as text": "Copy as text", "Text copied to clipboard": "Text copied to clipboard", "Copy public link": "<PERSON><PERSON> tautan publik", "Link copied to clipboard": "Tautan disalin ke papan klip", "Copy link": "<PERSON><PERSON>an", "Copy": "<PERSON><PERSON>", "Duplicate": "Duplikat", "Duplicate document": "Duplikat dokumen", "Copy document": "Copy document", "collection": "koleksi", "Pin to {{collectionName}}": "Pin ke {{collectionName}}", "Pinned to collection": "<PERSON><PERSON> ke koleksi", "Pin to home": "Pin ke beranda", "Pinned to home": "Pinned to home", "Pin": "<PERSON>n", "Search in document": "Search in document", "Print": "Cetak", "Print document": "Cetak dokumen", "Import document": "Impor dokumen", "Templatize": "Jadikan template", "Create template": "Buat template", "Open random document": "Buka dokumen acak", "Search documents for \"{{searchQuery}}\"": "Telusuri dokumen untuk \"{{searchQuery}}\"", "Move to workspace": "Move to workspace", "Move": "Pindahkan", "Move to collection": "Move to collection", "Move {{ documentType }}": "Pindahkan {{ documentType }}", "Are you sure you want to archive this document?": "Are you sure you want to archive this document?", "Document archived": "<PERSON><PERSON><PERSON> diarsip", "Archiving this document will remove it from the collection and search results.": "Archiving this document will remove it from the collection and search results.", "Delete {{ documentName }}": "Hapus {{ documentName }}", "Permanently delete": "<PERSON><PERSON> secara permanen", "Permanently delete {{ documentName }}": "Hapus {{ documentName }} secara permanen", "Empty trash": "Kosongkan Sampah", "Permanently delete documents in trash": "Permanently delete documents in trash", "Comments": "Komentar", "History": "Riwayat", "Insights": "<PERSON><PERSON><PERSON>", "Disable viewer insights": "Disable viewer insights", "Enable viewer insights": "Enable viewer insights", "Leave document": "Leave document", "You have left the shared document": "You have left the shared document", "Could not leave document": "Could not leave document", "Home": "Be<PERSON><PERSON>", "Drafts": "Draf", "Search": "Telusuri", "Trash": "Sampah", "Settings": "<PERSON><PERSON><PERSON><PERSON>", "Profile": "Profil", "Templates": "Template", "Notifications": "Notif<PERSON><PERSON>", "Preferences": "<PERSON><PERSON><PERSON><PERSON>", "Documentation": "Dokumentasi", "API documentation": "Dokumentasi API", "Toggle sidebar": "Buka sidebar", "Send us feedback": "<PERSON><PERSON><PERSON> kami masukan", "Report a bug": "Laporkan bug", "Changelog": "Catatan perubahan", "Keyboard shortcuts": "Pintasan papan ketik", "Download {{ platform }} app": "<PERSON><PERSON><PERSON> a<PERSON> {{ platform }}", "Log out": "<PERSON><PERSON><PERSON>", "Mark notifications as read": "Mark notifications as read", "Archive all notifications": "Archive all notifications", "New App": "New App", "New Application": "New Application", "This version of the document was deleted": "This version of the document was deleted", "Link copied": "<PERSON>tan disalin", "Dark": "<PERSON><PERSON><PERSON>", "Light": "<PERSON><PERSON>", "System": "Sistem", "Appearance": "Tampilan", "Change theme": "U<PERSON> tema", "Change theme to": "<PERSON><PERSON> tema menjadi", "Switch workspace": "<PERSON><PERSON><PERSON> r<PERSON> kerja", "Select a workspace": "<PERSON><PERSON><PERSON> kerja", "New workspace": "<PERSON><PERSON> kerja baru", "Create a workspace": "<PERSON><PERSON><PERSON> ruang kerja", "Login to workspace": "Login to workspace", "Invite people": "Undang orang", "Invite to workspace": "Invite to workspace", "Promote to {{ role }}": "Promote to {{ role }}", "Demote to {{ role }}": "Demote to {{ role }}", "Update role": "Update role", "Delete user": "Hapus <PERSON>", "Collection": "<PERSON><PERSON><PERSON><PERSON>", "Collections": "<PERSON><PERSON><PERSON><PERSON>", "Debug": "Debug", "Document": "Dokumen", "Documents": "Dokumen", "Recently viewed": "<PERSON><PERSON>", "Revision": "Revisi", "Navigation": "Na<PERSON><PERSON><PERSON>", "Notification": "Pemberitahuan", "People": "Orang", "Workspace": "<PERSON><PERSON>", "Recent searches": "Penelusuran terbaru", "currently editing": "sedang menyunting", "currently viewing": "sedang melihat", "previously edited": "sebelumnya menyunting", "You": "<PERSON><PERSON>", "Viewers": "<PERSON><PERSON><PERSON>", "Collections are used to group documents and choose permissions": "Collections are used to group documents and choose permissions", "Name": "<PERSON><PERSON>", "The default access for workspace members, you can share with more users or groups later.": "The default access for workspace members, you can share with more users or groups later.", "Public document sharing": "Berbagi dokumen publik", "Allow documents within this collection to be shared publicly on the internet.": "Allow documents within this collection to be shared publicly on the internet.", "Commenting": "Mengomentari", "Allow commenting on documents within this collection.": "Allow commenting on documents within this collection.", "Saving": "Menyimpan", "Save": "Simpan", "Creating": "Membuat", "Create": "Buat", "Collection deleted": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "I’m sure – Delete": "<PERSON><PERSON> <PERSON><PERSON>", "Deleting": "<PERSON><PERSON><PERSON><PERSON>", "Are you sure about that? Deleting the <em>{{collectionName}}</em> collection is permanent and cannot be restored, however all published documents within will be moved to the trash.": "Anda yakin? Mengh<PERSON>us koleksi <em>{{collectionName}}</em> bersifat permanen dan tidak dapat dipulihkan, namun dokumen di dalamnya akan dipindahkan ke tempat sampah.", "Also, <em>{{collectionName}}</em> is being used as the start view – deleting it will reset the start view to the Home page.": "<PERSON>, <em>{{collectionName}}</em> digunakan sebagai tampilan awal – menghapusnya akan mengatur ulang tampilan awal ke halaman <PERSON>.", "Type a command or search": "<PERSON><PERSON><PERSON> perintah atau penelusuran", "Choose a template": "<PERSON><PERSON><PERSON> temp<PERSON>", "Are you sure you want to permanently delete this entire comment thread?": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus semua komentar ini secara permanen?", "Are you sure you want to permanently delete this comment?": "Apa Anda yakin ingin menghapus komentar ini secara permanen?", "Confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "manage access": "<PERSON><PERSON><PERSON>", "view and edit access": "melihat dan mengedit akses", "view only access": "akses hanya melihat", "no access": "tidak ada akses", "You do not have permission to move {{ documentName }} to the {{ collectionName }} collection": "You do not have permission to move {{ documentName }} to the {{ collectionName }} collection", "Move document": "Pindahkan dokumen", "Moving": "<PERSON><PERSON><PERSON><PERSON>", "Moving the document <em>{{ title }}</em> to the {{ newCollectionName }} collection will change permission for all workspace members from <em>{{ prevPermission }}</em> to <em>{{ newPermission }}</em>.": "Moving the document <em>{{ title }}</em> to the {{ newCollectionName }} collection will change permission for all workspace members from <em>{{ prevPermission }}</em> to <em>{{ newPermission }}</em>.", "Submenu": "Submenu", "Collections could not be loaded, please reload the app": "Koleksi tidak dapat dimuat, harap muat ulang aplikasi", "Default collection": "<PERSON><PERSON><PERSON><PERSON> bawaan", "Start view": "<PERSON><PERSON><PERSON> awal", "Install now": "<PERSON><PERSON>", "Deleted Collection": "<PERSON><PERSON><PERSON><PERSON>", "Untitled": "Tak <PERSON>", "Unpin": "Unpin", "{{ minutes }}m read": "{{ minutes }}m read", "Select a location to copy": "Select a location to copy", "Document copied": "Document copied", "Couldn’t copy the document, try again?": "Couldn’t copy the document, try again?", "Include nested documents": "Include nested documents", "Copy to <em>{{ location }}</em>": "Copy to <em>{{ location }}</em>", "Search collections & documents": "Telusuri koleksi & dokumen", "No results found": "<PERSON><PERSON> di<PERSON><PERSON>n hasil", "New": "<PERSON><PERSON>", "Only visible to you": "Hanya terlihat untuk Anda", "Draft": "Draf", "Template": "Template", "You updated": "<PERSON><PERSON>", "{{ userName }} updated": "{{ userName }} me<PERSON><PERSON>ui", "You deleted": "<PERSON><PERSON>", "{{ userName }} deleted": "{{ userName }} menghapus", "You archived": "<PERSON><PERSON>", "{{ userName }} archived": "{{ userName }} men<PERSON><PERSON><PERSON><PERSON>", "Imported": "<PERSON><PERSON><PERSON><PERSON> diimpor", "You created": "<PERSON>a memb<PERSON>t", "{{ userName }} created": "{{ userName }} membuat", "You published": "<PERSON><PERSON>", "{{ userName }} published": "{{ userName }} diterbitkan", "Never viewed": "Tak pernah dilihat", "Viewed": "<PERSON><PERSON><PERSON>", "in": "in", "nested document": "doku<PERSON> be<PERSON>", "nested document_plural": "doku<PERSON> be<PERSON>", "{{ total }} task": "{{ total }} tugas", "{{ total }} task_plural": "{{ total }} tugas", "{{ completed }} task done": "{{ completed }} tug<PERSON> se<PERSON>ai", "{{ completed }} task done_plural": "{{ completed }} tug<PERSON> se<PERSON>ai", "{{ completed }} of {{ total }} tasks": "{{ completed }} dari {{ total }} tugas", "Currently editing": "Sedang menyunting", "Currently viewing": "Sedang melihat", "Viewed {{ timeAgo }}": "Di<PERSON><PERSON> {{ timeAgo }} lalu", "Module failed to load": "<PERSON><PERSON><PERSON> gagal dimuat", "Loading Failed": "<PERSON><PERSON>", "Sorry, part of the application failed to load. This may be because it was updated since you opened the tab or because of a failed network request. Please try reloading.": "<PERSON><PERSON>, sebagian dari aplikasi gagal dimuat. Hal ini mungkin terjadi karena aplikasi telah diperbarui sejak Anda membuka tab atau karena permintaan yang gagal. <PERSON>lakan coba memuat ulang.", "Reload": "<PERSON><PERSON> ul<PERSON>", "Something Unexpected Happened": "<PERSON><PERSON><PERSON><PERSON> Yang Tidak Terduga Terjadi", "Sorry, an unrecoverable error occurred{{notified}}. Please try reloading the page, it may have been a temporary glitch.": "<PERSON><PERSON>, ter<PERSON><PERSON> kesalahan yang tidak dapat dipulihkan{{notified}}. <PERSON><PERSON>an coba memuat ulang halaman, mungkin ada kesalahan sementara.", "our engineers have been notified": "teknisi kami telah di<PERSON>", "Show detail": "Show detail", "Revision deleted": "Revision deleted", "Current version": "Current version", "{{userName}} edited": "{{userName}} menyunting", "{{userName}} archived": "{{userName}} men<PERSON><PERSON><PERSON><PERSON>", "{{userName}} restored": "{{userName}} memulihkan", "{{userName}} deleted": "{{userName}} menghapus", "{{userName}} added {{addedUserName}}": "{{userName}} added {{addedUserName}}", "{{userName}} removed {{removedUserName}}": "{{userName}} removed {{removedUserName}}", "{{userName}} moved from trash": "{{userName}} me<PERSON><PERSON><PERSON> dari sampah", "{{userName}} published": "{{userName}} menerbitkan", "{{userName}} unpublished": "{{userName}} tidak menerbitkan", "{{userName}} moved": "{{userName}} me<PERSON><PERSON><PERSON>", "Export started": "Ekspor dimulai", "Your file will be available in {{ location }} soon": "Your file will be available in {{ location }} soon", "View": "View", "A ZIP file containing the images, and documents in the Markdown format.": "Berkas ZIP yang berisi gambar, dan dokumen dalam format Markdown.", "A ZIP file containing the images, and documents as HTML files.": "Berkas ZIP yang berisi gambar, dan dokumen sebagai berkas HTML.", "Structured data that can be used to transfer data to another compatible {{ appName }} instance.": "Data terstruktur yang dapat digunakan untuk memindahkan data ke server {{ appName }} lain yang kompatibel.", "Export": "Ekspor", "Exporting the collection <em>{{collectionName}}</em> may take some time.": "Mengekspor koleksi <em>{{collectionName}}</em> mungkin memerlukan waktu.", "You will receive an email when it's complete.": "<PERSON>a akan menerima surel jika sudah selesai.", "Include attachments": "Include attachments", "Including uploaded images and files in the exported data": "Including uploaded images and files in the exported data", "{{count}} more user": "{{count}} more user", "{{count}} more user_plural": "{{count}} more users", "Filter": "Filter", "No results": "<PERSON>k ada hasil", "{{authorName}} created <3></3>": "{{authorName}} created <3></3>", "{{authorName}} opened <3></3>": "{{authorName}} opened <3></3>", "Search emoji": "<PERSON>i emoji", "Search icons": "Search icons", "Choose default skin tone": "Choose default skin tone", "Show menu": "Tampilkan menu", "Icon Picker": "<PERSON><PERSON> Picker", "Icons": "Icons", "Emojis": "Emojis", "Remove": "Hapus", "All": "<PERSON><PERSON><PERSON>", "Frequently Used": "Frequently Used", "Search Results": "<PERSON><PERSON>", "Smileys & People": "Smileys & People", "Animals & Nature": "Animals & Nature", "Food & Drink": "Food & Drink", "Activity": "Activity", "Travel & Places": "Travel & Places", "Objects": "Objects", "Symbols": "Symbols", "Flags": "Flags", "Select a color": "<PERSON><PERSON><PERSON> warna", "Loading": "Memuat", "Permission": "Permission", "View only": "<PERSON><PERSON> me<PERSON>", "Can edit": "Can edit", "No access": "Tidak memiliki akses", "Default access": "Akses default", "Change Language": "Ubah Bahasa", "Dismiss": "<PERSON><PERSON><PERSON>", "You’re offline.": "Anda sedang luring.", "Sorry, an error occurred.": "<PERSON><PERSON>, telah terjadi k<PERSON>han.", "Click to retry": "Klik untuk mencoba lagi", "Back": "Kembali", "Unknown": "Unknown", "Mark all as read": "Mark all as read", "You're all caught up": "You're all caught up", "Icon": "Icon", "My App": "My App", "Tagline": "Tagline", "A short description": "A short description", "Callback URLs": "Callback URLs", "Published": "Published", "Allow this app to be installed by other workspaces": "Allow this app to be installed by other workspaces", "{{ username }} reacted with {{ emoji }}": "{{ username }} reacted with {{ emoji }}", "{{ firstUsername }} and {{ secondUsername }} reacted with {{ emoji }}": "{{ firstUsername }} and {{ secondUsername }} reacted with {{ emoji }}", "{{ firstUsername }} and {{ count }} others reacted with {{ emoji }}": "{{ firstUsername }} and {{ count }} other reacted with {{ emoji }}", "{{ firstUsername }} and {{ count }} others reacted with {{ emoji }}_plural": "{{ firstUsername }} and {{ count }} others reacted with {{ emoji }}", "Add reaction": "Add reaction", "Reaction picker": "Reaction picker", "Could not load reactions": "Could not load reactions", "Reaction": "Reaction", "Results": "<PERSON><PERSON>", "No results for {{query}}": "Tak ada hasil untuk {{query}}", "Manage": "Manage", "All members": "All members", "Everyone in the workspace": "Everyone in the workspace", "{{ count }} member": "{{ count }} anggota", "{{ count }} member_plural": "{{ count }} anggota", "Invite": "Invite", "{{ userName }} was added to the collection": "{{ userName }} ditambah<PERSON> ke koleksi", "{{ count }} people added to the collection": "{{ count }} people added to the collection", "{{ count }} people added to the collection_plural": "{{ count }} people added to the collection", "{{ count }} people and {{ count2 }} groups added to the collection": "{{ count }} people and {{ count2 }} groups added to the collection", "{{ count }} people and {{ count2 }} groups added to the collection_plural": "{{ count }} people and {{ count2 }} groups added to the collection", "Add": "Tambahkan", "Add or invite": "Add or invite", "Viewer": "Viewer", "Editor": "Editor", "Suggestions for invitation": "Suggestions for invitation", "No matches": "No matches", "Can view": "Can view", "Everyone in the collection": "Everyone in the collection", "You have full access": "You have full access", "Created the document": "Created the document", "Other people": "Other people", "Other workspace members may have access": "Other workspace members may have access", "This document may be shared with more workspace members through a parent document or collection you do not have access to": "This document may be shared with more workspace members through a parent document or collection you do not have access to", "Access inherited from collection": "Access inherited from collection", "{{ userName }} was removed from the document": "{{ userName }} was removed from the document", "Could not remove user": "Tidak dapat menghapus pengguna", "Permissions for {{ userName }} updated": "Permissions for {{ userName }} updated", "Could not update user": "Tidak dapat memperbarui pengguna", "Has access through <2>parent</2>": "Has access through <2>parent</2>", "Suspended": "Disuspen", "Invited": "<PERSON><PERSON><PERSON>", "Active <1></1> ago": "Aktif <1></1> yang lalu", "Never signed in": "Tidak pernah masuk", "Leave": "Meninggalkan", "Only lowercase letters, digits and dashes allowed": "<PERSON><PERSON> huru<PERSON> k<PERSON>, an<PERSON><PERSON>, dan tanda hubung diperbolehkan", "Sorry, this link has already been used": "<PERSON><PERSON>, tautan ini telah digunakan", "Public link copied to clipboard": "Public link copied to clipboard", "Web": "Web", "Anyone with the link can access because the parent document, <2>{{documentTitle}}</2>, is shared": "Anyone with the link can access because the parent document, <2>{{documentTitle}}</2>, is shared", "Allow anyone with the link to access": "Allow anyone with the link to access", "Publish to internet": "Terbitkan ke internet", "Search engine indexing": "Search engine indexing", "Disable this setting to discourage search engines from indexing the page": "Disable this setting to discourage search engines from indexing the page", "Show last modified": "Show last modified", "Display the last modified timestamp on the shared page": "Display the last modified timestamp on the shared page", "Nested documents are not shared on the web. Toggle sharing to enable access, this will be the default behavior in the future": "Nested documents are not shared on the web. Toggle sharing to enable access, this will be the default behavior in the future", "{{ userName }} was added to the document": "{{ userName }} was added to the document", "{{ count }} people added to the document": "{{ count }} people added to the document", "{{ count }} people added to the document_plural": "{{ count }} people added to the document", "{{ count }} groups added to the document": "{{ count }} groups added to the document", "{{ count }} groups added to the document_plural": "{{ count }} groups added to the document", "Logo": "Logo", "Archived collections": "Archived collections", "New doc": "<PERSON><PERSON><PERSON> baru", "Empty": "Kosong", "Collapse": "<PERSON>sing<PERSON>", "Expand": "Perlengkap", "Document not supported – try Markdown, Plain text, HTML, or Word": "<PERSON><PERSON><PERSON> tidak diduku<PERSON> – co<PERSON>, te<PERSON> <PERSON>a, HTML, atau Word", "Go back": "Kembali", "Go forward": "Ke berik<PERSON>", "Could not load shared documents": "Could not load shared documents", "Shared with me": "Shared with me", "Show more": "Selengkapnya", "Could not load starred documents": "Could not load starred documents", "Starred": "Berbintang", "Up to date": "<PERSON><PERSON><PERSON><PERSON>", "{{ releasesBehind }} versions behind": "{{ <PERSON><PERSON><PERSON><PERSON> }} versi di belakang", "{{ releasesBehind }} versions behind_plural": "{{ <PERSON><PERSON><PERSON><PERSON> }} versi di belakang", "Change permissions?": "Change permissions?", "{{ documentName }} cannot be moved within {{ parentDocumentName }}": "{{ documentName }} cannot be moved within {{ parentDocumentName }}", "You can't reorder documents in an alphabetically sorted collection": "<PERSON>a tidak dapat menyusun ulang dokumen dalam koleksi yang diurutkan menurut abjad", "The {{ documentName }} cannot be moved here": "The {{ documentName }} cannot be moved here", "Return to App": "Kembali ke Aplikasi", "Installation": "<PERSON><PERSON><PERSON><PERSON>", "Unstar document": "Unstar document", "Star document": "Star document", "Template created, go ahead and customize it": "Template te<PERSON> di<PERSON>, silakan dises<PERSON><PERSON>n", "Creating a template from <em>{{titleWithDefault}}</em> is a non-destructive action – we'll make a copy of the document and turn it into a template that can be used as a starting point for new documents.": "Membuat template dari <em>{{titleWithDefault}}</em> adalah tindakan yang tidak merusak – kami akan membuat salinan dokumen dan mengubahnya menjadi template yang dapat digunakan sebagai titik awal untuk dokumen baru.", "Enable other members to use the template immediately": "Enable other members to use the template immediately", "Location": "Location", "Admins can manage the workspace and access billing.": "Admins can manage the workspace and access billing.", "Editors can create, edit, and comment on documents.": "Editors can create, edit, and comment on documents.", "Viewers can only view and comment on documents.": "Viewers can only view and comment on documents.", "Are you sure you want to make {{ userName }} a {{ role }}?": "Are you sure you want to make {{ userName }} a {{ role }}?", "I understand, delete": "<PERSON><PERSON>, ha<PERSON>", "Are you sure you want to permanently delete {{ userName }}? This operation is unrecoverable, consider suspending the user instead.": "Anda yakin ingin menghapus {{ userName }} secara permanen? Operasi ini tidak dapat dipulihkan, sebagai gantinya pertimbangkan untuk menangguhkan pengguna.", "Are you sure you want to suspend {{ userName }}? Suspended users will be prevented from logging in.": "Anda yakin ingin menang<PERSON>kan {{ userName }}? Pengguna yang ditangguhkan akan dicegah untuk masuk.", "New name": "<PERSON>a baru", "Name can't be empty": "<PERSON>a tidak boleh kosong", "Check your email to verify the new address.": "Check your email to verify the new address.", "The email will be changed once verified.": "The email will be changed once verified.", "You will receive an email to verify your new address. It must be unique in the workspace.": "You will receive an email to verify your new address. It must be unique in the workspace.", "A confirmation email will be sent to the new address before it is changed.": "A confirmation email will be sent to the new address before it is changed.", "New email": "New email", "Email can't be empty": "Email can't be empty", "Your import completed": "Your import completed", "Previous match": "Previous match", "Next match": "Next match", "Find and replace": "Find and replace", "Find": "Find", "Match case": "Match case", "Enable regex": "Enable regex", "Replace options": "Replace options", "Replacement": "Replacement", "Replace": "Replace", "Replace all": "Replace all", "Profile picture": "Gambar profil", "Create a new doc": "Buat dokumen baru", "{{ userName }} won't be notified, as they do not have access to this document": "{{ userName }} won't be notified, as they do not have access to this document", "Keep as link": "Keep as link", "Mention": "Mention", "Embed": "Embed", "Add column after": "Add column after", "Add column before": "Add column before", "Add row after": "Add row after", "Add row before": "Add row before", "Align center": "Rata tengah", "Align left": "Rata kiri", "Align right": "<PERSON>a kanan", "Default width": "Default width", "Full width": "Full width", "Bulleted list": "<PERSON><PERSON><PERSON> be<PERSON>oin", "Todo list": "<PERSON>ftar tugas", "Code block": "Blok kode", "Copied to clipboard": "<PERSON><PERSON>in ke papan klip", "Code": "<PERSON><PERSON>", "Comment": "Komentari", "Create link": "<PERSON><PERSON><PERSON> tautan", "Sorry, an error occurred creating the link": "<PERSON><PERSON>, terjadi kesalahan saat membuat tautan", "Create a new child doc": "Create a new child doc", "Delete table": "<PERSON><PERSON> tabel", "Delete file": "Delete file", "Width x Height": "Width x Height", "Download file": "Download file", "Replace file": "Replace file", "Delete image": "Hapus gambar", "Download image": "Unduh gambar", "Replace image": "Ubah gambar", "Italic": "Miring", "Sorry, that link won’t work for this embed type": "<PERSON><PERSON>, tautan itu tidak berfungsi untuk jenis embed ini", "File attachment": "<PERSON><PERSON><PERSON>", "Enter a link": "Enter a link", "Big heading": "<PERSON><PERSON><PERSON>", "Medium heading": "<PERSON><PERSON><PERSON>g", "Small heading": "<PERSON><PERSON><PERSON> k<PERSON>il", "Extra small heading": "Extra small heading", "Heading": "<PERSON><PERSON><PERSON>", "Divider": "<PERSON><PERSON><PERSON><PERSON>", "Image": "Gambar", "Sorry, an error occurred uploading the file": "<PERSON><PERSON>, terjadi kesalahan saat mengunggah gambar", "Write a caption": "<PERSON><PERSON> k<PERSON>", "Info": "Informasi", "Info notice": "Pemberitahuan info", "Link": "Tautan", "Highlight": "<PERSON><PERSON>", "Type '/' to insert": "Ketik '/' untuk menyisi<PERSON>kan", "Keep typing to filter": "Terus mengetik untuk menyaring", "Open link": "<PERSON><PERSON>", "Go to link": "<PERSON><PERSON>", "Sorry, that type of link is not supported": "<PERSON><PERSON>, jenis tautan tersebut tidak didukung", "Ordered list": "<PERSON><PERSON><PERSON> be<PERSON><PERSON>t", "Page break": "<PERSON><PERSON>", "Paste a link": "<PERSON><PERSON><PERSON>", "Paste a {{service}} link…": "<PERSON><PERSON><PERSON> tautan {{service}}…", "Placeholder": "Placeholder", "Quote": "Ku<PERSON><PERSON>", "Remove link": "<PERSON><PERSON>", "Search or paste a link": "Telusuri atau tempel tautan", "Strikethrough": "Dicoret", "Bold": "<PERSON><PERSON>", "Subheading": "Subjudul", "Sort ascending": "Sort ascending", "Sort descending": "Sort descending", "Table": "<PERSON><PERSON>", "Export as CSV": "Export as CSV", "Toggle header": "Toggle header", "Math inline (LaTeX)": "Matematika inline (LaTeX)", "Math block (LaTeX)": "Blok matematika (LaTeX)", "Merge cells": "Merge cells", "Split cell": "Split cell", "Tip": "Tip", "Tip notice": "Pemberitahuan tip", "Warning": "Peringatan", "Warning notice": "Pemberitahuan peringatan", "Success": "<PERSON><PERSON><PERSON>", "Success notice": "Pemberitahuan sukses", "Current date": "Tanggal saat ini", "Current time": "Waktu saat ini", "Current date and time": "<PERSON>gal dan waktu saat ini", "Indent": "Indentasi", "Outdent": "Outdent", "Video": "Video", "None": "None", "Could not import file": "Tidak dapat mengimpor berkas", "Unsubscribed from document": "Unsubscribed from document", "Unsubscribed from collection": "Unsubscribed from collection", "Account": "<PERSON><PERSON><PERSON>", "API & Apps": "API & Apps", "Details": "<PERSON><PERSON><PERSON>", "Security": "<PERSON><PERSON><PERSON>", "Features": "<PERSON><PERSON>", "Members": "Anggota", "Groups": "Grup", "API Keys": "API Keys", "Applications": "Applications", "Shared Links": "<PERSON><PERSON> yang di<PERSON>ikan", "Import": "Impor", "Install": "Install", "Integrations": "Integrasi", "Revoke token": "Cabut token", "Revoke": "<PERSON><PERSON><PERSON>", "Show path to document": "Tampilkan path ke dokumen", "Path to document": "Path ke dokumen", "Group member options": "Opsi anggota grup", "Export collection": "Ekspor koleksi", "Rename": "<PERSON><PERSON>", "Sort in sidebar": "Urutkan di sidebar", "A-Z sort": "A-Z sort", "Z-A sort": "Z-A sort", "Manual sort": "Pengurutan manual", "Comment options": "Opsi komentar", "Show document menu": "Show document menu", "{{ documentName }} restored": "{{ documentName }} restored", "Document options": "Opsi dokumen", "Choose a collection": "<PERSON><PERSON><PERSON>", "Subscription inherited from collection": "Subscription inherited from collection", "Apply template": "Apply template", "Enable embeds": "Aktifkan penyematan", "Export options": "Opsi ekspor", "Group members": "Anggota grup", "Edit group": "<PERSON><PERSON> grup", "Delete group": "Hapus grup", "Group options": "Opsi grup", "Cancel": "<PERSON><PERSON>", "Import menu options": "Import menu options", "Member options": "Opsi anggota", "New document in <em>{{ collectionName }}</em>": "Dokumen baru di <em>{{ collectionName }}</em>", "New child document": "<PERSON><PERSON><PERSON> be<PERSON> baru", "Save in workspace": "Save in workspace", "Notification settings": "Notification settings", "Revoke {{ appName }}": "Revoke {{ appName }}", "Revoking": "Mencabut", "Are you sure you want to revoke access?": "Are you sure you want to revoke access?", "Delete app": "Delete app", "Revision options": "Opsi revisi", "Share link revoked": "Tautan berbagi dicabut", "Share link copied": "<PERSON><PERSON> berbagi disalin", "Share options": "<PERSON><PERSON> bagikan", "Go to document": "<PERSON><PERSON>ju ke dokumen", "Revoke link": "<PERSON><PERSON>", "Contents": "<PERSON><PERSON>", "Headings you add to the document will appear here": "<PERSON><PERSON><PERSON> yang <PERSON>a tambahkan ke dokumen akan muncul di sini", "Table of contents": "<PERSON><PERSON><PERSON> isi", "Change name": "G<PERSON>", "Change email": "Change email", "Suspend user": "Tangguhkan pengguna", "An error occurred while sending the invite": "<PERSON><PERSON><PERSON><PERSON> kesalahan saat mengirim undangan", "User options": "Opsi pengguna", "Change role": "Change role", "Resend invite": "<PERSON><PERSON>", "Revoke invite": "<PERSON><PERSON>", "Activate user": "Activate user", "template": "template", "document": "document", "published": "published", "edited": "diedit", "created the collection": "created the collection", "mentioned you in": "mentioned you in", "left a comment on": "left a comment on", "resolved a comment on": "resolved a comment on", "shared": "shared", "invited you to": "invited you to", "Choose a date": "Choose a date", "API key created. Please copy the value now as it will not be shown again.": "API key created. Please copy the value now as it will not be shown again.", "Scopes": "<PERSON><PERSON><PERSON>", "Space-separated scopes restrict the access of this API key to specific parts of the API. Leave blank for full access": "Space-separated scopes restrict the access of this API key to specific parts of the API. Leave blank for full access", "Expiration": "Expiration", "Never expires": "Never expires", "7 days": "7 days", "30 days": "30 days", "60 days": "60 days", "90 days": "90 days", "Custom": "Custom", "No expiration": "No expiration", "The document archive is empty at the moment.": "<PERSON><PERSON><PERSON> dokumen kosong saat ini.", "Collection menu": "<PERSON><PERSON> k<PERSON>i", "Drop documents to import": "Dokumen untuk diimpor dengan drop", "<em>{{ collectionName }}</em> doesn’t contain any\n                    documents yet.": "<em>{{ collectionName }}</em> belum berisi\n                    dokumen.", "{{ usersCount }} users and {{ groupsCount }} groups with access": "{{ usersCount }} pengguna dan {{ groupsCount }} grup dengan akses", "{{ usersCount }} users and {{ groupsCount }} groups with access_plural": "{{ usersCount }} pengguna dan {{ groupsCount }} grup dengan akses", "{{ usersCount }} users and a group have access": "{{ usersCount }} pengguna dan grup memiliki akses", "{{ usersCount }} users and a group have access_plural": "{{ usersCount }} pengguna dan grup memiliki akses", "{{ usersCount }} users with access": "{{ usersCount }} pengg<PERSON> dengan akses", "{{ usersCount }} users with access_plural": "{{ usersCount }} pengg<PERSON> dengan akses", "{{ groupsCount }} groups with access": "{{ groupsCount }} grup dengan akses", "{{ groupsCount }} groups with access_plural": "{{ groupsCount }} grup dengan akses", "Archived by {{userName}}": "Diars<PERSON><PERSON> oleh {{userName}}", "Sorry, an error occurred saving the collection": "<PERSON><PERSON>, terjadi kes<PERSON>han saat menyimpan koleksi", "Add a description": "Tambahkan keterangan", "Share": "Bagikan", "Overview": "Overview", "Recently updated": "<PERSON><PERSON>", "Recently published": "<PERSON><PERSON>", "Least recently updated": "Baru-baru ini diperbarui", "A–Z": "A–Z", "Signing in": "Sedang masuk", "You can safely close this window once the Outline desktop app has opened": "<PERSON>a dapat menutup jendela ini dengan aman setelah aplikasi desktop Outline dibuka", "Error creating comment": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat membuat komentar", "Add a comment": "Tambahkan komentar", "Add a reply": "<PERSON><PERSON><PERSON> balasan", "Reply": "<PERSON><PERSON>", "Post": "Posting", "Upload image": "Upload image", "No resolved comments": "No resolved comments", "No comments yet": "Belum ada komentar", "New comments": "New comments", "Most recent": "Most recent", "Order in doc": "Order in doc", "Resolved": "Resolved", "Sort comments": "Sort comments", "Show {{ count }} reply": "Show {{ count }} reply", "Show {{ count }} reply_plural": "Show {{ count }} replies", "Error updating comment": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han ketika memperbarui komentar", "Document is too large": "Document is too large", "This document has reached the maximum size and can no longer be edited": "This document has reached the maximum size and can no longer be edited", "Authentication failed": "<PERSON>ten<PERSON><PERSON><PERSON> gagal", "Please try logging out and back in again": "Please try logging out and back in again", "Authorization failed": "<PERSON><PERSON><PERSON><PERSON> gagal", "You may have lost access to this document, try reloading": "You may have lost access to this document, try reloading", "Too many users connected to document": "Too many users connected to document", "Your edits will sync once other users leave the document": "Your edits will sync once other users leave the document", "Server connection lost": "Koneksi server terputus", "Edits you make will sync once you’re online": "<PERSON><PERSON><PERSON> yang <PERSON>a buat akan disinkronkan saat Anda daring", "Document restored": "<PERSON><PERSON><PERSON>", "Images are still uploading.\nAre you sure you want to discard them?": "<PERSON><PERSON><PERSON> ma<PERSON>h <PERSON>.\n<PERSON>pa Anda yakin ingin membatalkannya?", "{{ count }} comment": "{{ count }} komentar", "{{ count }} comment_plural": "{{ count }} komentar", "Viewed by": "<PERSON><PERSON><PERSON>", "only you": "hanya <PERSON>", "person": "orang", "people": "orang", "Last updated": "<PERSON><PERSON><PERSON>", "Type '/' to insert, or start writing…": "Ketik '/' untuk men<PERSON>, atau mulai menulis…", "Hide contents": "Sembunyikan isi", "Show contents": "<PERSON><PERSON><PERSON><PERSON> isi", "available when headings are added": "available when headings are added", "Edit {{noun}}": "Sunting {{noun}}", "Switch to dark": "<PERSON><PERSON><PERSON> ke gelap", "Switch to light": "<PERSON><PERSON><PERSON> ke cerah", "Archived": "Diarsip<PERSON>", "Save draft": "Save draft", "Done editing": "Done editing", "Restore version": "Pulihkan versi", "No history yet": "Belum ada riwayat", "Source": "Source", "Imported from {{ source }}": "Imported from {{ source }}", "Stats": "Statistik", "{{ count }} minute read": "{{ count }} menit membaca", "{{ count }} minute read_plural": "{{ count }} menit membaca", "{{ count }} words": "{{ count }} kata", "{{ count }} words_plural": "{{ count }} kata", "{{ count }} characters": "{{ count }} karakter", "{{ count }} characters_plural": "{{ count }} karakter", "{{ number }} emoji": "{{ number }} emoji", "No text selected": "Tidak ada teks yang dipilih", "{{ count }} words selected": "{{ count }} kata terp<PERSON>h", "{{ count }} words selected_plural": "{{ count }} kata terp<PERSON>h", "{{ count }} characters selected": "{{ count }} ka<PERSON><PERSON> terp<PERSON>h", "{{ count }} characters selected_plural": "{{ count }} ka<PERSON><PERSON> terp<PERSON>h", "Contributors": "Kontributor", "Created": "Dibuat", "Creator": "Pembuat", "Last edited": "<PERSON><PERSON><PERSON> disunting", "Previously edited": "<PERSON><PERSON><PERSON><PERSON> diedit", "No one else has viewed yet": "Belum ada orang lain yang melihat", "Viewed {{ count }} times by {{ teamMembers }} people": "<PERSON><PERSON><PERSON> {{ count }} kali o<PERSON>h {{ teamMembers }} orang", "Viewed {{ count }} times by {{ teamMembers }} people_plural": "<PERSON><PERSON><PERSON> {{ count }} kali o<PERSON>h {{ teamMembers }} orang", "Viewer insights are disabled.": "Viewer insights are disabled.", "Sorry, the last change could not be persisted – please reload the page": "<PERSON><PERSON>, per<PERSON>han terakhir tidak dapat disimpan – muat ulang halaman", "{{ count }} days": "{{ count }} day", "{{ count }} days_plural": "{{ count }} days", "This template will be permanently deleted in <2></2> unless restored.": "Template ini akan dihapus secara permanen di <2></2> kecuali dipulihkan.", "This document will be permanently deleted in <2></2> unless restored.": "Dokumen ini akan dihapus secara permanen di <2></2> kecuali dipulihkan.", "Highlight some text and use the <1></1> control to add placeholders that can be filled out when creating new documents": "Sorot beberapa teks dan gunakan <1></1> kontrol untuk menambahkan placeholder yang dapat diisi saat membuat dokumen baru", "You’re editing a template": "<PERSON>a sedang mengedit template", "Deleted by {{userName}}": "<PERSON><PERSON><PERSON> oleh {{userName}}", "Observing {{ userName }}": "Mengamati {{ userName }}", "Backlinks": "<PERSON><PERSON> balik", "Close": "<PERSON><PERSON><PERSON>", "This document is large which may affect performance": "This document is large which may affect performance", "{{ teamName }} is using {{ appName }} to share documents, please login to continue.": "{{ teamName }} menggunakan {{ appName }} untuk berbagi dokumen, silakan login untuk melanjutkan.", "Are you sure you want to delete the <em>{{ documentTitle }}</em> template?": "Anda yakin ingin menghapus templat <em>{{ documentTitle }}</em>?", "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history</em>.": "<PERSON><PERSON><PERSON><PERSON> Anda yakin? Menghapus dokumen <em>{{ documentTitle }}</em> akan menghapus semua riwayatnya</em>.", "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history and <em>{{ any }} nested document</em>.": "<PERSON><PERSON><PERSON><PERSON> Anda yakin? Menghapus dokumen <em>{{ documentTitle }}</em> akan menghapus semua riwayatnya dan <em>{{ any }} dokumen bersarang</em>.", "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history and <em>{{ any }} nested document</em>._plural": "A<PERSON><PERSON><PERSON> Anda yakin? Menghapus <em>{{ documentTitle }}</em> dokumen akan menghapus semua riwayatnya dan <em>{{ any }} dokumen bersarang</em>.", "If you’d like the option of referencing or restoring the {{noun}} in the future, consider archiving it instead.": "Jika Anda ingin opsi mereferensikan atau memulihkan {{noun}} di masa mendatang, pertimbangkan untuk mengarsipkannya.", "Select a location to move": "Pilih lokasi untuk dipindahkan", "Document moved": "<PERSON><PERSON><PERSON>", "Couldn’t move the document, try again?": "Tidak dapat memindahkan dokumen, coba lagi?", "Move to <em>{{ location }}</em>": "Pindah ke <em>{{ location }}</em>", "Couldn’t create the document, try again?": "Tidak dapat membuat dokumen, coba lagi?", "Document permanently deleted": "<PERSON><PERSON><PERSON> dihapus secara permanen", "Are you sure you want to permanently delete the <em>{{ documentTitle }}</em> document? This action is immediate and cannot be undone.": "<PERSON><PERSON> Anda yakin ingin menghapus dokumen <em>{{ documentTitle }}</em> secara permanen? Tindakan ini langsung dilakukan dan tidak dapat dibatalkan.", "Select a location to publish": "Pilih lokasi untuk dipublikasikan", "Document published": "<PERSON><PERSON><PERSON>", "Couldn’t publish the document, try again?": "Tidak dapat memublikasikan dokumen, coba lagi?", "Publish in <em>{{ location }}</em>": "Publikasikan di <em>{{ location }}</em>", "Search documents": "Telusuri dokumen", "No documents found for your filters.": "Tidak ditemukan hasil untuk filter tersebut.", "You’ve not got any drafts at the moment.": "Anda tidak memiliki draf apa pun saat ini.", "Payment Required": "Payment Required", "No access to this doc": "No access to this doc", "It doesn’t look like you have permission to access this document.": "It doesn’t look like you have permission to access this document.", "Please request access from the document owner.": "Please request access from the document owner.", "Not found": "Not found", "The page you’re looking for cannot be found. It might have been deleted or the link is incorrect.": "The page you’re looking for cannot be found. It might have been deleted or the link is incorrect.", "Offline": "Luring", "We were unable to load the document while offline.": "Kami tidak dapat memuat dokumen saat luring.", "Your account has been suspended": "<PERSON><PERSON><PERSON>a telah disuspen", "Warning Sign": "<PERSON><PERSON>", "A workspace admin (<em>{{ suspendedContactEmail }}</em>) has suspended your account. To re-activate your account, please reach out to them directly.": "A workspace admin (<em>{{ suspendedContactEmail }}</em>) has suspended your account. To re-activate your account, please reach out to them directly.", "Created by me": "Dibuat oleh saya", "Weird, this shouldn’t ever be empty": "<PERSON><PERSON>, ini seharusnya tidak pernah kosong", "You haven’t created any documents yet": "Anda belum membuat dokumen apa pun", "Documents you’ve recently viewed will be here for easy access": "Dokumen yang baru saja Anda lihat akan ada di sini agar mudah diakses", "We sent out your invites!": "<PERSON><PERSON> men<PERSON> undangan Anda!", "Those email addresses are already invited": "Alamat email tersebut sudah diundang", "Sorry, you can only send {{MAX_INVITES}} invites at a time": "<PERSON><PERSON>, <PERSON>a hanya dapat mengirim {{MAX_INVITES}} undangan sekaligus", "Invited {{roleName}} will receive access to": "Invited {{roleName}} will receive access to", "{{collectionCount}} collections": "{{collectionCount}} collections", "Admin": "Admin", "Can manage all workspace settings": "Can manage all workspace settings", "Can create, edit, and delete documents": "Can create, edit, and delete documents", "Can view and comment": "Can view and comment", "Invite people to join your workspace. They can sign in with {{signinMethods}} or use their email address.": "Invite people to join your workspace. They can sign in with {{signinMethods}} or use their email address.", "Invite members to join your workspace. They will need to sign in with {{signinMethods}}.": "Undang anggota untuk bergabung dengan workspace Anda. <PERSON><PERSON>a harus masuk dengan {{signinMethods}}.", "As an admin you can also <2>enable email sign-in</2>.": "Se<PERSON>ai admin, <PERSON><PERSON> j<PERSON> da<PERSON>t <2>mengaktifkan email sign-in</2>.", "Invite as": "Invite as", "Role": "<PERSON><PERSON>", "Email": "<PERSON><PERSON>", "Add another": "Tambah lagi", "Inviting": "Mengundang", "Send Invites": "<PERSON><PERSON>", "Open command menu": "Buka menu perintah", "Forward": "Forward", "Edit current document": "Edit dokumen saat ini", "Move current document": "Pindahkan dokumen saat ini", "Open document history": "Buka riwayat dokumen", "Jump to search": "<PERSON><PERSON><PERSON> ke pencarian", "Jump to home": "<PERSON><PERSON>ju ke home", "Focus search input": "<PERSON>car<PERSON> fokus", "Open this guide": "<PERSON>uka panduan ini", "Enter": "Enter", "Publish document and exit": "Publikasikan dokumen dan keluar", "Save document": "Simpan dokumen", "Cancel editing": "Batalkan penyuntingan", "Collaboration": "Collaboration", "Formatting": "Pemformatan", "Paragraph": "Paragra<PERSON>", "Large header": "<PERSON><PERSON><PERSON>", "Medium header": "<PERSON><PERSON><PERSON>g", "Small header": "<PERSON><PERSON><PERSON> k<PERSON>il", "Underline": "<PERSON><PERSON> bawah", "Undo": "Urungkan", "Redo": "<PERSON><PERSON><PERSON><PERSON>", "Move block up": "Move block up", "Move block down": "Move block down", "Lists": "<PERSON><PERSON><PERSON>", "Toggle task list item": "Toggle task list item", "Tab": "Tab", "Indent list item": "<PERSON><PERSON> daftar indentasi", "Outdent list item": "Item daftar outdent", "Move list item up": "Pindahkan item daftar ke atas", "Move list item down": "Pindahkan item daftar ke bawah", "Tables": "Tables", "Insert row": "Insert row", "Next cell": "Next cell", "Previous cell": "Previous cell", "Space": "Space", "Numbered list": "<PERSON><PERSON><PERSON> berno<PERSON>", "Blockquote": "Ku<PERSON><PERSON>", "Horizontal divider": "Pembagi horizontal", "LaTeX block": "Blok LaTeX", "Inline code": "Kode inline", "Inline LaTeX": "LaTeX Inline", "Triggers": "Triggers", "Mention users and more": "Mention users and more", "Emoji": "<PERSON><PERSON><PERSON>", "Insert block": "Insert block", "Sign In": "<PERSON><PERSON><PERSON>", "Continue with Email": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Continue with {{ authProviderName }}": "<PERSON><PERSON><PERSON><PERSON><PERSON> den<PERSON> {{ authProviderName }}", "Back to home": "<PERSON><PERSON><PERSON> ke beranda", "The workspace could not be found": "The workspace could not be found", "To continue, enter your workspace’s subdomain.": "To continue, enter your workspace’s subdomain.", "subdomain": "subdomain", "Continue": "<PERSON><PERSON><PERSON><PERSON>", "The domain associated with your email address has not been allowed for this workspace.": "Domain yang dikaitkan dengan alamat email Anda belum diizinkan untuk workspace ini.", "Unable to sign-in. Please navigate to your workspace's custom URL, then try to sign-in again.<1></1>If you were invited to a workspace, you will find a link to it in the invite email.": "Tidak dapat masuk. Buka URL khusus workspace Anda, lalu coba masuk lagi.<1></1> Jika Anda diundang ke workspace, <PERSON><PERSON> akan menemukan tautan ke workspace tersebut di email undangan.", "Sorry, a new account cannot be created with a personal Gmail address.<1></1>Please use a Google Workspaces account instead.": "<PERSON><PERSON>, akun baru tidak dapat dibuat dengan alamat Gmail pribadi.<1></1> <PERSON><PERSON> gunakan akun Google Workspaces sebagai gantinya.", "The workspace associated with your user is scheduled for deletion and cannot be accessed at this time.": "The workspace associated with your user is scheduled for deletion and cannot be accessed at this time.", "The workspace you authenticated with is not authorized on this installation. Try another?": "Workspace yang Anda autentikasi tidak diotorisasi pada penginstalan ini. Coba yang lain?", "We could not read the user info supplied by your identity provider.": "Kami tidak dapat membaca info pengguna yang diberikan oleh penyedia identitas Anda.", "Your account uses email sign-in, please sign-in with email to continue.": "<PERSON><PERSON><PERSON> email, harap masuk dengan email untuk melanjutkan.", "An email sign-in link was recently sent, please check your inbox or try again in a few minutes.": "Tautan masuk via email baru saja dikirim, harap periksa kotak masuk Anda atau coba lagi dalam beberapa menit.", "Authentication failed – we were unable to sign you in at this time. Please try again.": "Autentikasi gagal – kami tidak dapat memasukkan Anda saat ini. <PERSON>lakan coba lagi.", "Authentication failed – you do not have permission to access this workspace.": "Otentikasi gagal – Anda tidak memiliki izin untuk mengakses workspace ini.", "Sorry, it looks like that sign-in link is no longer valid, please try requesting another.": "<PERSON><PERSON>, se<PERSON><PERSON><PERSON> tautan masuk itu sudah tidak valid, coba minta yang lain.", "Your account has been suspended. To re-activate your account, please contact a workspace admin.": "<PERSON><PERSON><PERSON>a telah ditangguhkan. Untuk mengaktifkan kembali akun <PERSON>, harap hubungi admin ruang kerja.", "This workspace has been suspended. Please contact support to restore access.": "This workspace has been suspended. Please contact support to restore access.", "Authentication failed – this login method was disabled by a workspace admin.": "Authentication failed – this login method was disabled by a workspace admin.", "The workspace you are trying to join requires an invite before you can create an account.<1></1>Please request an invite from your workspace admin and try again.": "Workspace yang ingin <PERSON>a masuki memer<PERSON>an undangan sebelum Anda dapat membuat akun.<1></1> Harap minta undangan dari admin workspace Anda dan coba lagi.", "Sorry, an unknown error occurred.": "Sorry, an unknown error occurred.", "Choose a workspace": "Choose a workspace", "Choose an {{ appName }} workspace or login to continue connecting this app": "Choose an {{ appName }} workspace or login to continue connecting this app", "Create workspace": "Create workspace", "Setup your workspace by providing a name and details for admin login. You can change these later.": "Setup your workspace by providing a name and details for admin login. You can change these later.", "Workspace name": "Nama Workspace", "Admin name": "Admin name", "Admin email": "Admin email", "Login": "<PERSON><PERSON><PERSON>", "Error": "<PERSON><PERSON><PERSON>", "Failed to load configuration.": "<PERSON><PERSON> memuat kon<PERSON>.", "Check the network requests and server logs for full details of the error.": "Periksa permintaan jaringan dan log server untuk rincian lengkap tentang kesalahan tersebut.", "Custom domain setup": "Setup domain khusus", "Almost there": "<PERSON><PERSON><PERSON>", "Your custom domain is successfully pointing at Outline. To complete the setup process please contact support.": "Domain kustom Anda berhasil diarahkan ke Outline. Untuk menyelesaikan proses pen<PERSON>, silakan hubungi admin support.", "Choose workspace": "Pilih workspace", "This login method requires choosing your workspace to continue": "Metode login ini mengharuskan memilih workspace Anda untuk melanjutkan", "Check your email": "<PERSON><PERSON><PERSON>", "A magic sign-in link has been sent to the email <em>{{ emailLinkSentTo }}</em> if an account exists.": "<PERSON><PERSON> ajaib untuk masuk sudah dikirim ke <em>{{ emailLinkSentTo }}</em> jika akun tersebut ada.", "Back to login": "Ke<PERSON>li ke Login", "Get started by choosing a sign-in method for your new workspace below…": "<PERSON><PERSON><PERSON> dengan memilih metode masuk untuk workspace baru Anda di bawah…", "Login to {{ authProviderName }}": "<PERSON><PERSON><PERSON> ke {{ authProviderName }}", "You signed in with {{ authProviderName }} last time.": "<PERSON>a masuk dengan {{ authProviderName }} te<PERSON><PERSON> kali.", "Or": "<PERSON><PERSON>", "Already have an account? Go to <1>login</1>.": "Sudah punya akun? <1><PERSON><PERSON><PERSON></1>.", "An error occurred": "An error occurred", "The OAuth client could not be found, please check the provided client ID": "The OAuth client could not be found, please check the provided client ID", "The OAuth client could not be loaded, please check the redirect URI is valid": "The OAuth client could not be loaded, please check the redirect URI is valid", "Required OAuth parameters are missing": "Required OAuth parameters are missing", "Authorize": "Authorize", "{{ appName }} wants to access {{ teamName }}": "{{ appName }} wants to access {{ teamName }}", "By <em>{{ developerName }}</em>": "By <em>{{ developerName }}</em>", "{{ appName }} will be able to access your account and perform the following actions": "{{ appName }} will be able to access your account and perform the following actions", "read": "read", "write": "write", "read and write": "read and write", "API keys": "API keys", "attachments": "attachments", "collections": "collections", "comments": "comments", "documents": "documents", "events": "events", "groups": "groups", "integrations": "integrations", "notifications": "notifications", "reactions": "reactions", "pins": "pins", "shares": "shares", "users": "users", "teams": "teams", "workspace": "workspace", "Read all data": "Read all data", "Write all data": "Write all data", "Any collection": "<PERSON><PERSON><PERSON>", "All time": "All time", "Past day": "<PERSON><PERSON><PERSON>", "Past week": "<PERSON><PERSON>", "Past month": "<PERSON><PERSON><PERSON>", "Past year": "<PERSON><PERSON>", "Any time": "<PERSON><PERSON> pun", "Remove document filter": "Remove document filter", "Any status": "Any status", "Remove search": "<PERSON><PERSON>", "Any author": "<PERSON><PERSON><PERSON> penuli<PERSON>", "Search titles only": "<PERSON>i judul saja", "Something went wrong": "Something went wrong", "Please try again or contact support if the problem persists": "Please try again or contact support if the problem persists", "No documents found for your search filters.": "Tidak ada dokumen yang ditemukan untuk filter pencarian Anda.", "Create personal API keys to authenticate with the API and programatically control\n      your workspace's data. For more details see the <em>developer documentation</em>.": "Create personal API keys to authenticate with the API and programatically control\n      your workspace's data. For more details see the <em>developer documentation</em>.", "API keys have been disabled by an admin for your account": "API keys have been disabled by an admin for your account", "Application access": "Application access", "Manage which third-party and internal applications have been granted access to your {{ appName }} account.": "Manage which third-party and internal applications have been granted access to your {{ appName }} account.", "API": "API", "API keys can be used to authenticate with the API and programatically control\n          your workspace's data. For more details see the <em>developer documentation</em>.": "API keys can be used to authenticate with the API and programatically control\n          your workspace's data. For more details see the <em>developer documentation</em>.", "Application published": "Application published", "Application updated": "Application updated", "Client secret rotated": "Client secret rotated", "Rotate secret": "Rotate secret", "Rotating the client secret will invalidate the current secret. Make sure to update any applications using these credentials.": "Rotating the client secret will invalidate the current secret. Make sure to update any applications using these credentials.", "Displayed to users when authorizing": "Displayed to users when authorizing", "Developer information shown to users when authorizing": "Developer information shown to users when authorizing", "Developer name": "Developer name", "Developer URL": "Developer URL", "Allow users from other workspaces to authorize this app": "Allow users from other workspaces to authorize this app", "Credentials": "Credentials", "OAuth client ID": "OAuth client ID", "The public identifier for this app": "The public identifier for this app", "OAuth client secret": "OAuth client secret", "Store this value securely, do not expose it publicly": "Store this value securely, do not expose it publicly", "Where users are redirected after authorizing this app": "Where users are redirected after authorizing this app", "Authorization URL": "Authorization URL", "Where users are redirected to authorize this app": "Where users are redirected to authorize this app", "Applications allow you to build internal or public integrations with Outline and provide secure access via OAuth. For more details see the <em>developer documentation</em>.": "Applications allow you to build internal or public integrations with Outline and provide secure access via OAuth. For more details see the <em>developer documentation</em>.", "by {{ name }}": "by {{ name }}", "Last used": "Last used", "No expiry": "No expiry", "Restricted scope": "Restricted scope", "API key copied to clipboard": "API key copied to clipboard", "Copied": "Di<PERSON>in", "Are you sure you want to revoke the {{ tokenName }} token?": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin mencabut token {{ tokenName }}?", "Disconnect integration": "Disconnect integration", "Connected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Disconnect": "Memutuskan", "Disconnecting": "Disconnecting", "Allowed domains": "Domain yang diizinkan", "The domains which should be allowed to create new accounts using SSO. Changing this setting does not affect existing user accounts.": "Domain yang seharusnya diizinkan untuk membuat akun baru menggunakan SSO. Mengubah setelan ini tidak memengaruhi akun pengguna yang ada.", "Remove domain": "Hapus domain", "Add a domain": "Tambahkan domain", "Save changes": "<PERSON><PERSON><PERSON>", "Please choose a single file to import": "<PERSON><PERSON>an pilih satu berkas untuk diimpor", "Your import is being processed, you can safely leave this page": "Pengimporan Anda sedang diproses, <PERSON><PERSON> dapat mening<PERSON>kan halaman ini dengan aman", "File not supported – please upload a valid ZIP file": "File tidak didukung – harap unggah file ZIP yang valid", "Set the default permission level for collections created from the import": "Set the default permission level for collections created from the import", "Uploading": "Mengunggah", "Start import": "Start import", "Processing": "Memproses", "Expired": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Completed": "Se<PERSON><PERSON>", "Failed": "Gaga<PERSON>", "All collections": "<PERSON><PERSON><PERSON>", "Import deleted": "Import deleted", "Export deleted": "Ekspor dihapus", "Are you sure you want to delete this import?": "Are you sure you want to delete this import?", "Deleting this import will also delete all collections and documents that were created from it. This cannot be undone.": "Deleting this import will also delete all collections and documents that were created from it. This cannot be undone.", "Check server logs for more details.": "Check server logs for more details.", "{{userName}} requested": "{{userName}} meminta", "Groups are for organizing your team. They work best when centered around a function or a responsibility — Support or Engineering for example.": "Grup adalah untuk mengatur tim <PERSON>. Grup bekerja paling baik ketika berpusat di sekitar fungsi atau tanggung jawab — Dukungan atau Teknik misalnya.", "You’ll be able to add people to the group next.": "Anda dapat menambahkan orang ke grup berikutnya.", "You can edit the name of this group at any time, however doing so too often might confuse your team mates.": "<PERSON>a dapat menyunting nama grup ini kapan saja, namun melakukan ini terlalu sering mungkin dapat membuat rekan tim <PERSON>a bin<PERSON>.", "Are you sure about that? Deleting the <em>{{groupName}}</em> group will cause its members to lose access to collections and documents that it is associated with.": "Apa kamu yakin akan hal itu? Menghapus grup <em>{{groupName}}</em> akan menyebabkan anggotanya kehilangan akses ke koleksi dan dokumen yang terkait dengannya.", "Add people to {{groupName}}": "Tambahkan orang ke {{groupName}}", "{{userName}} was removed from the group": "{{userName}} dikeluarkan dari grup", "Add and remove members to the <em>{{groupName}}</em> group. Members of the group will have access to any collections this group has been added to.": "Menambah dan menghapus anggota ke grup <em>{{groupName}}</em>. Anggota grup akan memiliki akses ke koleksi apa pun yang ditambahkan ke grup ini.", "Add people": "Tambahkan orang", "Listing members of the <em>{{groupName}}</em> group.": "Daftar anggota kelompok <em>{{groupName}}</em>.", "This group has no members.": "Grup ini tidak memiliki anggota.", "{{userName}} was added to the group": "{{userName}} ditambahkan ke grup", "Could not add user": "Tidak dapat menambahkan pengguna", "Add members below to give them access to the group. Need to add someone who’s not yet a member?": "Tambahkan anggota di bawah untuk memberi mereka akses ke grup. Perlu menambahkan seseorang yang belum menjadi anggota?", "Invite them to {{teamName}}": "Undang mereka ke {{teamName}}", "Ask an admin to invite them first": "Ask an admin to invite them first", "Search by name": "<PERSON>i dengan nama", "Search people": "<PERSON>i orang", "No people matching your search": "Tak ada orang yang cocok dengan pencarian Anda", "No people left to add": "Tak ada orang lagi yang dapat ditambahkan", "Date created": "Date created", "Crop Image": "Crop Image", "Crop image": "Crop image", "How does this work?": "Bagaimana cara kerjanya?", "You can import a zip file that was previously exported from the JSON option in another instance. In {{ appName }}, open <em>Export</em> in the Settings sidebar and click on <em>Export Data</em>.": "Anda dapat mengimpor file zip yang sebelumnya diekspor dari opsi JSON di contoh lain. Di {{ appName }}, buka <em>Ekspor</em> di sidebar Pengaturan dan klik <em>Ekspor Data</em>.", "Drag and drop the zip file from the JSON export option in {{appName}}, or click to upload": "Drag and drop file zip dari opsi ekspor JSON di {{appName}}, atau klik untuk mengunggah", "Canceled": "Canceled", "Import canceled": "Import canceled", "Are you sure you want to cancel this import?": "Are you sure you want to cancel this import?", "Canceling": "Canceling", "Canceling this import will discard any progress made. This cannot be undone.": "Canceling this import will discard any progress made. This cannot be undone.", "{{ count }} document imported": "{{ count }} document imported", "{{ count }} document imported_plural": "{{ count }} documents imported", "You can import a zip file that was previously exported from an Outline installation – collections, documents, and images will be imported. In Outline, open <em>Export</em> in the Settings sidebar and click on <em>Export Data</em>.": "Anda dapat mengimpor file zip yang sebelumnya diekspor dari penginstalan Outline – kole<PERSON><PERSON>, do<PERSON><PERSON>, dan gambar akan diimpor. Di Outline, buka <em>Export</em> di sidebar Settings dan klik <em>Export Data</em>.", "Drag and drop the zip file from the Markdown export option in {{appName}}, or click to upload": "Drag and drop file zip dari opsi Markdown export di {{appName}}, atau klik untuk mengunggah", "Configure": "Configure", "Connect": "Hubungkan", "Last active": "Terakhir aktif", "Guest": "Guest", "Never used": "Never used", "Are you sure you want to delete the {{ appName }} application? This cannot be undone.": "Are you sure you want to delete the {{ appName }} application? This cannot be undone.", "Shared by": "Shared by", "Date shared": "<PERSON><PERSON>n", "Last accessed": "<PERSON><PERSON><PERSON>", "Domain": "Domain", "Views": "<PERSON><PERSON><PERSON>", "All roles": "All roles", "Admins": "Admin", "Editors": "Editors", "All status": "All status", "Active": "Aktif", "Left": "Left", "Right": "Right", "Settings saved": "Pengaturan disimpan", "Logo updated": "<PERSON><PERSON>", "Unable to upload new logo": "Tidak dapat mengunggah logo baru", "Delete workspace": "Delete workspace", "These settings affect the way that your workspace appears to everyone on the team.": "These settings affect the way that your workspace appears to everyone on the team.", "Display": "Tampilan", "The logo is displayed at the top left of the application.": "Logo ditampilkan di atas kiri aplikasi.", "The workspace name, usually the same as your company name.": "<PERSON><PERSON> workspace, biasanya sama dengan nama per<PERSON>an <PERSON>.", "Description": "Description", "A short description of your workspace.": "A short description of your workspace.", "Theme": "<PERSON><PERSON>", "Customize the interface look and feel.": "Sesuaikan tampilan dan nuansa antarmuka.", "Reset theme": "<PERSON><PERSON> ul<PERSON> tema", "Accent color": "<PERSON><PERSON>", "Accent text color": "Warna teks aksen", "Public branding": "Branding publik", "Show your workspace logo, description, and branding on publicly shared pages.": "Show your workspace logo, description, and branding on publicly shared pages.", "Table of contents position": "Table of contents position", "The side to display the table of contents in relation to the main content.": "The side to display the table of contents in relation to the main content.", "Behavior": "<PERSON><PERSON><PERSON>", "Subdomain": "Subdomain", "Your workspace will be accessible at": "Your workspace will be accessible at", "Choose a subdomain to enable a login page just for your team.": "Pilih subdomain untuk mengaktifkan halaman masuk hanya untuk tim <PERSON>.", "This is the screen that workspace members will first see when they sign in.": "Ini adalah layar yang akan dilihat pertama kali oleh anggota workspace saat mereka masuk.", "Danger": "Danger", "You can delete this entire workspace including collections, documents, and users.": "You can delete this entire workspace including collections, documents, and users.", "Export data": "Ekspor data", "A full export might take some time, consider exporting a single document or collection. You may leave this page once the export has started – if you have notifications enabled, we will email a link to <em>{{ userEmail }}</em> when it’s complete.": "A full export might take some time, consider exporting a single document or collection. You may leave this page once the export has started – if you have notifications enabled, we will email a link to <em>{{ userEmail }}</em> when it’s complete.", "Recent exports": "Ekspor terbaru", "Manage optional and beta features. Changing these settings will affect the experience for all members of the workspace.": "Kelola fitur opsional dan beta. Mengubah pengaturan ini akan memengaruhi pengalaman semua anggota workspace.", "Separate editing": "Separate editing", "When enabled documents have a separate editing mode by default instead of being always editable. This setting can be overridden by user preferences.": "When enabled documents have a separate editing mode by default instead of being always editable. This setting can be overridden by user preferences.", "When enabled team members can add comments to documents.": "<PERSON><PERSON>, anggota tim dapat menambahkan komentar ke dokumen.", "Create a group": "Buat grup", "Could not load groups": "Could not load groups", "New group": "Grup baru", "Groups can be used to organize and manage the people on your team.": "Grup dapat digunakan untuk mengatur dan mengelola orang-orang di tim <PERSON>.", "No groups have been created yet": "Belum ada grup yang dibuat", "Import a zip file of Markdown documents (exported from version 0.67.0 or earlier)": "Impor file zip dokumen Markdown (diekspor dari versi 0.67.0 atau sebelumnya)", "Import data": "Impor data", "Import a JSON data file exported from another {{ appName }} instance": "Impor file data JSON yang diekspor dari instance {{ appName }} lainnya", "Import pages from a Confluence instance": "Impor halaman dari instansi Confluence", "Enterprise": "Enterprise", "Quickly transfer your existing documents, pages, and files from other tools and services into {{appName}}. You can also drag and drop any HTML, Markdown, and text documents directly into Collections in the app.": "Transfer dokumen, halaman, dan file Anda dengan cepat dari alat dan layanan lain ke {{appName}}. Anda juga dapat drag and drop dokumen HTML, <PERSON><PERSON>, dan teks apa pun langsung ke Koleksi di aplikasi.", "Recent imports": "Impor terbaru", "Configure a variety of integrations with third-party services.": "Configure a variety of integrations with third-party services.", "Could not load members": "Could not load members", "Everyone that has signed into {{appName}} is listed here. It’s possible that there are other users who have access through {{signinMethods}} but haven’t signed in yet.": "Everyone that has signed into {{appName}} is listed here. It’s possible that there are other users who have access through {{signinMethods}} but haven’t signed in yet.", "Receive a notification whenever a new document is published": "Terima pemberitahuan setiap kali dokumen baru diterbitkan", "Document updated": "<PERSON><PERSON><PERSON>", "Receive a notification when a document you are subscribed to is edited": "Terima pemberitahuan saat dokumen langganan <PERSON> diedit", "Comment posted": "Komentar diposting", "Receive a notification when a document you are subscribed to or a thread you participated in receives a comment": "Terima pemberitahuan saat dokumen langganan Anda atau utas yang Anda ikuti menerima komentar", "Mentioned": "Di-mention", "Receive a notification when someone mentions you in a document or comment": "Terima pemberitahuan ketika seseorang me-mention <PERSON><PERSON> dalam dokumen atau komentar", "Receive a notification when a comment thread you were involved in is resolved": "Receive a notification when a comment thread you were involved in is resolved", "Collection created": "Koleksi dibuat", "Receive a notification whenever a new collection is created": "Terima pemberitahuan setiap kali koleksi baru dibuat", "Invite accepted": "Undangan diterima", "Receive a notification when someone you invited creates an account": "Terima pemberitahuan saat seseorang yang Anda undang membuat akun", "Invited to document": "Invited to document", "Receive a notification when a document is shared with you": "Receive a notification when a document is shared with you", "Invited to collection": "Invited to collection", "Receive a notification when you are given access to a collection": "Receive a notification when you are given access to a collection", "Export completed": "Ekspor selesai", "Receive a notification when an export you requested has been completed": "Terima pemberitahuan saat ekspor yang Anda minta telah selesai", "Getting started": "<PERSON><PERSON><PERSON>", "Tips on getting started with features and functionality": "Tips untuk memulai dengan fitur dan fungsionalitas", "New features": "<PERSON>tur baru", "Receive an email when new features of note are added": "Terima email saat fitur catatan baru ditambahkan", "Notifications saved": "Notifikasi disimpan", "Unsubscription successful. Your notification settings were updated": "<PERSON><PERSON><PERSON><PERSON> be<PERSON><PERSON><PERSON> be<PERSON>. Pengaturan notifikasi <PERSON>a <PERSON>er<PERSON>", "Manage when and where you receive email notifications.": "<PERSON><PERSON><PERSON> kapan dan di mana Anda mendapat notifikasi surel.", "The email integration is currently disabled. Please set the associated environment variables and restart the server to enable notifications.": "Integrasi email saat ini dinonaktifkan. Harap setel variabel environment terkait dan mulai ulang server untuk mengaktifkan notifikasi.", "Preferences saved": "Preferensi disimpan", "Delete account": "<PERSON><PERSON> akun", "Manage settings that affect your personal experience.": "<PERSON><PERSON><PERSON> pengaturan yang memengaruhi pengalaman pribadi Anda.", "Language": "Bahasa", "Choose the interface language. Community translations are accepted though our <2>translation portal</2>.": "<PERSON><PERSON><PERSON> bahasa antarmuka. Terjemahan komunitas diterima melalui <2>portal terjemahan kami</2> .", "Choose your preferred interface color scheme.": "Choose your preferred interface color scheme.", "Use pointer cursor": "<PERSON><PERSON><PERSON> k<PERSON>", "Show a hand cursor when hovering over interactive elements.": "<PERSON><PERSON><PERSON><PERSON> kursor tangan saat mengarahkan kursor ke elemen interaktif.", "Show line numbers": "<PERSON><PERSON><PERSON><PERSON> nomor baris", "Show line numbers on code blocks in documents.": "<PERSON><PERSON><PERSON>an nomor baris pada blok kode di dokumen.", "When enabled, documents have a separate editing mode. When disabled, documents are always editable when you have permission.": "When enabled, documents have a separate editing mode. When disabled, documents are always editable when you have permission.", "Remember previous location": "Ingat lokasi sebelumnya", "Automatically return to the document you were last viewing when the app is re-opened.": "<PERSON><PERSON><PERSON> otomatis kembali ke dokumen yang terakhir Anda lihat saat aplikasi dibuka kembali.", "Smart text replacements": "Smart text replacements", "Auto-format text by replacing shortcuts with symbols, dashes, smart quotes, and other typographical elements.": "Auto-format text by replacing shortcuts with symbols, dashes, smart quotes, and other typographical elements.", "You may delete your account at any time, note that this is unrecoverable": "<PERSON>a dapat menghapus akun Anda kapan saja, namun ingat bahwa ini tidak dapat dibatalkan", "Profile saved": "Profil disimpan", "Profile picture updated": "Gambar profil dip<PERSON><PERSON><PERSON>", "Unable to upload new profile picture": "Tidak dapat mengunggah gambar profil baru", "Manage how you appear to other members of the workspace.": "<PERSON><PERSON>la bagaimana Anda terlihat oleh anggota lain dari workspace.", "Photo": "Foto", "Choose a photo or image to represent yourself.": "<PERSON><PERSON><PERSON> sebuah foto atau gambar yang mewakili diri Anda.", "This could be your real name, or a nickname — however you’d like people to refer to you.": "Ini bisa berupa nama asli <PERSON>, atau nama panggilan — bagaimanapun Anda ingin orang-orang meru<PERSON>k <PERSON>.", "Email address": "<PERSON><PERSON><PERSON>", "Are you sure you want to require invites?": "Anda yakin ingin mewajibkan undangan?", "New users will first need to be invited to create an account. <em>Default role</em> and <em>Allowed domains</em> will no longer apply.": "Pengguna baru pertama-tama harus diundang untuk membuat akun. <em>Peran default</em> dan <em>Domain yang diizinkan</em> tidak akan berlaku lagi.", "Settings that impact the access, security, and content of your workspace.": "Settings that impact the access, security, and content of your workspace.", "Allow members to sign-in with {{ authProvider }}": "Izinkan anggota untuk masuk dengan {{ authProvider }}", "Disabled": "Dinonaktifkan", "Allow members to sign-in using their email address": "Izinkan anggota untuk masuk menggunakan alamat email mereka", "The server must have SMTP configured to enable this setting": "Server harus memiliki konfigurasi SMTP untuk mengaktifkan setelan ini", "Access": "<PERSON><PERSON><PERSON>", "Allow users to send invites": "Allow users to send invites", "Allow editors to invite other people to the workspace": "Allow editors to invite other people to the workspace", "Require invites": "Memerlukan undangan", "Require members to be invited to the workspace before they can create an account using SSO.": "Wajibkan anggota untuk diundang ke workspace sebelum mereka dapat membuat akun menggunakan SSO.", "Default role": "Peran default", "The default user role for new accounts. Changing this setting does not affect existing user accounts.": "Peran pengguna default untuk akun baru. Mengubah setelan ini tidak memengaruhi akun pengguna yang ada.", "When enabled, documents can be shared publicly on the internet by any member of the workspace": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, dokumen dapat dibagikan secara publik di internet oleh anggota workspace mana pun", "Viewer document exports": "Ekspor dokumen oleh viewer", "When enabled, viewers can see download options for documents": "<PERSON><PERSON> <PERSON><PERSON>, viewer dapat melihat opsi unduhan untuk dokumen", "Users can delete account": "Users can delete account", "When enabled, users can delete their own account from the workspace": "When enabled, users can delete their own account from the workspace", "Rich service embeds": "Embed Rich service", "Links to supported services are shown as rich embeds within your documents": "Tautan ke layanan yang didukung ditampilkan sebagai Rich Embeds dalam dokumen <PERSON>a", "Collection creation": "Pembuatan koleksi", "Allow editors to create new collections within the workspace": "Allow editors to create new collections within the workspace", "Workspace creation": "Workspace creation", "Allow editors to create new workspaces": "Allow editors to create new workspaces", "Could not load shares": "Could not load shares", "Sharing is currently disabled.": "Berbagi saat ini dinonaktifkan.", "You can globally enable and disable public document sharing in the <em>security settings</em>.": "Anda dapat mengaktifkan dan menonaktifkan berbagi dokumen publik secara global di <em>pengaturan keamanan</em>.", "Documents that have been shared are listed below. Anyone that has the public link can access a read-only version of the document until the link has been revoked.": "Dokumen yang telah dibagikan tercantum di bawah ini. Siapa pun yang memiliki tautan publik dapat mengakses dokumen dalam versi read-only hingga tautan dicabut.", "You can create templates to help your team create consistent and accurate documentation.": "Anda dapat membuat template untuk membantu tim Anda membuat dokumentasi yang konsisten dan akurat.", "Alphabetical": "Alfabetis", "There are no templates just yet.": "Belum ada template.", "A confirmation code has been sent to your email address, please enter the code below to permanently destroy this workspace.": "A confirmation code has been sent to your email address, please enter the code below to permanently destroy this workspace.", "Confirmation code": "Confirmation code", "Deleting the <1>{{workspaceName}}</1> workspace will destroy all collections, documents, users, and associated data. You will be immediately logged out of {{appName}}.": "Deleting the <1>{{workspaceName}}</1> workspace will destroy all collections, documents, users, and associated data. You will be immediately logged out of {{appName}}.", "Please note that workspaces are completely separated. They can have a different domain, settings, users, and billing.": "Please note that workspaces are completely separated. They can have a different domain, settings, users, and billing.", "You are creating a new workspace using your current account — <em>{{email}}</em>": "You are creating a new workspace using your current account — <em>{{email}}</em>", "To create a workspace under another email please sign up from the homepage": "To create a workspace under another email please sign up from the homepage", "Trash emptied": "Trash emptied", "Are you sure you want to permanently delete all the documents in Trash? This action is immediate and cannot be undone.": "Are you sure you want to permanently delete all the documents in Trash? This action is immediate and cannot be undone.", "Recently deleted": "Recently deleted", "Trash is empty at the moment.": "Sampah kosong saat ini.", "A confirmation code has been sent to your email address, please enter the code below to permanently destroy your account.": "A confirmation code has been sent to your email address, please enter the code below to permanently destroy your account.", "Are you sure? Deleting your account will destroy identifying data associated with your user and cannot be undone. You will be immediately logged out of {{appName}} and all your API tokens will be revoked.": "Anda yakin? Menghapus akun Anda akan menghancurkan data pengidentifikasi yang terkait dengan pengguna Anda dan tidak dapat dibatalkan. Anda akan segera keluar dari {{appName}} dan semua token API Anda akan dicabut.", "Delete my account": "Delete my account", "Today": "<PERSON> ini", "Yesterday": "<PERSON><PERSON><PERSON>", "Last week": "<PERSON><PERSON>", "This month": "<PERSON><PERSON><PERSON> ini", "Last month": "<PERSON><PERSON><PERSON>", "This year": "<PERSON><PERSON> ini", "Expired yesterday": "Expired yesterday", "Expired {{ date }}": "Expired {{ date }}", "Expires today": "Expires today", "Expires tomorrow": "Expires tomorrow", "Expires {{ date }}": "Expires {{ date }}", "Whoops, you need to accept the permissions in GitHub to connect {{appName}} to your workspace. Try again?": "Whoops, you need to accept the permissions in GitHub to connect {{appName}} to your workspace. Try again?", "Something went wrong while authenticating your request. Please try logging in again.": "Something went wrong while authenticating your request. Please try logging in again.", "The owner of GitHub account has been requested to install the {{githubAppName}} GitHub app. Once approved, previews will be shown for respective links.": "The owner of GitHub account has been requested to install the {{githubAppName}} GitHub app. Once approved, previews will be shown for respective links.", "Enable previews of GitHub issues and pull requests in documents by connecting a GitHub organization or specific repositories to {appName}.": "Enable previews of GitHub issues and pull requests in documents by connecting a GitHub organization or specific repositories to {appName}.", "Enabled by {{integrationCreatedBy}}": "Enabled by {{integrationCreated<PERSON>y}}", "Disconnecting will prevent previewing GitHub links from this organization in documents. Are you sure?": "Disconnecting will prevent previewing GitHub links from this organization in documents. Are you sure?", "The GitHub integration is currently disabled. Please set the associated environment variables and restart the server to enable the integration.": "The GitHub integration is currently disabled. Please set the associated environment variables and restart the server to enable the integration.", "Google Analytics": "Google Analytics", "Add a Google Analytics 4 measurement ID to send document views and analytics from the workspace to your own Google Analytics account.": "Tambahkan ID measurement Google Analytics 4 untuk mengirimkan tampilan dokumen dan analitik dari workspace ke akun Google Analytics Anda sendiri.", "Measurement ID": "ID Measurement", "Create a \"Web\" stream in your Google Analytics admin dashboard and copy the measurement ID from the generated code snippet to install.": "Buat \"Web\" stream di dasbor admin Google Analytics Anda dan salin ID measurement dari snippet kode yang dihasilkan untuk menginstal.", "Whoops, you need to accept the permissions in Linear to connect {{appName}} to your workspace. Try again?": "Whoops, you need to accept the permissions in Linear to connect {{appName}} to your workspace. Try again?", "Something went wrong while processing your request. Please try again.": "Something went wrong while processing your request. Please try again.", "Enable previews of Linear issues in documents by connecting a Linear workspace to {appName}.": "Enable previews of Linear issues in documents by connecting a Linear workspace to {appName}.", "Disconnecting will prevent previewing Linear links from this workspace in documents. Are you sure?": "Disconnecting will prevent previewing Linear links from this workspace in documents. Are you sure?", "The Linear integration is currently disabled. Please set the associated environment variables and restart the server to enable the integration.": "The Linear integration is currently disabled. Please set the associated environment variables and restart the server to enable the integration.", "Configure a Matomo installation to send views and analytics from the workspace to your own Matomo instance.": "Configure a Matomo installation to send views and analytics from the workspace to your own Matomo instance.", "Instance URL": "Instance URL", "The URL of your Matomo instance. If you are using Matomo Cloud it will end in matomo.cloud/": "The URL of your Matomo instance. If you are using Matomo Cloud it will end in matomo.cloud/", "Site ID": "Site ID", "An ID that uniquely identifies the website in your Matomo instance.": "An ID that uniquely identifies the website in your Matomo instance.", "Whoops, you need to accept the permissions in Notion to connect {{ appName }} to your workspace. Try again?": "Whoops, you need to accept the permissions in Notion to connect {{ appName }} to your workspace. Try again?", "Import pages from Notion": "Import pages from Notion", "Add to Slack": "Tambahkan ke Slack", "document published": "dokumen di<PERSON>bit<PERSON>", "document updated": "do<PERSON><PERSON>", "Posting to the <em>{{ channelName }}</em> channel on": "Posting ke saluran <em>{{ channelName }}</em>", "These events should be posted to Slack": "Event ini akan diposting ke Slack", "This will prevent any future updates from being posted to this Slack channel. Are you sure?": "This will prevent any future updates from being posted to this Slack channel. Are you sure?", "Whoops, you need to accept the permissions in Slack to connect {{appName}} to your workspace. Try again?": "Whoops, you need to accept the permissions in Slack to connect {{appName}} to your workspace. Try again?", "Personal account": "Personal account", "Link your {{appName}} account to Slack to enable searching and previewing the documents you have access to, directly within chat.": "Link your {{appName}} account to Slack to enable searching and previewing the documents you have access to, directly within chat.", "Disconnecting your personal account will prevent searching for documents from Slack. Are you sure?": "Disconnecting your personal account will prevent searching for documents from S<PERSON>ck. Are you sure?", "Slash command": "Slash command", "Get rich previews of {{ appName }} links shared in Slack and use the <em>{{ command }}</em> slash command to search for documents without leaving your chat.": "Dapatkan pratinjau kaya dari {{ appName }} tautan yang dibagikan di Slack dan gunakan perintah garis miring <em>{{ command }}</em> untuk mencari dokumen tanpa meninggalkan obrolan Anda.", "This will remove the Outline slash command from your Slack workspace. Are you sure?": "This will remove the Outline slash command from your Slack workspace. Are you sure?", "Connect {{appName}} collections to Slack channels. Messages will be automatically posted to Slack when documents are published or updated.": "Hubungkan {{appName}} koleksi ke saluran Slack. Pesan akan diposting secara otomatis ke Slack saat dokumen diterbitkan atau diperbarui.", "Comment by {{ author }} on \"{{ title }}\"": "Comment by {{ author }} on \"{{ title }}\"", "How to use {{ command }}": "How to use {{ command }}", "To search your workspace use {{ command }}. \nType {{ command2 }} help to display this help text.": "To search your workspace use {{ command }}. \nType {{ command2 }} help to display this help text.", "Post to Channel": "Posting ke Saluran", "This is what we found for \"{{ term }}\"": "Ini yang kami temukan untuk \"{{ term }}\"", "No results for \"{{ term }}\"": "Tidak ada hasil untuk \"{{ term }}\"", "It looks like you haven’t linked your {{ appName }} account to Slack yet": "It looks like you haven’t linked your {{ appName }} account to Slack yet", "Link your account": "Link your account", "Link your account in {{ appName }} settings to search from Slack": "Link your account in {{ appName }} settings to search from Slack", "Configure a Umami installation to send views and analytics from the workspace to your own Umami instance.": "Configure a Umami installation to send views and analytics from the workspace to your own Umami instance.", "The URL of your Umami instance. If you are using Umami Cloud it will begin with {{ url }}": "The URL of your Umami instance. If you are using Umami Cloud it will begin with {{ url }}", "Script name": "Script name", "The name of the script file that Umami uses to track analytics.": "The name of the script file that <PERSON><PERSON> uses to track analytics.", "An ID that uniquely identifies the website in your Umami instance.": "An ID that uniquely identifies the website in your Umami instance.", "Are you sure you want to delete the {{ name }} webhook?": "Anda yakin ingin menghapus {{ name }} webhook?", "Webhook updated": "Webhook diperbarui", "Update": "<PERSON><PERSON><PERSON>", "Updating": "<PERSON><PERSON><PERSON><PERSON>", "Provide a descriptive name for this webhook and the URL we should send a POST request to when matching events are created.": "Berikan nama deskriptif untuk webhook ini dan URL yang harus kami kirimkan POST request saat event yang cocok dibuat.", "A memorable identifer": "<PERSON><PERSON><PERSON> yang mudah di<PERSON>t", "URL": "URL", "Signing secret": "Signing secret", "Subscribe to all events, groups, or individual events. We recommend only subscribing to the minimum amount of events that your application needs to function.": "Subscribe ke semua event, grup, atau event individu. <PERSON><PERSON> hanya Subscribe jumlah minimum event yang dibutuhkan aplikasi Anda untuk berfungsi.", "All events": "Semua event", "All {{ groupName }} events": "Semu<PERSON> {{ groupName }} event", "Delete webhook": "<PERSON><PERSON> webhook", "Subscribed events": "Subscribed event", "Edit webhook": "Mengedit webhook", "Webhook created": "Webhook dibuat", "Webhooks": "Webhook", "New webhook": "Webhook baru", "Webhooks can be used to notify your application when events happen in {{appName}}. Events are sent as a https request with a JSON payload in near real-time.": "Webhook dapat digunakan untuk memberi tahu aplikasi Anda saat peristiwa terjadi di {{appName}}. Event dikirim sebagai permintaan https dengan payload JSON hampir secara real-time.", "Inactive": "Tidak aktif", "Create a webhook": "Buat webhook", "Zapier is a platform that allows {{appName}} to easily integrate with thousands of other business tools. Automate your workflows, sync data, and more.": "Zapier adalah platform yang memungkinkan {{appName}} untuk berintegrasi dengan mudah dengan ribuan alat bisnis lainnya. Otomatiskan alur kerja <PERSON>, sinkronkan data, dan lainnya.", "Never logged in": "Never logged in", "Online now": "Online now", "Online {{ timeAgo }}": "Online {{ timeAgo }}", "Viewed just now": "Viewed just now", "You updated {{ timeAgo }}": "You updated {{ timeAgo }}", "{{ user }} updated {{ timeAgo }}": "{{ user }} updated {{ timeAgo }}", "You created {{ timeAgo }}": "You created {{ timeAgo }}", "{{ user }} created {{ timeAgo }}": "{{ user }} created {{ timeAgo }}", "Error loading data": "Error loading data"}
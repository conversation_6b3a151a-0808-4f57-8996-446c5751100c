{"New API key": "新しい API キー", "Open collection": "コレクションを開く", "New collection": "コレクションを追加", "Create a collection": "コレクションを作成", "Edit": "編集", "Edit collection": "コレクションを編集", "Permissions": "権限", "Collection permissions": "コレクションの権限", "Share this collection": "このコレクションを共有する", "Search in collection": "コレクション内を検索", "Star": "お気に入りに追加", "Unstar": "お気に入りから削除", "Subscribe": "通知を有効にする", "Subscribed to document notifications": "ドキュメントの通知を有効にしました", "Unsubscribe": "通知を無効にする", "Unsubscribed from document notifications": "ドキュメントの通知を無効にしました", "Archive": "アーカイブ", "Archive collection": "コレクションをアーカイブ", "Collection archived": "コレクションをアーカイブしました", "Archiving": "アーカイブ中", "Archiving this collection will also archive all documents within it. Documents from the collection will no longer be visible in search results.": "このコレクションをアーカイブすると、その中のすべてのドキュメントもアーカイブされます。コレクション内のドキュメントは検索結果に表示されなくなります。", "Restore": "復元", "Collection restored": "コレクションが復元されました", "Delete": "削除", "Delete collection": "コレクションを削除", "New template": "新規テンプレート", "Delete comment": "コメントを削除", "Mark as resolved": "解決済みとしてマーク", "Thread resolved": "スレッドは解決されました", "Mark as unresolved": "未解決としてマーク", "View reactions": "リアクションを表示", "Reactions": "リアクション", "Copy ID": "ID をコピー", "Clear IndexedDB cache": "データベースのインデックスを削除する", "IndexedDB cache cleared": "データベースのインデックスを削除しました", "Toggle debug logging": "デバッグログの切り替え", "Debug logging enabled": "デバッグログは有効です", "Debug logging disabled": "デバッグログは無効です", "Development": "開発者メニュー", "Open document": "ドキュメントを開く", "New document": "ドキュメントを作成", "New draft": "新しいドラフト", "New from template": "テンプレートから作成", "New nested document": "このページにドキュメントを作成", "Publish": "公開", "Published {{ documentName }}": "{{ documentName }} は公開されています", "Publish document": "ドキュメントを公開", "Unpublish": "非公開", "Unpublished {{ documentName }}": "{{ documentName }} は非公開です", "Share this document": "ドキュメントを共有する", "HTML": "HTML", "PDF": "PDF", "Exporting": "エクスポート中", "Markdown": "<PERSON><PERSON>", "Download": "ダウンロード", "Download document": "ドキュメントをダウンロード", "Copy as Markdown": "Markdown 形式でコピー", "Markdown copied to clipboard": "Markdown をクリップボードにコピーしました", "Copy as text": "テキストとしてコピーする", "Text copied to clipboard": "テキストをクリップボードにコピーしました", "Copy public link": "公開リンクをコピー", "Link copied to clipboard": "リンクをクリップボードにコピーしました", "Copy link": "リンクをコピー", "Copy": "コピー", "Duplicate": "複製", "Duplicate document": "ドキュメントを複製", "Copy document": "ドキュメントをコピー", "collection": "コレクション", "Pin to {{collectionName}}": "{{collectionName}} にピン留めする", "Pinned to collection": "コレクションにピン留めしました", "Pin to home": "ホームに固定", "Pinned to home": "ホームに固定しました", "Pin": "ピン留め", "Search in document": "ドキュメント内を検索", "Print": "プリント", "Print document": "ドキュメントを印刷する", "Import document": "ドキュメントをインポート", "Templatize": "テンプレート化", "Create template": "テンプレートを作成", "Open random document": "ランダムなドキュメントを開く", "Search documents for \"{{searchQuery}}\"": "\"{{searchQuery}}\" の検索結果", "Move to workspace": "ワークスペースに移動", "Move": "移動", "Move to collection": "コレクションに移動", "Move {{ documentType }}": "{{ documentType }} を移動", "Are you sure you want to archive this document?": "このドキュメントをアーカイブしてもよろしいですか？", "Document archived": "ドキュメントをアーカイブしました", "Archiving this document will remove it from the collection and search results.": "このドキュメントをアーカイブすると、コレクションと検索結果から削除されます。", "Delete {{ documentName }}": "{{ documentName }} を削除します", "Permanently delete": "完全に削除します", "Permanently delete {{ documentName }}": "{{ documentName }} を完全に削除します", "Empty trash": "ごみ箱を空にする", "Permanently delete documents in trash": "ゴミ箱にあるドキュメントを完全に削除する", "Comments": "コメント", "History": "変更履歴", "Insights": "分析", "Disable viewer insights": "ビューアインサイトを無効にする", "Enable viewer insights": "ビューアインサイトを有効にする", "Leave document": "ドキュメントから退出する", "You have left the shared document": "共有ドキュメントから退出しました", "Could not leave document": "ドキュメントから退出することができませんでした", "Home": "ホーム", "Drafts": "下書き", "Search": "検索", "Trash": "ゴミ箱", "Settings": "設定", "Profile": "プロフィール", "Templates": "テンプレート", "Notifications": "通知", "Preferences": "設定", "Documentation": "ドキュメント", "API documentation": "API ドキュメント", "Toggle sidebar": "サイドバーの表示を切り替え", "Send us feedback": "フィードバック", "Report a bug": "バグ報告", "Changelog": "変更履歴", "Keyboard shortcuts": "キーボードショートカット", "Download {{ platform }} app": "{{ platform }} アプリをダウンロード", "Log out": "ログアウト", "Mark notifications as read": "通知を既読済みにする", "Archive all notifications": "全ての通知をアーカイブ", "New App": "New App", "New Application": "New Application", "This version of the document was deleted": "This version of the document was deleted", "Link copied": "リンクをコピーしました", "Dark": "ダークモード", "Light": "ライトモード", "System": "システム", "Appearance": "テーマ", "Change theme": "テーマを変更", "Change theme to": "テーマを変更：", "Switch workspace": "ワークスペースを切り替える", "Select a workspace": "ワークスペースを選択", "New workspace": "新しいワークスペース", "Create a workspace": "ワークスペースを作成", "Login to workspace": "ワークスペースにログイン", "Invite people": "ユーザーを招待", "Invite to workspace": "ワークスペースに招待", "Promote to {{ role }}": "{{ role }} に昇格", "Demote to {{ role }}": "{{ role }} に降格", "Update role": "ロールを更新", "Delete user": "ユーザーを削除", "Collection": "コレクション", "Collections": "コレクション", "Debug": "デバッグ", "Document": "ドキュメント", "Documents": "ドキュメント", "Recently viewed": "閲覧履歴", "Revision": "バージョン", "Navigation": "ナビゲーション", "Notification": "通知", "People": "メンバー", "Workspace": "ワークスペース", "Recent searches": "検索履歴", "currently editing": "編集中", "currently viewing": "閲覧中", "previously edited": "編集済み", "You": "あなた", "Viewers": "閲覧者", "Collections are used to group documents and choose permissions": "コレクションはドキュメントのグループ化や権限の選択に使用されます。", "Name": "名前", "The default access for workspace members, you can share with more users or groups later.": "ワークスペースメンバーのデフォルトのアクセス権です。後から他のユーザーまたはグループと共有することもできます。", "Public document sharing": "公開されているドキュメントを共有", "Allow documents within this collection to be shared publicly on the internet.": "このコレクション内のドキュメントをインターネット上で公開することを許可します。", "Commenting": "コメントの追加", "Allow commenting on documents within this collection.": "Allow commenting on documents within this collection.", "Saving": "保存", "Save": "保存", "Creating": "作成中", "Create": "作成", "Collection deleted": "コレクションを削除しました", "I’m sure – Delete": "完全に削除する", "Deleting": "削除中", "Are you sure about that? Deleting the <em>{{collectionName}}</em> collection is permanent and cannot be restored, however all published documents within will be moved to the trash.": "<em> {{collectionName}} </em> コレクションの削除は永久的で、復元はできません。ただし、コレクション内の全ての公開されたドキュメントはゴミ箱に移動されます。", "Also, <em>{{collectionName}}</em> is being used as the start view – deleting it will reset the start view to the Home page.": "また、<em>{{collectionName}}</em> がホームページとして使用されています – これを削除すると、ホームページはデフォルトのオプションにリセットされます。", "Type a command or search": "コマンドを入力または検索", "Choose a template": "テンプレートを選択", "Are you sure you want to permanently delete this entire comment thread?": "このコメントのスレッド全体を完全に削除してもよろしいですか？", "Are you sure you want to permanently delete this comment?": "このコメントを完全に削除してもよろしいですか？", "Confirm": "確認", "manage access": "アクセスを管理", "view and edit access": "閲覧と編集", "view only access": "閲覧専用", "no access": "アクセス不可", "You do not have permission to move {{ documentName }} to the {{ collectionName }} collection": "{{ documentName }} を {{ collectionName }} コレクションに移動する権限がありません", "Move document": "ドキュメントを移動", "Moving": "移動中", "Moving the document <em>{{ title }}</em> to the {{ newCollectionName }} collection will change permission for all workspace members from <em>{{ prevPermission }}</em> to <em>{{ newPermission }}</em>.": "ドキュメント <em>{{ title }}</em> を {{ newCollectionName }} コレクションに移動すると、 <em>{{ prevPermission }}</em> から <em>{{ newPermission }}</em> へのすべてのワークスペースメンバーの権限が変更されます。", "Submenu": "サブメニュー", "Collections could not be loaded, please reload the app": "コレクションを読み込めませんでした。アプリを再読み込みしてください。", "Default collection": "デフォルトコレクション", "Start view": "起動時の画面", "Install now": "インストールしています", "Deleted Collection": "削除したコレクション", "Untitled": "無題のドキュメント", "Unpin": "ピン留めを外す", "{{ minutes }}m read": "{{ minutes }} ", "Select a location to copy": "コピー先を選択してください", "Document copied": "ドキュメントがコピーされました", "Couldn’t copy the document, try again?": "ドキュメントをコピーできませんでした。もう一度お試しください。", "Include nested documents": "子ドキュメントを含める", "Copy to <em>{{ location }}</em>": "<em>{{ location }}</em> にコピーする", "Search collections & documents": "ドキュメントやコレクションを検索する", "No results found": "なにも見つかりませんでした", "New": "新規作成", "Only visible to you": "あなたにのみ表示されます", "Draft": "下書き", "Template": "テンプレート", "You updated": "あなたが更新しました", "{{ userName }} updated": "{{ userName }} が更新しました", "You deleted": "あなたが削除しました", "{{ userName }} deleted": "{{ userName }} が削除しました", "You archived": "あなたがアーカイブしました", "{{ userName }} archived": "{{ userName }} がアーカイブしました", "Imported": "インポートされました", "You created": "あなたが作成しました", "{{ userName }} created": "{{ userName }} が作成しました", "You published": "あなたが公開しました", "{{ userName }} published": "{{ userName }} が公開しました", "Never viewed": "未読", "Viewed": "閲覧済み", "in": "/", "nested document": "個のドキュメント", "nested document_plural": "個のドキュメント", "{{ total }} task": "{{ total }} 件のタスク", "{{ total }} task_plural": "{{ total }} 件のタスク", "{{ completed }} task done": "{{ completed }} 件のタスクが完了", "{{ completed }} task done_plural": "{{ completed }} 件のタスクが完了", "{{ completed }} of {{ total }} tasks": "{{ completed }} / {{ total }} 件のタスクが完了", "Currently editing": "編集中", "Currently viewing": "閲覧中", "Viewed {{ timeAgo }}": "{{ timeAgo }} 前に閲覧済み", "Module failed to load": "モジュールの読み込みに失敗しました", "Loading Failed": "読み込みに失敗しました", "Sorry, part of the application failed to load. This may be because it was updated since you opened the tab or because of a failed network request. Please try reloading.": "一部のアプリケーションを読み込めませんでした。 再読み込みしてください。", "Reload": "再読み込み", "Something Unexpected Happened": "予期せぬエラーが発生しました", "Sorry, an unrecoverable error occurred{{notified}}. Please try reloading the page, it may have been a temporary glitch.": "回復不能なエラーが発生しました {{notified}}。ページを再読み込みしてください。", "our engineers have been notified": "開発者に通知されました", "Show detail": "詳細を表示", "Revision deleted": "Revision deleted", "Current version": "現在のバージョン", "{{userName}} edited": "{{userName}} が編集しました", "{{userName}} archived": "{{userName}} がアーカイブしました", "{{userName}} restored": "{{userName}} が復元しました", "{{userName}} deleted": "{{userName}} が削除しました", "{{userName}} added {{addedUserName}}": "{{userName}} が {{addedUserName}} を追加しました", "{{userName}} removed {{removedUserName}}": "{{userName}} が {{removedUserName}} を削除しました", "{{userName}} moved from trash": "{{userName}} がゴミ箱から移動しました", "{{userName}} published": "{{userName}} が公開しました", "{{userName}} unpublished": "{{userName}} が非公開にしました", "{{userName}} moved": "{{userName}} が移動しました", "Export started": "エクスポートを開始しました", "Your file will be available in {{ location }} soon": "あなたのファイルは間もなく {{ location }} で利用できます", "View": "表示", "A ZIP file containing the images, and documents in the Markdown format.": "画像、および Markdown 形式のドキュメントを含む ZIP ファイル", "A ZIP file containing the images, and documents as HTML files.": "画像、および HTML 形式のドキュメントを含む ZIP ファイル", "Structured data that can be used to transfer data to another compatible {{ appName }} instance.": "他の {{ appName }} インスタンスにデータをインポートするために使用できるデータ。", "Export": "エクスポート", "Exporting the collection <em>{{collectionName}}</em> may take some time.": "<em>{{collectionName}}</em> のエクスポートには時間がかかる場合があります。", "You will receive an email when it's complete.": "完了するとメールが届きます", "Include attachments": "添付ファイルを含める", "Including uploaded images and files in the exported data": "エクスポートデータにアップロードされた画像とファイルを含む", "{{count}} more user": "{{count}} more user", "{{count}} more user_plural": "{{count}} more users", "Filter": "フィルター", "No results": "検索結果はありません", "{{authorName}} created <3></3>": "{{authorName}} が作成 <3></3>", "{{authorName}} opened <3></3>": "{{authorName}} が開きました<3></3>", "Search emoji": "絵文字を検索", "Search icons": "アイコンを検索", "Choose default skin tone": "デフォルトのスキントーンを選択", "Show menu": "メニューを表示", "Icon Picker": "アイコンを選択", "Icons": "アイコン", "Emojis": "絵文字", "Remove": "削除", "All": "全て", "Frequently Used": "使用頻度の高い項目", "Search Results": "検索結果", "Smileys & People": "表情・人物", "Animals & Nature": "動物・自然", "Food & Drink": "食べ物・飲み物", "Activity": "アクティビティ", "Travel & Places": "旅行・場所", "Objects": "オブジェクト", "Symbols": "シンボル", "Flags": "フラグ", "Select a color": "色を選択", "Loading": "ローディング", "Permission": "権限", "View only": "表示のみ", "Can edit": "編集可能", "No access": "アクセス不可", "Default access": "デフォルトアクセス", "Change Language": "言語を変更", "Dismiss": "却下", "You’re offline.": "オフライン", "Sorry, an error occurred.": "エラーが発生しました", "Click to retry": "再試行", "Back": "戻る", "Unknown": "不明", "Mark all as read": "全て既読にする", "You're all caught up": "全て確認済み", "Icon": "Icon", "My App": "My App", "Tagline": "Tagline", "A short description": "A short description", "Callback URLs": "Callback URLs", "Published": "公開済み", "Allow this app to be installed by other workspaces": "Allow this app to be installed by other workspaces", "{{ username }} reacted with {{ emoji }}": "{{ username }} が {{ emoji }} でリアクション", "{{ firstUsername }} and {{ secondUsername }} reacted with {{ emoji }}": "{{ firstUsername }} と {{ secondUsername }} が {{ emoji }} でリアクション", "{{ firstUsername }} and {{ count }} others reacted with {{ emoji }}": "{{ firstUsername }}と他{{ count }}人が{{ emoji }}で反応した", "{{ firstUsername }} and {{ count }} others reacted with {{ emoji }}_plural": "{{ firstUsername }}と他{{ count }}人が{{ emoji }}で反応した", "Add reaction": "リアクションを追加", "Reaction picker": "リアクションピッカー", "Could not load reactions": "リアクションを読み込めません", "Reaction": "リアクション", "Results": "検索結果", "No results for {{query}}": "{{query}} に該当する検索結果はありませんでした", "Manage": "管理者", "All members": "全員", "Everyone in the workspace": "ワークスペースにいる全ての人", "{{ count }} member": "{{ count }} 人のメンバー", "{{ count }} member_plural": "{{ count }} 人のメンバー", "Invite": "招待", "{{ userName }} was added to the collection": "{{ userName }} をコレクションに追加しました", "{{ count }} people added to the collection": "{{ count }} 人をコレクションに追加しました", "{{ count }} people added to the collection_plural": "{{ count }} 人をコレクションに追加しました", "{{ count }} people and {{ count2 }} groups added to the collection": "{{ count }} 人と {{ count2 }} 個のグループをコレクションに追加しました", "{{ count }} people and {{ count2 }} groups added to the collection_plural": "{{ count }} 人と {{ count2 }} 個のグループをコレクションに追加しました", "Add": "追加", "Add or invite": "追加または招待", "Viewer": "閲覧者", "Editor": "編集者", "Suggestions for invitation": "招待の候補", "No matches": "見つかりませんでした", "Can view": "閲覧可能", "Everyone in the collection": "コレクション内の全てのメンバー", "You have full access": "あなたはフルアクセス権を持っています", "Created the document": "ドキュメントを作成しました", "Other people": "他の人々", "Other workspace members may have access": "他のワークスペースのメンバーがアクセスできる可能性があります", "This document may be shared with more workspace members through a parent document or collection you do not have access to": "アクセスできない親ドキュメントやコレクションを通じて、より多くのワークスペースのメンバーとこのドキュメントを共有できます", "Access inherited from collection": "コレクションから継承したアクセス", "{{ userName }} was removed from the document": "{{ userName }} がドキュメントから削除されました", "Could not remove user": "このユーザーを削除することはできません。", "Permissions for {{ userName }} updated": "{{ userName }} の権限が更新されました", "Could not update user": "ユーザーを更新できませんでした", "Has access through <2>parent</2>": "<2>親ドキュメント</2>を通じてアクセス可能", "Suspended": "保留中", "Invited": "招待済み", "Active <1></1> ago": "<1></1> 前にアクティブ", "Never signed in": "ログインしたことがありません", "Leave": "退出する", "Only lowercase letters, digits and dashes allowed": "小文字、数字、ダッシュのみ使用できます", "Sorry, this link has already been used": "このリンクは既に使用されています", "Public link copied to clipboard": "公開リンクをクリップボードにコピーしました", "Web": "ウェブ", "Anyone with the link can access because the parent document, <2>{{documentTitle}}</2>, is shared": "親ドキュメント <2>{{documentTitle}}</2> が共有されているため、リンクを持っている人は誰でもアクセスできます", "Allow anyone with the link to access": "リンクを持っている誰もがアクセス可能", "Publish to internet": "インターネットに公開する", "Search engine indexing": "検索エンジンのインデックス", "Disable this setting to discourage search engines from indexing the page": "この設定を無効にすると、検索エンジンのインデックス化を防ぐことができます", "Show last modified": "Show last modified", "Display the last modified timestamp on the shared page": "Display the last modified timestamp on the shared page", "Nested documents are not shared on the web. Toggle sharing to enable access, this will be the default behavior in the future": "子ドキュメントは Web で共有されません\nアクセスを有効にするためには共有を切り替えてください\nこれは今後のデフォルトとなります", "{{ userName }} was added to the document": "{{ userName }} をドキュメントに追加しました", "{{ count }} people added to the document": "{{ count }} 人をドキュメントに追加しました", "{{ count }} people added to the document_plural": "{{ count }} 人をドキュメントに追加しました", "{{ count }} groups added to the document": "{{ count }} グループをドキュメントに追加しました", "{{ count }} groups added to the document_plural": "{{ count }} グループをドキュメントに追加しました", "Logo": "ロゴ", "Archived collections": "アーカイブされたコレクション", "New doc": "ドキュメントを新規作成", "Empty": "空", "Collapse": "折りたたむ", "Expand": "展開", "Document not supported – try Markdown, Plain text, HTML, or Word": "ドキュメントはサポートされていません。\nMarkdown、プレーンテキスト、HTML、または Word をお試しください。", "Go back": "戻る", "Go forward": "進む", "Could not load shared documents": "共有済みのドキュメントを読み込めませんでした", "Shared with me": "共有アイテム", "Show more": "さらに表示", "Could not load starred documents": "お気に入りのドキュメントを読み込めませんでした", "Starred": "お気に入り", "Up to date": "最新の状態", "{{ releasesBehind }} versions behind": "最新版から {{ releasesBehind }} バージョン遅れています", "{{ releasesBehind }} versions behind_plural": "最新版から {{ releasesBehind }} バージョン遅れています", "Change permissions?": "権限を変更しますか？", "{{ documentName }} cannot be moved within {{ parentDocumentName }}": "{{ documentName }} は {{ parentDocumentName }} 以内には移動できません", "You can't reorder documents in an alphabetically sorted collection": "五十音順で並べられたコレクション内のドキュメントは、並べ替えができません。", "The {{ documentName }} cannot be moved here": "{{ documentName }} はここに移動できません", "Return to App": "アプリに戻る", "Installation": "インストール", "Unstar document": "お気に入りから削除", "Star document": "お気に入りに追加", "Template created, go ahead and customize it": "テンプレートが作成されました", "Creating a template from <em>{{titleWithDefault}}</em> is a non-destructive action – we'll make a copy of the document and turn it into a template that can be used as a starting point for new documents.": "<em>{{titleWithDefault}}</em>のドキュメントからテンプレートを作成すると、元のドキュメントはコピーされ、テンプレートになります。", "Enable other members to use the template immediately": "他のメンバーがテンプレートをすぐに利用できるようにする", "Location": "場所", "Admins can manage the workspace and access billing.": "管理者はワークスペースと請求書へのアクセスを管理できます", "Editors can create, edit, and comment on documents.": "編集者はドキュメントの作成、編集、およびコメントができます", "Viewers can only view and comment on documents.": "閲覧者はドキュメントの閲覧とコメントが可能です", "Are you sure you want to make {{ userName }} a {{ role }}?": "{{ userName }} を {{ role }} にしてもよろしいですか？", "I understand, delete": "削除する", "Are you sure you want to permanently delete {{ userName }}? This operation is unrecoverable, consider suspending the user instead.": "{{ userName }} を完全に削除してもよろしいですか？\nこの操作を元に戻すことはできません。\n代わりにユーザーを凍結することを検討してください。", "Are you sure you want to suspend {{ userName }}? Suspended users will be prevented from logging in.": "{{ userName }} を凍結してもよろしいですか？ \nこれによりユーザーはログインできなくなります。", "New name": "新しい名前", "Name can't be empty": "名前を空にすることはできません", "Check your email to verify the new address.": "新しいアドレスを確認するためにメールを確認してください。", "The email will be changed once verified.": "確認後、メールアドレスは変更されます。", "You will receive an email to verify your new address. It must be unique in the workspace.": "新しいアドレスを確認するためのメールが届きます。ワークスペース内で同じアドレスである必要があります。", "A confirmation email will be sent to the new address before it is changed.": "変更される前に新しいアドレスに確認メールが送信されます。", "New email": "新しいメールアドレス", "Email can't be empty": "メールアドレスを入力してください", "Your import completed": "インポートが完了しました", "Previous match": "前の一致", "Next match": "次の一致", "Find and replace": "検索と置換", "Find": "検索", "Match case": "大文字と小文字を区別する", "Enable regex": "正規表現を有効にする", "Replace options": "置換オプション", "Replacement": "置換する", "Replace": "置換", "Replace all": "全て置換", "Profile picture": "プロフィール画像", "Create a new doc": "ドキュメントを作成", "{{ userName }} won't be notified, as they do not have access to this document": "{{ userName }} はこのドキュメントへのアクセス権限がないため、通知されません", "Keep as link": "リンクとして保持", "Mention": "メンション", "Embed": "埋め込み", "Add column after": "後ろに列を挿入", "Add column before": "前に列を挿入", "Add row after": "後ろに行を挿入", "Add row before": "前に行を挿入", "Align center": "中央揃え", "Align left": "左揃え", "Align right": "右揃え", "Default width": "デフォルトの幅", "Full width": "左右の余白を縮小", "Bulleted list": "箇条書きリスト", "Todo list": "タスク一覧", "Code block": "コードブロック", "Copied to clipboard": "クリップボードにコピー", "Code": "コード", "Comment": "コメント", "Create link": "リンクを作成", "Sorry, an error occurred creating the link": "リンクの作成に失敗しました", "Create a new child doc": "ドキュメントを作成", "Delete table": "テーブルを削除", "Delete file": "ファイルを削除", "Width x Height": "Width x Height", "Download file": "ファイルをダウンロード", "Replace file": "ファイルを置換", "Delete image": "画像を削除", "Download image": "画像をダウンロード", "Replace image": "画像を置換", "Italic": "斜体", "Sorry, that link won’t work for this embed type": "この埋め込みではリンクが機能しません", "File attachment": "ファイル添付", "Enter a link": "リンクを入力", "Big heading": "見出し1", "Medium heading": "見出し2", "Small heading": "見出し3", "Extra small heading": "追加の小さな見出し", "Heading": "見出し", "Divider": "区切り線", "Image": "画像", "Sorry, an error occurred uploading the file": "ファイルのアップロード中にエラーが発生しました", "Write a caption": "脚注を書く", "Info": "情報", "Info notice": "アラート - INFO", "Link": "リンク", "Highlight": "ハイライト", "Type '/' to insert": "文字を入力するか、「/」でコマンドを呼び出します...", "Keep typing to filter": "入力を続けてフィルターを適用", "Open link": "リンクを開く", "Go to link": "リンクへ移動", "Sorry, that type of link is not supported": "このリンクは現在未対応です", "Ordered list": "番号付きリスト", "Page break": "ページ区切り", "Paste a link": "リンクを貼り付け", "Paste a {{service}} link…": "{{service}} のリンクを貼り付ける", "Placeholder": "プレースホルダー", "Quote": "引用", "Remove link": "リンクを削除", "Search or paste a link": "リンクを検索または貼り付ける", "Strikethrough": "取り消し線", "Bold": "太字", "Subheading": "小見出し", "Sort ascending": "昇順に並べ替え", "Sort descending": "降順にソート", "Table": "表", "Export as CSV": "CSV 形式でエクスポート", "Toggle header": "ヘッダーの切り替え", "Math inline (LaTeX)": "インライン数式 (LaTeX)", "Math block (LaTeX)": "ブロック数式 (LaTeX)", "Merge cells": "Merge cells", "Split cell": "Split cell", "Tip": "ヒント", "Tip notice": "アラート - TIP", "Warning": "警告", "Warning notice": "アラート - WARNING", "Success": "成功しました", "Success notice": "アラート - SUCCESS", "Current date": "現在の日付", "Current time": "現在の時刻", "Current date and time": "現在の日時", "Indent": "インデント", "Outdent": "インデント解除", "Video": "動画", "None": "なし", "Could not import file": "ファイルをインポートできませんでした", "Unsubscribed from document": "ドキュメントの通知を解除しました", "Unsubscribed from collection": "コレクションの登録を解除しました", "Account": "アカウント", "API & Apps": "API & Apps", "Details": "詳細情報", "Security": "セキュリティ", "Features": "機能", "Members": "メンバー", "Groups": "グループ", "API Keys": "APIキー", "Applications": "Applications", "Shared Links": "共有済みのリンク", "Import": "インポート", "Install": "Install", "Integrations": "連携", "Revoke token": "トークンを取り消す", "Revoke": "無効化", "Show path to document": "ドキュメントのパスを表示", "Path to document": "ドキュメントのパス", "Group member options": "グループメンバーオプション", "Export collection": "コレクションをエクスポート", "Rename": "名前を変更", "Sort in sidebar": "サイドバーの並べ替え", "A-Z sort": "A-Z ソート", "Z-A sort": "Z-A ソート", "Manual sort": "手動で並べ替え", "Comment options": "コメントオプション", "Show document menu": "ドキュメントメニューを表示", "{{ documentName }} restored": "{{ documentName }} を復元", "Document options": "ドキュメントオプション", "Choose a collection": "コレクションを選ぶ", "Subscription inherited from collection": "コレクションから継承されたサブスクリプション", "Apply template": "Apply template", "Enable embeds": "埋め込みを有効にする", "Export options": "エクスポートオプション", "Group members": "グループのメンバー", "Edit group": "グループを編集", "Delete group": "グループを削除", "Group options": "グループオプション", "Cancel": "キャンセル", "Import menu options": "インポートメニューオプション", "Member options": "メンバーオプション", "New document in <em>{{ collectionName }}</em>": "<em>{{ collectionName }}</em> にドキュメントを新規作成", "New child document": "新しい子ドキュメント", "Save in workspace": "ワークスペースに保存", "Notification settings": "通知設定", "Revoke {{ appName }}": "Revoke {{ appName }}", "Revoking": "無効化しています", "Are you sure you want to revoke access?": "Are you sure you want to revoke access?", "Delete app": "Delete app", "Revision options": "改訂オプション", "Share link revoked": "共有リンクを無効にしました", "Share link copied": "共有リンクをコピーしました", "Share options": "共有オプション", "Go to document": "ドキュメントに移動", "Revoke link": "リンクを無効化", "Contents": "目次", "Headings you add to the document will appear here": "ドキュメントに追加した見出しがここに表示されます", "Table of contents": "目次", "Change name": "名前を変更", "Change email": "メールアドレスを変更する", "Suspend user": "ユーザーを凍結", "An error occurred while sending the invite": "招待を送信中にエラーが発生しました", "User options": "ユーザーオプション", "Change role": "ロールを変更", "Resend invite": "招待を再送信", "Revoke invite": "招待を取り消す", "Activate user": "ユーザーを凍結解除", "template": "テンプレート", "document": "ドキュメント", "published": "公開済み", "edited": "編集済み", "created the collection": "コレクションを作成しました", "mentioned you in": "あなたがメンションされました", "left a comment on": "がコメントを残しました", "resolved a comment on": "コメントを解決しました：", "shared": "共有済み", "invited you to": "あなたを招待しました", "Choose a date": "日付を選択", "API key created. Please copy the value now as it will not be shown again.": "API キーが作成されました。再度表示されないため、今すぐ値をコピーしてください。", "Scopes": "スコープ", "Space-separated scopes restrict the access of this API key to specific parts of the API. Leave blank for full access": "スペースで区切られたスコープは、この API キーのアクセスを特定の部分に制限します。フルアクセスするには空白のままにしてください。", "Expiration": "有効期限", "Never expires": "無期限", "7 days": "7日", "30 days": "30日", "60 days": "60日", "90 days": "90日", "Custom": "カスタム", "No expiration": "無期限", "The document archive is empty at the moment.": "アーカイブにはなにもありません。", "Collection menu": "コレクションメニュー", "Drop documents to import": "インポートするドキュメントをドロップ", "<em>{{ collectionName }}</em> doesn’t contain any\n                    documents yet.": "<em>{{ collectionName }}</em> にはまだ\n                    ドキュメントがありません。", "{{ usersCount }} users and {{ groupsCount }} groups with access": "アクセス権を持つ {{ usersCount }} 人のユーザーと {{ groupsCount }} 個のグループ", "{{ usersCount }} users and {{ groupsCount }} groups with access_plural": "アクセス権を持つ {{ usersCount }} 人のユーザーと {{ groupsCount }} 個のグループ", "{{ usersCount }} users and a group have access": "アクセス権を持つ {{ usersCount }} 人のユーザーと1つのグループ", "{{ usersCount }} users and a group have access_plural": "アクセス権を持つ {{ usersCount }} 人のユーザーと1つのグループ", "{{ usersCount }} users with access": "アクセス権を持つ {{ usersCount }} 人のユーザー", "{{ usersCount }} users with access_plural": "アクセス権を持つ {{ usersCount }} 人のユーザー", "{{ groupsCount }} groups with access": "アクセス権を持つ {{ groupsCount }} 個のグループ", "{{ groupsCount }} groups with access_plural": "アクセス権を持つ {{ groupsCount }} 個のグループ", "Archived by {{userName}}": "{{userName}} がアーカイブしました", "Sorry, an error occurred saving the collection": "コレクションの保存中にエラーが発生しました", "Add a description": "説明文を追加", "Share": "共有", "Overview": "概要", "Recently updated": "更新日の新しい順", "Recently published": "最近公開されたもの", "Least recently updated": "更新日の古い順", "A–Z": "A-Z", "Signing in": "ログイン中", "You can safely close this window once the Outline desktop app has opened": "Outline デスクトップ アプリが開いたら、このウィンドウを安全に閉じることができます", "Error creating comment": "コメントの作成中にエラーが発生しました", "Add a comment": "コメントを追加", "Add a reply": "返信を追加", "Reply": "返信する", "Post": "投稿する", "Upload image": "画像をアップロード", "No resolved comments": "解決済みのコメントはありません", "No comments yet": "コメントはありません", "New comments": "新しいコメント", "Most recent": "最新", "Order in doc": "ドキュメント内の順序", "Resolved": "解決済み", "Sort comments": "コメントを並べ替える", "Show {{ count }} reply": "{{ count }} 件の返信を表示", "Show {{ count }} reply_plural": "{{ count }} 件の返信を表示", "Error updating comment": "コメントの更新中にエラーが発生しました", "Document is too large": "ドキュメントが大きすぎます", "This document has reached the maximum size and can no longer be edited": "このドキュメントは最大容量に達しており、これ以上編集できません", "Authentication failed": "認証に失敗しました", "Please try logging out and back in again": "ログアウトして再度ログインをお試しください", "Authorization failed": "認証に失敗しました", "You may have lost access to this document, try reloading": "このドキュメントへのアクセス権を失った可能性があります。再読み込みしてください。", "Too many users connected to document": "ドキュメントを閲覧しているユーザーが多すぎます", "Your edits will sync once other users leave the document": "他のユーザーがドキュメントを離れると、あなたの編集内容が同期されます。", "Server connection lost": "サーバーへの接続が失われました", "Edits you make will sync once you’re online": "編集内容はオンラインになると同期されます", "Document restored": "ドキュメントが復元されました", "Images are still uploading.\nAre you sure you want to discard them?": "画像はまだアップロード中です\nこの操作を取り消しますか？", "{{ count }} comment": "{{ count }} 件のコメント", "{{ count }} comment_plural": "{{ count }} 件のコメント", "Viewed by": "閲覧者", "only you": "自分のみ", "person": "個人", "people": "メンバー", "Last updated": "最終更新", "Type '/' to insert, or start writing…": "「/」で挿入、または書き込みを開始…", "Hide contents": "コンテンツを非表示", "Show contents": "コンテンツを表示", "available when headings are added": "見出しが追加されると利用可能です", "Edit {{noun}}": "{{noun}} を編集", "Switch to dark": "ダークテーマに切り替え", "Switch to light": "ライトテーマに切り替え", "Archived": "アーカイブ", "Save draft": "下書きを保存", "Done editing": "編集完了", "Restore version": "復元", "No history yet": "履歴はありません", "Source": "ソース", "Imported from {{ source }}": "{{ source }} からインポートされました", "Stats": "統計", "{{ count }} minute read": "{{ count }} 分で読めます", "{{ count }} minute read_plural": "{{ count }} 分で読めます", "{{ count }} words": "{{ count }} 単語", "{{ count }} words_plural": "{{ count }} 単語", "{{ count }} characters": "{{ count }} 文字", "{{ count }} characters_plural": "{{ count }} 文字", "{{ number }} emoji": "{{ number }} 個の絵文字", "No text selected": "テキストが選択されていません", "{{ count }} words selected": "{{ count }} 語選択", "{{ count }} words selected_plural": "{{ count }} 語選択", "{{ count }} characters selected": "{{ count }} 文字選択", "{{ count }} characters selected_plural": "{{ count }} 文字選択", "Contributors": "編集者", "Created": "作成日時", "Creator": "作成者", "Last edited": "最終更新", "Previously edited": "編集済み", "No one else has viewed yet": "まだ誰も見ていません", "Viewed {{ count }} times by {{ teamMembers }} people": "{{ teamMembers }} 人が {{ count }} 回閲覧", "Viewed {{ count }} times by {{ teamMembers }} people_plural": "{{ teamMembers }} 人が {{ count }} 回閲覧", "Viewer insights are disabled.": "ビューアインサイトは無効です", "Sorry, the last change could not be persisted – please reload the page": "申し訳ありませんが、最後の変更を保持できませんでした。\nページをリロードしてください。", "{{ count }} days": "{{ count }} 日", "{{ count }} days_plural": "{{ count }} 日", "This template will be permanently deleted in <2></2> unless restored.": "このテンプレートは復元されない限り、<2></2>で永久に削除されます。", "This document will be permanently deleted in <2></2> unless restored.": "このドキュメントは復元されない限り、<2></2> 後に、永久に削除されます。", "Highlight some text and use the <1></1> control to add placeholders that can be filled out when creating new documents": "このテンプレートを使用して新しいドキュメントを作成する際に埋められる要素を追加するには、<1></1> キーを使用してください。", "You’re editing a template": "テンプレートを編集しています", "Deleted by {{userName}}": "{{userName}} が削除しました", "Observing {{ userName }}": "{{ userName }} を観察中", "Backlinks": "ハイパーリンク", "Close": "閉じる", "This document is large which may affect performance": "This document is large which may affect performance", "{{ teamName }} is using {{ appName }} to share documents, please login to continue.": "{{ teamName }} は {{ appName }} を使用してドキュメントを共有しています。続けるにはログインしてください。", "Are you sure you want to delete the <em>{{ documentTitle }}</em> template?": "<em>{{ documentTitle }}</em> を削除してもよろしいですか？", "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history</em>.": "<em>{{ documentTitle }}</em>ドキュメントを削除すると、その履歴もすべて削除されます。このまま続けますか？", "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history and <em>{{ any }} nested document</em>.": "<em>{{ documentTitle }}</em> ドキュメントを削除すると、その履歴と <em>ドキュメント内の{{ any }} </em>も全て削除されます。よろしいですか？", "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history and <em>{{ any }} nested document</em>._plural": "<em>{{ documentTitle }}</em> ドキュメントを削除すると、その履歴と <em>ネストされた{{ any }} のドキュメント</em>が全て削除されます。よろしいですか？", "If you’d like the option of referencing or restoring the {{noun}} in the future, consider archiving it instead.": "将来的に {{noun}} の参照または復元が必要になる可能性がある場合は、代わりにアーカイブすることを検討してください。", "Select a location to move": "移動先を選択してください", "Document moved": "ドキュメントが移動されました", "Couldn’t move the document, try again?": "ドキュメントを移動できませんでした。もう一度お試しください。", "Move to <em>{{ location }}</em>": "<em>{{ location }}</em> に移動する", "Couldn’t create the document, try again?": "ドキュメントを作成できませんでした。もう一度やり直してください。", "Document permanently deleted": "ドキュメントが完全に削除されました", "Are you sure you want to permanently delete the <em>{{ documentTitle }}</em> document? This action is immediate and cannot be undone.": "<em>{{ documentTitle }}</em> を完全に削除してもよろしいですか？この操作は即時に反映され、元に戻すことはできません。", "Select a location to publish": "公開する場所を選択してください", "Document published": "ドキュメントが公開されました", "Couldn’t publish the document, try again?": "ドキュメントを公開できませんでした、もう一度お試しください。", "Publish in <em>{{ location }}</em>": "<em>{{ location }}</em> に公開", "Search documents": "ドキュメントの検索", "No documents found for your filters.": "検索結果はありませんでした。", "You’ve not got any drafts at the moment.": "下書きはありません", "Payment Required": "支払いが必要です", "No access to this doc": "このドキュメントにアクセスできません", "It doesn’t look like you have permission to access this document.": "このドキュメントにアクセスする権限がないようです。", "Please request access from the document owner.": "Please request access from the document owner.", "Not found": "見つかりません", "The page you’re looking for cannot be found. It might have been deleted or the link is incorrect.": "お探しのページが見つかりません。削除されたか、リンクが正しくありません。", "Offline": "オフライン", "We were unable to load the document while offline.": "インターネットに接続していない状態でドキュメントを読み込むことができません。", "Your account has been suspended": "あなたのアカウントは凍結されています。", "Warning Sign": "警告", "A workspace admin (<em>{{ suspendedContactEmail }}</em>) has suspended your account. To re-activate your account, please reach out to them directly.": "ワークスペース管理者 (<em>{{ suspendedContactEmail }}</em>) はあなたのアカウントを凍結しました。\nアカウントを再度有効化するには、管理者に直接連絡してください。", "Created by me": "自分が作成", "Weird, this shouldn’t ever be empty": "ここには何もありません", "You haven’t created any documents yet": "ここにはまだドキュメントがありません", "Documents you’ve recently viewed will be here for easy access": "最近閲覧したドキュメントがここに表示されます", "We sent out your invites!": "招待を送信しました！", "Those email addresses are already invited": "これらのメールアドレスは、すでに招待されています。", "Sorry, you can only send {{MAX_INVITES}} invites at a time": "一度に送信できる招待は最大 {{MAX_INVITES}} 人までです。", "Invited {{roleName}} will receive access to": "招待された {{roleName}} は次にアクセスできます", "{{collectionCount}} collections": "{{collectionCount}} 個のコレクション", "Admin": "管理者", "Can manage all workspace settings": "全てのワークスペースの設定が可能", "Can create, edit, and delete documents": "ドキュメントの作成、編集、削除が可能", "Can view and comment": "閲覧とコメントが可能", "Invite people to join your workspace. They can sign in with {{signinMethods}} or use their email address.": "メンバーやゲストをワークスペースに招待します。 {{signinMethods}} でログインするか、メール アドレスを使用できます。", "Invite members to join your workspace. They will need to sign in with {{signinMethods}}.": "メンバーをワークスペースに招待します。 {{signinMethods}} でログインする必要があります。", "As an admin you can also <2>enable email sign-in</2>.": "管理者として、<2>メールによるログインを有効にする</2>ことも可能です。", "Invite as": "次の内容で招待", "Role": "ロール", "Email": "メール", "Add another": "追加", "Inviting": "招待中", "Send Invites": "招待を送信する", "Open command menu": "コマンドメニューを開く", "Forward": "進む", "Edit current document": "ドキュメントを編集", "Move current document": "現在のドキュメントを移動する", "Open document history": "ドキュメント履歴を開く", "Jump to search": "検索へ移動", "Jump to home": "ホームへ移動", "Focus search input": "検索窓にフォーカスする", "Open this guide": "ガイドを開く", "Enter": "エンター", "Publish document and exit": "ドキュメントを公開して終了", "Save document": "ドキュメントを保存", "Cancel editing": "編集をキャンセル", "Collaboration": "コラボレーション", "Formatting": "書式設定", "Paragraph": "段落", "Large header": "大見出し", "Medium header": "中見出し", "Small header": "小見出し", "Underline": "下線", "Undo": "元に戻す", "Redo": "やり直し", "Move block up": "Move block up", "Move block down": "Move block down", "Lists": "リスト", "Toggle task list item": "タスクリスト項目を切り替える", "Tab": "タブ", "Indent list item": "リスト項目をインデント", "Outdent list item": "リスト項目をインデント解除", "Move list item up": "リスト項目を上に移動", "Move list item down": "リスト項目を下に移動", "Tables": "テーブル", "Insert row": "行の挿入", "Next cell": "次のセル", "Previous cell": "前のセル", "Space": "スペース", "Numbered list": "順序付きのリスト", "Blockquote": "引用", "Horizontal divider": "区切り線", "LaTeX block": "LaTeX ブロック", "Inline code": "インラインコード", "Inline LaTeX": "インライン LaTeX", "Triggers": "トリガー", "Mention users and more": "ユーザーなどをメンションする", "Emoji": "絵文字", "Insert block": "ブロックの挿入", "Sign In": "ログイン", "Continue with Email": "Eメール でログイン", "Continue with {{ authProviderName }}": "{{ authProviderName }} でログイン", "Back to home": "ホームへ戻る", "The workspace could not be found": "ワークスペースが見つかりませんでした", "To continue, enter your workspace’s subdomain.": "続行するには、ワークスペースのサブドメインを入力してください。", "subdomain": "サブドメイン", "Continue": "次へ", "The domain associated with your email address has not been allowed for this workspace.": "メールアドレスに関連付けられたドメインは、このワークスペースで許可されていません。", "Unable to sign-in. Please navigate to your workspace's custom URL, then try to sign-in again.<1></1>If you were invited to a workspace, you will find a link to it in the invite email.": "ログインできません。ワークスペースのカスタム URL に移動し、再度ログインをお試しください。<1></1>ワークスペースに招待された場合、招待メールにリンクが表示されます。", "Sorry, a new account cannot be created with a personal Gmail address.<1></1>Please use a Google Workspaces account instead.": "個人のGmailアドレスで新しいアカウントを作成することはできません。\n<1></1>代わりにGoogle Workspace のアカウントを使用してください。", "The workspace associated with your user is scheduled for deletion and cannot be accessed at this time.": "ユーザーに関連付けられたワークスペースは削除予定であり、現時点ではアクセスできません", "The workspace you authenticated with is not authorized on this installation. Try another?": "認証したワークスペースは、権限がありません。別のワークスペースでお試しください。", "We could not read the user info supplied by your identity provider.": "ID プロバイダーから提供されたユーザー情報を読み取ることができませんでした。", "Your account uses email sign-in, please sign-in with email to continue.": "あなたのアカウントはメールによるログインを使用しています。続行するにはメールでログインしてください。", "An email sign-in link was recently sent, please check your inbox or try again in a few minutes.": "メールにログインリンクが送信されました。受信トレイを確認するか、数分後にもう一度お試しください。", "Authentication failed – we were unable to sign you in at this time. Please try again.": "ログインに失敗しました。もう一度試してください。", "Authentication failed – you do not have permission to access this workspace.": "認証に失敗しました - このワークスペースにアクセスする権限がありません。", "Sorry, it looks like that sign-in link is no longer valid, please try requesting another.": "このログインリンクは無効になりました。別のリンクを発行してください。", "Your account has been suspended. To re-activate your account, please contact a workspace admin.": "あなたのアカウントは凍結されています。アカウントを再度有効にするには、ワークスペース管理者にお問い合わせください。", "This workspace has been suspended. Please contact support to restore access.": "このワークスペースは凍結されています。アクセスを復元するにはサポートにお問い合わせください。", "Authentication failed – this login method was disabled by a workspace admin.": "認証に失敗しました – このログイン方法はワークスペース管理者によって無効化されています。", "The workspace you are trying to join requires an invite before you can create an account.<1></1>Please request an invite from your workspace admin and try again.": "アカウントを作成するには、参加しようとしているワークスペースの招待が必要です。 <1></1>ワークスペース管理者に招待をリクエストしてから、もう一度お試しください。", "Sorry, an unknown error occurred.": "申し訳ありません、不明なエラーが発生しました。", "Choose a workspace": "Choose a workspace", "Choose an {{ appName }} workspace or login to continue connecting this app": "Choose an {{ appName }} workspace or login to continue connecting this app", "Create workspace": "Create workspace", "Setup your workspace by providing a name and details for admin login. You can change these later.": "Setup your workspace by providing a name and details for admin login. You can change these later.", "Workspace name": "ワークスペース名", "Admin name": "Admin name", "Admin email": "Admin email", "Login": "ログイン", "Error": "エラー", "Failed to load configuration.": "構成の読み込みに失敗しました", "Check the network requests and server logs for full details of the error.": "エラーの詳細については、サーバーログとネットワークリクエストを確認してください。", "Custom domain setup": "独自ドメインでセットアップ", "Almost there": "もう少しで完了です", "Your custom domain is successfully pointing at Outline. To complete the setup process please contact support.": "カスタムドメインは正常に Outline を認識しています。セットアッププロセスを完了するには、サポートにお問い合わせください。", "Choose workspace": "ワークスペースを選択", "This login method requires choosing your workspace to continue": "現在のログイン方法では、ワークスペースを選択して続行する必要があります", "Check your email": "メールをご確認ください", "A magic sign-in link has been sent to the email <em>{{ emailLinkSentTo }}</em> if an account exists.": "<em>{{ emailLinkSentTo }}</em>が登録されている場合、そのメールアドレスにログイン用の URL が送信されました。", "Back to login": "ログイン画面に戻る", "Get started by choosing a sign-in method for your new workspace below…": "新しいワークスペースのログイン方法を以下から選択して始めましょう…", "Login to {{ authProviderName }}": "{{ authProviderName }} でログイン", "You signed in with {{ authProviderName }} last time.": "前回は {{ authProviderName }} でログインしました", "Or": "または", "Already have an account? Go to <1>login</1>.": "すでにアカウントをお持ちですか？<1>ログイン</1>", "An error occurred": "An error occurred", "The OAuth client could not be found, please check the provided client ID": "The OAuth client could not be found, please check the provided client ID", "The OAuth client could not be loaded, please check the redirect URI is valid": "The OAuth client could not be loaded, please check the redirect URI is valid", "Required OAuth parameters are missing": "Required OAuth parameters are missing", "Authorize": "Authorize", "{{ appName }} wants to access {{ teamName }}": "{{ appName }} wants to access {{ teamName }}", "By <em>{{ developerName }}</em>": "By <em>{{ developerName }}</em>", "{{ appName }} will be able to access your account and perform the following actions": "{{ appName }} will be able to access your account and perform the following actions", "read": "read", "write": "write", "read and write": "read and write", "API keys": "API keys", "attachments": "attachments", "collections": "collections", "comments": "comments", "documents": "documents", "events": "events", "groups": "groups", "integrations": "integrations", "notifications": "notifications", "reactions": "reactions", "pins": "pins", "shares": "shares", "users": "users", "teams": "teams", "workspace": "workspace", "Read all data": "Read all data", "Write all data": "Write all data", "Any collection": "コレクション", "All time": "全期間", "Past day": "一日前", "Past week": "直近1週間", "Past month": "直近1ヶ月", "Past year": "直近1年", "Any time": "全期間", "Remove document filter": "フィルターを解除する", "Any status": "全てのステータス", "Remove search": "検索履歴を削除", "Any author": "全ての作成者", "Search titles only": "タイトルのみ検索", "Something went wrong": "エラーが発生しました", "Please try again or contact support if the problem persists": "問題が解決しない場合は、もう一度やり直すか、サポートにお問い合わせください", "No documents found for your search filters.": "検索条件に該当するドキュメントは見つかりませんでした", "Create personal API keys to authenticate with the API and programatically control\n      your workspace's data. For more details see the <em>developer documentation</em>.": "Create personal API keys to authenticate with the API and programatically control\n      your workspace's data. For more details see the <em>developer documentation</em>.", "API keys have been disabled by an admin for your account": "API keys have been disabled by an admin for your account", "Application access": "Application access", "Manage which third-party and internal applications have been granted access to your {{ appName }} account.": "Manage which third-party and internal applications have been granted access to your {{ appName }} account.", "API": "API", "API keys can be used to authenticate with the API and programatically control\n          your workspace's data. For more details see the <em>developer documentation</em>.": "API キーを使用すると、API で認証し、\n         ワークスペースのデータをプログラムで制御できます。詳細については、<em>開発者向けドキュメント</em>を参照してください。", "Application published": "Application published", "Application updated": "Application updated", "Client secret rotated": "Client secret rotated", "Rotate secret": "Rotate secret", "Rotating the client secret will invalidate the current secret. Make sure to update any applications using these credentials.": "Rotating the client secret will invalidate the current secret. Make sure to update any applications using these credentials.", "Displayed to users when authorizing": "Displayed to users when authorizing", "Developer information shown to users when authorizing": "Developer information shown to users when authorizing", "Developer name": "Developer name", "Developer URL": "Developer URL", "Allow users from other workspaces to authorize this app": "Allow users from other workspaces to authorize this app", "Credentials": "Credentials", "OAuth client ID": "OAuth client ID", "The public identifier for this app": "The public identifier for this app", "OAuth client secret": "OAuth client secret", "Store this value securely, do not expose it publicly": "Store this value securely, do not expose it publicly", "Where users are redirected after authorizing this app": "Where users are redirected after authorizing this app", "Authorization URL": "Authorization URL", "Where users are redirected to authorize this app": "Where users are redirected to authorize this app", "Applications allow you to build internal or public integrations with Outline and provide secure access via OAuth. For more details see the <em>developer documentation</em>.": "Applications allow you to build internal or public integrations with Outline and provide secure access via OAuth. For more details see the <em>developer documentation</em>.", "by {{ name }}": "{{ name }}", "Last used": "最終使用日", "No expiry": "無期限", "Restricted scope": "制限付きスコープ", "API key copied to clipboard": "API キーをクリップボードにコピーしました", "Copied": "コピーしました", "Are you sure you want to revoke the {{ tokenName }} token?": "トークン {{ tokenName }} を失効します\nよろしいですか？", "Disconnect integration": "連携を解除", "Connected": "接続されました", "Disconnect": "接続を解除", "Disconnecting": "切断中", "Allowed domains": "許可されているドメイン", "The domains which should be allowed to create new accounts using SSO. Changing this setting does not affect existing user accounts.": "SSO を使用して新しいアカウントを作成できるドメイン。この設定を変更しても、既存のユーザーには影響しません。", "Remove domain": "ドメインを削除", "Add a domain": "ドメインを追加", "Save changes": "変更を保存", "Please choose a single file to import": "インポートするファイルを選択してください", "Your import is being processed, you can safely leave this page": "インポートしています\nこのページを閉じても問題ありません", "File not supported – please upload a valid ZIP file": "非対応ファイル - 有効な ZIP ファイルをアップロードしてください", "Set the default permission level for collections created from the import": "インポートから作成されたコレクションについて、デフォルトの権限レベルを設定する", "Uploading": "アップロード中", "Start import": "インポートを開始", "Processing": "処理中", "Expired": "期限切れ", "Completed": "完了", "Failed": "失敗", "All collections": "全てのコレクション", "Import deleted": "インポートを削除しました", "Export deleted": "エクスポート削除", "Are you sure you want to delete this import?": "本当にこのインポートを削除してよろしいですか？", "Deleting this import will also delete all collections and documents that were created from it. This cannot be undone.": "このインポートを削除すると、そこから作成された全てのコレクションとドキュメントも削除されます。元に戻すことはできません。", "Check server logs for more details.": "詳細はサーバーログをご確認ください", "{{userName}} requested": "{{userName}} がリクエスト", "Groups are for organizing your team. They work best when centered around a function or a responsibility — Support or Engineering for example.": "グループはユーザーを整理するためのものです。例えば「サポート」と「開発」のように、ユーザーの機能や責任を基に分けると上手くいくでしょう。", "You’ll be able to add people to the group next.": "このあと、ユーザーを追加できます。", "You can edit the name of this group at any time, however doing so too often might confuse your team mates.": "グループの名前はいつでも編集できますが、頻繁に編集するとチームメイトが混乱する可能性があります。", "Are you sure about that? Deleting the <em>{{groupName}}</em> group will cause its members to lose access to collections and documents that it is associated with.": "グループ「<em>{{groupName}}</em>」 を削除するとそのメンバーは、関連付けられている全てのコレクションとドキュメントにアクセスできなくなります。削除しますか？", "Add people to {{groupName}}": "ユーザーを {{groupName}} に追加", "{{userName}} was removed from the group": "{{userName}} をグループから除外しました", "Add and remove members to the <em>{{groupName}}</em> group. Members of the group will have access to any collections this group has been added to.": "<em>{{groupName}}</em> グループにメンバーを追加および削除します。グループのメンバーは、このグループが追加された全てのコレクションにアクセスできます。", "Add people": "ユーザーを追加", "Listing members of the <em>{{groupName}}</em> group.": "<em>{{groupName}}</em> のメンバーリスト", "This group has no members.": "このグループにはメンバーがいません", "{{userName}} was added to the group": "{{userName}} がグループに追加されました。", "Could not add user": "ユーザーを追加できませんでした", "Add members below to give them access to the group. Need to add someone who’s not yet a member?": "以下のメンバーを追加して、グループへのアクセスを許可します。", "Invite them to {{teamName}}": "{{team<PERSON>ame}} に招待する", "Ask an admin to invite them first": "管理者に招待するよう依頼する", "Search by name": "名前で探す", "Search people": "メンバーを検索", "No people matching your search": "検索に一致する人は見つかりませんでした", "No people left to add": "追加できる人は存在しません", "Date created": "作成日時", "Crop Image": "Crop Image", "Crop image": "画像を切り取る", "How does this work?": "これはどのように機能しますか?", "You can import a zip file that was previously exported from the JSON option in another instance. In {{ appName }}, open <em>Export</em> in the Settings sidebar and click on <em>Export Data</em>.": "以前にエクスポートした zip ファイルをインポートできます。 {{ appName }} で、サイドバーの <em>エクスポート画面</em> を開き、 <em>データのエクスポート</em> をクリックします。", "Drag and drop the zip file from the JSON export option in {{appName}}, or click to upload": "{{appName}} の JSON エクスポートオプションから zip ファイルをドラッグ&ドロップするか、クリックしてアップロードします", "Canceled": "キャンセルしました", "Import canceled": "インポートをキャンセルしました", "Are you sure you want to cancel this import?": "インポートをキャンセルしても良いですか？", "Canceling": "キャンセル中", "Canceling this import will discard any progress made. This cannot be undone.": "インポートをキャンセルすると進捗状況は破棄されます。元に戻すことはできません。", "{{ count }} document imported": "{{ count }} ドキュメントがインポートされました", "{{ count }} document imported_plural": "{{ count }} ドキュメントがインポートされました", "You can import a zip file that was previously exported from an Outline installation – collections, documents, and images will be imported. In Outline, open <em>Export</em> in the Settings sidebar and click on <em>Export Data</em>.": "以前、 Outline からエクスポートされた zip ファイルをインポートできます。コレクション、ドキュメント、および画像がインポートされます。 設定から、 <em>エクスポート画面</em> を開き、 <em>データのエクスポート</em>をクリックします。", "Drag and drop the zip file from the Markdown export option in {{appName}}, or click to upload": "{{appName}} の Markdown エクスポートオプションから zip ファイルをドラッグ&ドロップするか、クリックしてアップロードします", "Configure": "Configure", "Connect": "接続", "Last active": "最終ログイン", "Guest": "ゲスト", "Never used": "Never used", "Are you sure you want to delete the {{ appName }} application? This cannot be undone.": "Are you sure you want to delete the {{ appName }} application? This cannot be undone.", "Shared by": "共有者", "Date shared": "共有日", "Last accessed": "最終アクセス", "Domain": "ドメイン", "Views": "閲覧数", "All roles": "全てのロール", "Admins": "管理者", "Editors": "編集者", "All status": "全てのステータス", "Active": "アクティブ", "Left": "左", "Right": "右", "Settings saved": "設定が保存されました", "Logo updated": "ロゴが更新されました", "Unable to upload new logo": "ロゴをアップロードできませんでした", "Delete workspace": "ワークスペースを削除", "These settings affect the way that your workspace appears to everyone on the team.": "これらの設定は、ワークスペースがチームの全員にどのように表示されるかに影響します。", "Display": "表示", "The logo is displayed at the top left of the application.": "アプリの左上にロゴが表示されます。", "The workspace name, usually the same as your company name.": "チーム名です。これは通常、会社名と同じです。", "Description": "Description", "A short description of your workspace.": "A short description of your workspace.", "Theme": "テーマ", "Customize the interface look and feel.": "インターフェースのテーマをカスタマイズします", "Reset theme": "テーマをリセット", "Accent color": "アクセントカラー", "Accent text color": "アクセント文字の色", "Public branding": "ブランディング", "Show your workspace logo, description, and branding on publicly shared pages.": "Show your workspace logo, description, and branding on publicly shared pages.", "Table of contents position": "目次位置", "The side to display the table of contents in relation to the main content.": "目次を表示する場所", "Behavior": "動作", "Subdomain": "サブドメイン", "Your workspace will be accessible at": "あなたのワークスペースは次のドメインでアクセスできます", "Choose a subdomain to enable a login page just for your team.": "サブドメインを選択して、チーム専用のログインページを有効にします。", "This is the screen that workspace members will first see when they sign in.": "これは、ワークスペース メンバーが最初にログインしたときに表示される画面です。", "Danger": "危険", "You can delete this entire workspace including collections, documents, and users.": "コレクション、ドキュメント、およびユーザーを含むワークスペース全体を削除できます。", "Export data": "データのエクスポート", "A full export might take some time, consider exporting a single document or collection. You may leave this page once the export has started – if you have notifications enabled, we will email a link to <em>{{ userEmail }}</em> when it’s complete.": "完全なデータをエクスポートするには、時間がかかることがあります。代わりに、単一のドキュメントまたはコレクションをエクスポートすることを検討してください。処理が開始された後、このウェブページを閉じることができます。通知を有効にした場合、エクスポートが完了すると、Url が <em>{{ userEmail }}</em> にメールされます。", "Recent exports": "最近のエクスポート", "Manage optional and beta features. Changing these settings will affect the experience for all members of the workspace.": "オプション機能とベータ機能を管理します。これらの設定を変更すると、ワークスペースの全てのメンバーのエクスペリエンスに影響します。", "Separate editing": "閲覧モードで開く", "When enabled documents have a separate editing mode by default instead of being always editable. This setting can be overridden by user preferences.": "有効にすると、ドキュメントは常に編集できる状態で開かれる代わりに、閲覧モードで開かれます。 編集をするには、画面上部の編集ボタンを押す必要があります。\nこの設定はユーザー設定によって上書きできます。", "When enabled team members can add comments to documents.": "有効にすると、チームメンバーはドキュメントにコメントを追加できます。", "Create a group": "グループを作成", "Could not load groups": "グループを読み込めませんでした", "New group": "新規グループ", "Groups can be used to organize and manage the people on your team.": "グループを使用して、チームのメンバーを整理および管理できます。", "No groups have been created yet": "グループはまだ作成されていません", "Import a zip file of Markdown documents (exported from version 0.67.0 or earlier)": "Markdown ドキュメント (バージョン 0.67.0 以前からエクスポート) の zip ファイルをインポートする", "Import data": "データのインポート", "Import a JSON data file exported from another {{ appName }} instance": "別の {{ appName }} インスタンスからエクスポートされた JSON データファイルをインポートする", "Import pages from a Confluence instance": "Confluence からページをインポートする", "Enterprise": "エンタープライズ", "Quickly transfer your existing documents, pages, and files from other tools and services into {{appName}}. You can also drag and drop any HTML, Markdown, and text documents directly into Collections in the app.": "既存のドキュメント、ページ、ファイルを他のツールやサービスから {{appName}} にすばやく転送します。 HTML、Markdown、およびドキュメントをアプリ内のコレクションに直接ドラッグ&ドロップすることもできます。", "Recent imports": "最近のインポート", "Configure a variety of integrations with third-party services.": "Configure a variety of integrations with third-party services.", "Could not load members": "メンバーを読み込めませんでした", "Everyone that has signed into {{appName}} is listed here. It’s possible that there are other users who have access through {{signinMethods}} but haven’t signed in yet.": "{{appName}} にログインした全員が表示されます。\n{{signinMethods}} でアクセスできるものの、まだログインしていないユーザーが他にもいる可能性があります。", "Receive a notification whenever a new document is published": "新しいドキュメントが公開されたときに通知を受け取る", "Document updated": "ドキュメントの更新", "Receive a notification when a document you are subscribed to is edited": "通知を有効にしているドキュメントが編集されたときに通知を受け取る", "Comment posted": "投稿されたコメント", "Receive a notification when a document you are subscribed to or a thread you participated in receives a comment": "通知を有効にしているドキュメントや参加したスレッドにコメントされたときに通知を受け取る", "Mentioned": "メンション", "Receive a notification when someone mentions you in a document or comment": "誰かがあなたをドキュメントやコメントでメンションしたときに通知を受け取る", "Receive a notification when a comment thread you were involved in is resolved": "参加したコメントスレッドが解決されたときに通知を受け取る", "Collection created": "コレクションが作成されました", "Receive a notification whenever a new collection is created": "新しいコレクションが作成されたときに通知を受け取る", "Invite accepted": "招待が承認されました", "Receive a notification when someone you invited creates an account": "招待したユーザーがアカウントを作成したときに通知を受け取る", "Invited to document": "ドキュメントに招待済み", "Receive a notification when a document is shared with you": "ドキュメントが共有されたときに通知を受け取る", "Invited to collection": "コレクションに招待済み", "Receive a notification when you are given access to a collection": "コレクションへのアクセスが付与されたときに通知を受け取る", "Export completed": "エクスポート完了", "Receive a notification when an export you requested has been completed": "エクスポートが完了したときに通知を受け取る", "Getting started": "はじめに", "Tips on getting started with features and functionality": "機能を使いこなすためのヒント", "New features": "新機能", "Receive an email when new features of note are added": "注目の新機能が追加されたら、メールでお知らせします", "Notifications saved": "通知設定を保存しました", "Unsubscription successful. Your notification settings were updated": "通知を解除しました。", "Manage when and where you receive email notifications.": "メール通知を管理する", "The email integration is currently disabled. Please set the associated environment variables and restart the server to enable notifications.": "現在、メールとの連携は無効になっています。\n通知を有効にするためには、関連する環境変数を正しく設定し、サーバーを再起動してください。", "Preferences saved": "設定を保存しました", "Delete account": "アカウントの削除", "Manage settings that affect your personal experience.": "個人的な体験を設定します。", "Language": "言語", "Choose the interface language. Community translations are accepted though our <2>translation portal</2>.": "表示言語を選択します。\n<2>翻訳ポータル</2>を通じて翻訳コミュニティに参加できます。", "Choose your preferred interface color scheme.": "テーマを選択します。", "Use pointer cursor": "ポインター カーソルを使用する", "Show a hand cursor when hovering over interactive elements.": "インタラクティブな要素にカーソルを合わせると、ハンドカーソルが表示されます。", "Show line numbers": "行番号を表示", "Show line numbers on code blocks in documents.": "ドキュメント内のコードブロックに行番号を表示します。", "When enabled, documents have a separate editing mode. When disabled, documents are always editable when you have permission.": "有効にすると、ドキュメントは編集のできない閲覧モードで開かれます。編集をするには、画面上部の編集ボタンを押す必要があります。\n無効にすると、許可がある場合は常に編集できる状態で開かれます。", "Remember previous location": "前回開いていた場所を記憶する", "Automatically return to the document you were last viewing when the app is re-opened.": "アプリを再度開いたときに、最後に表示していたドキュメントを自動的に表示します。", "Smart text replacements": "スマートテキストの置換", "Auto-format text by replacing shortcuts with symbols, dashes, smart quotes, and other typographical elements.": "ショートカットをシンボル、ダッシュ、スマート引用符、その他の組版要素に置き換えることでテキストを自動的に書式設定します。", "You may delete your account at any time, note that this is unrecoverable": "アカウントはいつでも削除することができます。これは取り返しのつかないことです。", "Profile saved": "プロフィールを保存しました", "Profile picture updated": "プロフィール画像の更新に成功しました", "Unable to upload new profile picture": "プロフィール画像をアップロードできませんでした", "Manage how you appear to other members of the workspace.": "他のメンバーに自分がどのように見えるかを管理します。", "Photo": "画像", "Choose a photo or image to represent yourself.": "あなたに関連した画像を選択してください", "This could be your real name, or a nickname — however you’d like people to refer to you.": "あなたとわかる名前にしましょう", "Email address": "メールアドレス", "Are you sure you want to require invites?": "招待を要求してもよろしいですか？", "New users will first need to be invited to create an account. <em>Default role</em> and <em>Allowed domains</em> will no longer apply.": "新しいユーザーは、アカウントを作成するために招待される必要があります。 <em>デフォルトのロール</em> および <em>許可されたドメイン</em> は適用されなくなります。", "Settings that impact the access, security, and content of your workspace.": "ワークスペースのアクセス、セキュリティ、およびコンテンツに影響する設定。", "Allow members to sign-in with {{ authProvider }}": "メンバーが {{ authProvider }} でログインできるようにする", "Disabled": "無効化済み", "Allow members to sign-in using their email address": "メンバーが自分のメール アドレスを使用してログインできるようにする", "The server must have SMTP configured to enable this setting": "この設定を有効にするには、サーバーに SMTP が設定されている必要があります。", "Access": "アクセス", "Allow users to send invites": "ユーザーが招待を送信できるようにする", "Allow editors to invite other people to the workspace": "編集者が他の人をワークスペースに招待できるようにする", "Require invites": "招待が必要", "Require members to be invited to the workspace before they can create an account using SSO.": "メンバーは SSO を使用してアカウントを作成する前に、ワークスペースに招待される必要があります。", "Default role": "デフォルトのロール", "The default user role for new accounts. Changing this setting does not affect existing user accounts.": "新規アカウントのデフォルトのユーザーロールです。この設定を変更しても、既存のユーザーアカウントには影響しません。", "When enabled, documents can be shared publicly on the internet by any member of the workspace": "この設定を有効にすると、チームメンバーなら誰でもインターネット上でドキュメントを公開することができます。", "Viewer document exports": "閲覧者によるドキュメントのエクスポート", "When enabled, viewers can see download options for documents": "有効にすると、閲覧者はドキュメントのダウンロードオプションを表示できます", "Users can delete account": "ユーザーはアカウントを削除できます。", "When enabled, users can delete their own account from the workspace": "有効にすると、ユーザーはワークスペースから自分のアカウントを削除できます。", "Rich service embeds": "リッチサービスの埋め込み", "Links to supported services are shown as rich embeds within your documents": "サポートされた URL は、ドキュメント内に埋め込みとして表示されます。", "Collection creation": "コレクションを作成", "Allow editors to create new collections within the workspace": "編集者がワークスペース内にコレクションを作成することを許可する", "Workspace creation": "ワークスペースの作成", "Allow editors to create new workspaces": "編集者にワークスペースの作成を許可します", "Could not load shares": "共有を読み込めませんでした", "Sharing is currently disabled.": "共有は現在無効になっています", "You can globally enable and disable public document sharing in the <em>security settings</em>.": "<em>セキュリティ設定</em> で公開ドキュメントの共有をグローバルに有効/無効にすることができます。", "Documents that have been shared are listed below. Anyone that has the public link can access a read-only version of the document until the link has been revoked.": "共有済みのドキュメントは以下のとおりです。\n公開リンクを知っている人は誰でも、リンクが無効になるまでドキュメントの読み取り専用バージョンにアクセスできます。", "You can create templates to help your team create consistent and accurate documentation.": "チームが一貫性のある正確なドキュメントを作成するのに役立つテンプレートを作成できます。", "Alphabetical": "アルファベット順", "There are no templates just yet.": "テンプレートが見つかりません。", "A confirmation code has been sent to your email address, please enter the code below to permanently destroy this workspace.": "登録されたメールアドレスに認証用のコードが送信されました。ワークスペースを永久に削除するには、メール内のコードを入力してください。", "Confirmation code": "認証コード", "Deleting the <1>{{workspaceName}}</1> workspace will destroy all collections, documents, users, and associated data. You will be immediately logged out of {{appName}}.": "<1>{{workspaceName}}</1> ワークスペースを削除すると、全てのコレクション、ドキュメント、ユーザー、および関連データが破棄されます。 {{appName}} からすぐにログアウトされます。", "Please note that workspaces are completely separated. They can have a different domain, settings, users, and billing.": "ワークスペースは完全に分離されているため、異なるドメイン・設定・ユーザー・請求書を利用することができます。", "You are creating a new workspace using your current account — <em>{{email}}</em>": "現在のアカウントを使用して新しいワークスペースを作成しています — <em>{{email}}</em>", "To create a workspace under another email please sign up from the homepage": "別のメールアドレスでワークスペースを作成するには、ホームページからアカウントを作成してください", "Trash emptied": "ごみ箱を空にしました", "Are you sure you want to permanently delete all the documents in Trash? This action is immediate and cannot be undone.": "ゴミ箱にある全てのドキュメントを完全に削除してもよろしいですか？\nこの操作を元に戻すことはできません", "Recently deleted": "最近削除したドキュメント", "Trash is empty at the moment.": "ゴミ箱は空です", "A confirmation code has been sent to your email address, please enter the code below to permanently destroy your account.": "認証用のコードがメールアドレスに送信されました。認証コードを入力してアカウントを削除してください。", "Are you sure? Deleting your account will destroy identifying data associated with your user and cannot be undone. You will be immediately logged out of {{appName}} and all your API tokens will be revoked.": "アカウントを削除すると、あなたのユーザーに関連付けられている識別データが破壊され、元に戻すことはできません。すぐに {{appName}} からログアウトされ、全てのAPIトークンが無効になります。よろしいですか？", "Delete my account": "アカウントを削除", "Today": "今日", "Yesterday": "昨日", "Last week": "先週", "This month": "今月", "Last month": "先月", "This year": "今年", "Expired yesterday": "昨日で期限切れ", "Expired {{ date }}": "{{ date }} で期限切れ", "Expires today": "今日で期限切れ", "Expires tomorrow": "明日で期限切れ", "Expires {{ date }}": "期限: {{ date }}", "Whoops, you need to accept the permissions in GitHub to connect {{appName}} to your workspace. Try again?": "{{appName}} をワークスペースに接続するには、GitHub で権限を許可する必要があります。もう一度試しますか？", "Something went wrong while authenticating your request. Please try logging in again.": "リクエストの認証中に問題が発生しました。もう一度ログインしてください。", "The owner of GitHub account has been requested to install the {{githubAppName}} GitHub app. Once approved, previews will be shown for respective links.": "GitHub アカウントの所有者は GitHub App {{githubAppName}} をインストールするようにリクエストされています。承認すると、それぞれのリンクにプレビューが表示されます。", "Enable previews of GitHub issues and pull requests in documents by connecting a GitHub organization or specific repositories to {appName}.": "GitHub の組織または特定のリポジトリを {appName} に接続し、ドキュメントで GitHub の Issues と Pull Requests のプレビューを有効にします", "Enabled by {{integrationCreatedBy}}": "{{integrationCreatedBy}} によって有効化", "Disconnecting will prevent previewing GitHub links from this organization in documents. Are you sure?": "連携を解除すると、GitHub のリンクをドキュメントでプレビューできなくなります。よろしいですか？", "The GitHub integration is currently disabled. Please set the associated environment variables and restart the server to enable the integration.": "現在、GitHub との連携は無効になっています。\n関連する環境変数を正しく設定し、サーバーを再起動すると有効になります。", "Google Analytics": "Google Analytics", "Add a Google Analytics 4 measurement ID to send document views and analytics from the workspace to your own Google Analytics account.": "Google Analytics 4 measurement ID を追加すると、Google Analytics アカウントにドキュメントの閲覧数と分析を送信します。", "Measurement ID": "測定 ID", "Create a \"Web\" stream in your Google Analytics admin dashboard and copy the measurement ID from the generated code snippet to install.": "Google Analytics 管理ダッシュボードに「Web」ストリームを作成し、生成されたコードスニペットから測定 ID をコピーしてインストールします。", "Whoops, you need to accept the permissions in Linear to connect {{appName}} to your workspace. Try again?": "{{appName}} をワークスペースに接続するには、Linear で権限を許可する必要があります。もう一度試しますか？", "Something went wrong while processing your request. Please try again.": "Something went wrong while processing your request. Please try again.", "Enable previews of Linear issues in documents by connecting a Linear workspace to {appName}.": "Linear ワークスペースを {appName} に接続することで、ドキュメントの Linear 課題のプレビューを有効にします。", "Disconnecting will prevent previewing Linear links from this workspace in documents. Are you sure?": "連携を解除すると、Linear のリンクをドキュメントでプレビューできなくなります。よろしいですか？", "The Linear integration is currently disabled. Please set the associated environment variables and restart the server to enable the integration.": "現在、Linear との連携は無効になっています。\n関連する環境変数を正しく設定し、サーバーを再起動すると有効になります。", "Configure a Matomo installation to send views and analytics from the workspace to your own Matomo instance.": "あなたのインスタンスにワークスペースのビューと分析を送信するために、Matomo インスタンスを構成します", "Instance URL": "インスタンスURL", "The URL of your Matomo instance. If you are using Matomo Cloud it will end in matomo.cloud/": "Matomo インスタンスの URL\nMatomo クラウドを利用している場合の URL は matomo.cloud/ で終わります。", "Site ID": "サイト ID", "An ID that uniquely identifies the website in your Matomo instance.": "<PERSON><PERSON> インスタンスでウェブサイトを一意に識別する ID", "Whoops, you need to accept the permissions in Notion to connect {{ appName }} to your workspace. Try again?": "{{ appName }} をワークスペースに接続するには、Notionで権限を許可する必要があります。もう一度試しますか？", "Import pages from Notion": "Notion からページをインポートする", "Add to Slack": "Slack に追加", "document published": "ドキュメントを公開", "document updated": "ドキュメントが更新されました", "Posting to the <em>{{ channelName }}</em> channel on": "<em>{{ channelName }}</em> チャンネルへ投稿", "These events should be posted to Slack": "これらのイベントは Slack に投稿される必要があります", "This will prevent any future updates from being posted to this Slack channel. Are you sure?": "今後この Slack チャンネルにアップデートが投稿されなくなります\nよろしいですか？", "Whoops, you need to accept the permissions in Slack to connect {{appName}} to your workspace. Try again?": "{{appName}} をワークスペースに接続するには、Slack で権限を許可する必要があります。 もう一度試しますか？", "Personal account": "個人アカウント", "Link your {{appName}} account to Slack to enable searching and previewing the documents you have access to, directly within chat.": "{{appName}} アカウントを Slack と接続すると、チャットでドキュメントの検索とプレビューが可能になります", "Disconnecting your personal account will prevent searching for documents from Slack. Are you sure?": "個人アカウントとの接続を解除すると、Slack からドキュメントの検索ができなくなります。よろしいですか？", "Slash command": "スラッシュコマンド", "Get rich previews of {{ appName }} links shared in Slack and use the <em>{{ command }}</em> slash command to search for documents without leaving your chat.": "Slack で共有された {{ appName }} のリンクのプレビューを取得し、チャットからドキュメントを検索するには、<em>{{ command }}</em> スラッシュコマンドを使用してください。", "This will remove the Outline slash command from your Slack workspace. Are you sure?": "Slack ワークスペースから Outline のスラッシュコマンドが削除されます。よろしいですか？", "Connect {{appName}} collections to Slack channels. Messages will be automatically posted to Slack when documents are published or updated.": "{{appName}} コレクションを Slack のチャンネルに接続します。ドキュメントが公開または更新されると、Slack にメッセージが投稿されます。", "Comment by {{ author }} on \"{{ title }}\"": "{{ author }} が \"{{ title }}\" にコメント", "How to use {{ command }}": "{{ command }} の使用方法", "To search your workspace use {{ command }}. \nType {{ command2 }} help to display this help text.": "ワークスペースを検索するには、 {{ command }} を使用します。\n {{ command2 }} を入力するとこのヘルプテキストを表示します。", "Post to Channel": "チャンネルに投稿", "This is what we found for \"{{ term }}\"": "\"{{ term }}\" の検索結果", "No results for \"{{ term }}\"": "{{ term }} に該当する検索結果はありませんでした。", "It looks like you haven’t linked your {{ appName }} account to Slack yet": "{{ appName }} のアカウントを Slack にまだリンクしていないようです", "Link your account": "アカウントを連携する", "Link your account in {{ appName }} settings to search from Slack": "{{ appName }} の設定でアカウントを連携し、Slack から検索する", "Configure a Umami installation to send views and analytics from the workspace to your own Umami instance.": "あなたのインスタンスにワークスペースのビューと分析を送信するために、Umamiインスタンスを構成します", "The URL of your Umami instance. If you are using Umami Cloud it will begin with {{ url }}": "UmamiクラウドのURL。Umamiクラウドを使用している場合、 {{ url }} で始まります。", "Script name": "スクリプト名", "The name of the script file that Umami uses to track analytics.": "Umamiが分析を追跡するために使用するスクリプトファイルの名前。", "An ID that uniquely identifies the website in your Umami instance.": "Umamiインスタンスでウェブサイトを一意に識別する ID", "Are you sure you want to delete the {{ name }} webhook?": "{{ name }} を削除します\nよろしいですか？", "Webhook updated": "Webhook を更新しました", "Update": "更新", "Updating": "更新中", "Provide a descriptive name for this webhook and the URL we should send a POST request to when matching events are created.": "この Webhook のわかりやすい名前と、一致するイベントが発火されたときに POST リクエストを送信する URL を指定してください。", "A memorable identifer": "覚えやすい識別子", "URL": "URL", "Signing secret": "Secret", "Subscribe to all events, groups, or individual events. We recommend only subscribing to the minimum amount of events that your application needs to function.": "全てのイベント、グループ、または個別のイベントの通知を有効にします。アプリが機能するための最小限のものだけを選択することをお勧めします。", "All events": "全てのイベント", "All {{ groupName }} events": "{{ groupName }} の全てのイベント", "Delete webhook": "Webhook を削除する", "Subscribed events": "通知を設定したイベント", "Edit webhook": "Webhook を編集", "Webhook created": "Webhook を作成しました", "Webhooks": "Webhook", "New webhook": "新しい Webhook", "Webhooks can be used to notify your application when events happen in {{appName}}. Events are sent as a https request with a JSON payload in near real-time.": "Webhook は、{{appName}} でイベントが発生した際にアプリケーションに通知するものです。イベントは、ほぼリアルタイムで JSON ペイロードを含む https リクエストとして送信されます。", "Inactive": "無効", "Create a webhook": "Webhook を作成", "Zapier is a platform that allows {{appName}} to easily integrate with thousands of other business tools. Automate your workflows, sync data, and more.": "Zapier は、 {{appName}} を他の何千ものビジネスツールと簡単に統合できるプラットフォームです。ワークフローやデータの同期などを自動化できます。", "Never logged in": "未ログイン", "Online now": "オンライン", "Online {{ timeAgo }}": "{{ timeAgo }} 前にオンライン", "Viewed just now": "たった今", "You updated {{ timeAgo }}": "あなたが {{ timeAgo }} に更新しました", "{{ user }} updated {{ timeAgo }}": "{{ user }} が {{ timeAgo }} に更新しました", "You created {{ timeAgo }}": "あなたが {{ timeAgo }} に作成しました", "{{ user }} created {{ timeAgo }}": "{{ user }} が {{ timeAgo }} に作成しました", "Error loading data": "データ読込エラー"}
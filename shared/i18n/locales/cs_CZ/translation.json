{"New API key": "Nový API klíč", "Open collection": "Otevřít sbírku", "New collection": "Nová sbírka", "Create a collection": "Vytvořit novou sbírku", "Edit": "<PERSON><PERSON><PERSON><PERSON>", "Edit collection": "Upravit sbírku", "Permissions": "Oprávnění", "Collection permissions": "Oprávnění u sbírky", "Share this collection": "<PERSON>d<PERSON>let tuto kole<PERSON>", "Search in collection": "Hledat ve sbírce", "Star": "Přidat mezi oblíbené", "Unstar": "Odstranit z oblíbených", "Subscribe": "Přihlásit k odběru", "Subscribed to document notifications": "Přihlášen k odběru upozornění ohledně dokumentů", "Unsubscribe": "Odhlásit z odběru upozornění", "Unsubscribed from document notifications": "<PERSON><PERSON><PERSON><PERSON><PERSON> od<PERSON><PERSON><PERSON> ohledně dokumentů", "Archive": "Archiv", "Archive collection": "Archivovat sbírku", "Collection archived": "Sbírka archivována", "Archiving": "Probíhá archivace", "Archiving this collection will also archive all documents within it. Documents from the collection will no longer be visible in search results.": "Archivací této sbírky dojde také k archivaci všech dokumentů v ní. Dokumenty z kolekce již nebudou viditelné ve výsledcích vyhledávání.", "Restore": "Obnovit", "Collection restored": "Sbírka obnovena", "Delete": "Odstranit", "Delete collection": "Odstranit sbírku", "New template": "Nová šablona", "Delete comment": "Odstranit komentář", "Mark as resolved": "Označit jako v<PERSON>", "Thread resolved": "Vlákno vyřešeno", "Mark as unresolved": "Označit jako <PERSON>", "View reactions": "Prohlédnout reakce", "Reactions": "<PERSON><PERSON><PERSON>", "Copy ID": "Kopírovat ID", "Clear IndexedDB cache": "Smazat mezipaměť IndexedDB", "IndexedDB cache cleared": "Mezipaměť indexedDB vymazána", "Toggle debug logging": "Toggle debug logging", "Debug logging enabled": "Zaznamenávání logů povoleno", "Debug logging disabled": "Zaznamenávání logů zakázáno", "Development": "Vývoj", "Open document": "Otevřít dokument", "New document": "Nový dokument", "New draft": "Nový koncept", "New from template": "Nový ze šablony", "New nested document": "Nový vnořený dokument", "Publish": "Zveřejnit", "Published {{ documentName }}": "Publikováno {{ documentName }}", "Publish document": "Zveřejnit dokument", "Unpublish": "Zrušit zveřejnění", "Unpublished {{ documentName }}": "Nepublikováno {{ documentName }}", "Share this document": "Sdílet tento dokument", "HTML": "HTML", "PDF": "PDF", "Exporting": "Exportování", "Markdown": "<PERSON><PERSON>", "Download": "<PERSON><PERSON><PERSON><PERSON>", "Download document": "Stáhnout dokument", "Copy as Markdown": "Kopírovat jako <PERSON>", "Markdown copied to clipboard": "Markdown zkopírován do schránky", "Copy as text": "Copy as text", "Text copied to clipboard": "Text copied to clipboard", "Copy public link": "Zkopírovat veřejný odkaz", "Link copied to clipboard": "Odkaz zkopírován do schránky", "Copy link": "Kopírovat odkaz", "Copy": "Kopírovat", "Duplicate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Duplicate document": "Duplikovat dokument", "Copy document": "Kopírovat dokument", "collection": "sbírka", "Pin to {{collectionName}}": "Připnout na {{collectionName}}", "Pinned to collection": "Připnuto do sbírky", "Pin to home": "Připnout na domovskou obrazovku", "Pinned to home": "Připnuto na domovskou obrazovku", "Pin": "Připnout", "Search in document": "Hledat v dokumentu", "Print": "Tisk", "Print document": "Vytisknout dokument", "Import document": "Importovat dokument", "Templatize": "Vytvořit šablonu", "Create template": "Vytvořit šablonu", "Open random document": "Otevřít náhodný dokument", "Search documents for \"{{searchQuery}}\"": "Hledat v dokumentech \"{{searchQuery}}\"", "Move to workspace": "Přesunout do pracovního prostoru", "Move": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Move to collection": "Přesunout do sbírky", "Move {{ documentType }}": "Přesunout {{ documentType }}", "Are you sure you want to archive this document?": "Opravdu chcete archivovat tento dokument?", "Document archived": "Dokument archivován", "Archiving this document will remove it from the collection and search results.": "Archivací tento dokument odstraníte z kolekce a výsledků vyhledávání.", "Delete {{ documentName }}": "Odstranit {{ documentName }}", "Permanently delete": "Trvale odstranit", "Permanently delete {{ documentName }}": "Trvale odstranit {{ documentName }}", "Empty trash": "Vysypat koš", "Permanently delete documents in trash": "Trvale odstranit dokumenty v koši", "Comments": "Ko<PERSON><PERSON><PERSON><PERSON>", "History": "Historie", "Insights": "P<PERSON><PERSON><PERSON>y", "Disable viewer insights": "Vypnout analytika nahlížení", "Enable viewer insights": "Zapnout analytika nahlížení", "Leave document": "Opustit dokument", "You have left the shared document": "Opustil jste sdílený dokument", "Could not leave document": "Nepodařilo se opustit dokument", "Home": "Domovská stránka", "Drafts": "Koncepty", "Search": "Hledat", "Trash": "<PERSON><PERSON>", "Settings": "Nastavení", "Profile": "Profil", "Templates": "Šablony", "Notifications": "Upozornění", "Preferences": "Předvolby", "Documentation": "Dokumentace", "API documentation": "API dokumentace", "Toggle sidebar": "Postranní panel", "Send us feedback": "Napište nám svůj názor", "Report a bug": "Nahlásit chybu", "Changelog": "Změny ve verzích aplikace", "Keyboard shortcuts": "Klávesové zkratky", "Download {{ platform }} app": "Stáhněte si aplikaci {{ platform }}", "Log out": "Odhlásit se", "Mark notifications as read": "Označit upozornění jako p<PERSON>", "Archive all notifications": "Archivovat všechny notifikace", "New App": "New App", "New Application": "New Application", "This version of the document was deleted": "This version of the document was deleted", "Link copied": "Odkaz zkopírován", "Dark": "Te<PERSON>ný", "Light": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "System": "Systém", "Appearance": "Vzhled", "Change theme": "Změnit motiv", "Change theme to": "Změnit motiv na", "Switch workspace": "Přepnout pracovní prostor", "Select a workspace": "Vybrat pracovní prostor", "New workspace": "Nový pracovní prostor", "Create a workspace": "Vytvořte pracovní prostor", "Login to workspace": "Přihlaste se do pracovního prostoru", "Invite people": "Pozvat uživatele", "Invite to workspace": "Pozvat do pracovního prostoru", "Promote to {{ role }}": "<PERSON><PERSON><PERSON><PERSON><PERSON> na {{ role }}", "Demote to {{ role }}": "Snížit na {{ role }}", "Update role": "Aktualizovat roli", "Delete user": "<PERSON><PERSON><PERSON><PERSON>", "Collection": "Sbírka", "Collections": "<PERSON><PERSON><PERSON><PERSON>", "Debug": "Odstranit vývojářskou chybu", "Document": "Dokument", "Documents": "Dokumenty", "Recently viewed": "Nedávno zobrazené", "Revision": "Revize", "Navigation": "<PERSON><PERSON>", "Notification": "Upozornění ", "People": "<PERSON><PERSON><PERSON>", "Workspace": "Pracovní prostor", "Recent searches": "Nedávná vyhledávání", "currently editing": "p<PERSON><PERSON><PERSON><PERSON>", "currently viewing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "previously edited": "<PERSON><PERSON><PERSON><PERSON>", "You": "Vy", "Viewers": "Prohlížející", "Collections are used to group documents and choose permissions": "Kolekce se používají pro seskupení dokumentů a volbu spojených oprávnění", "Name": "Jméno", "The default access for workspace members, you can share with more users or groups later.": "Výchozí přístup pro členy pracovního prostoru můžete později sdílet s více uživateli nebo skupinami.", "Public document sharing": "Veřejné sdílení dokumentů", "Allow documents within this collection to be shared publicly on the internet.": "Je<PERSON><PERSON><PERSON> p<PERSON>leno, jakékoli dokumenty v této sbírce lze sdílet veřejně na internetu.", "Commenting": "Komentování", "Allow commenting on documents within this collection.": "Allow commenting on documents within this collection.", "Saving": "Uložení", "Save": "Uložit", "Creating": "Vytváření", "Create": "Vytvořit", "Collection deleted": "Kolekce odstraněna", "I’m sure – Delete": "An<PERSON>, sma<PERSON>t", "Deleting": "Mazání", "Are you sure about that? Deleting the <em>{{collectionName}}</em> collection is permanent and cannot be restored, however all published documents within will be moved to the trash.": "Jste si jisti? Smazán<PERSON> sbírky <em>{{collectionName}}</em> je trvalé a nelze vrátit zpět. Všechny publikované dokumenty ze smazané sbírky budou přesunuty do koše.", "Also, <em>{{collectionName}}</em> is being used as the start view – deleting it will reset the start view to the Home page.": "<em>{{collectionName}}</em> se také p<PERSON> jako domo<PERSON> stránka – odstraněním se obnoví výchozí nastavení.", "Type a command or search": "Zadejte příkaz nebo začněte vyhledávat", "Choose a template": "<PERSON><PERSON><PERSON><PERSON>", "Are you sure you want to permanently delete this entire comment thread?": "J<PERSON> si jisti, že chcete natrvalo odstranit vlákno komentářů?", "Are you sure you want to permanently delete this comment?": "Jste si jisti, že chcete natrvalo odstranit komentář?", "Confirm": "Potvrdit", "manage access": "spravovat přístup", "view and edit access": "přístup k prohlížení a úpravám", "view only access": "přístup pouze pro čtení", "no access": "bez přístupu", "You do not have permission to move {{ documentName }} to the {{ collectionName }} collection": "Nemáte oprávnění př<PERSON>unout {{ documentName }} do sb<PERSON>rky {{ collectionName }}", "Move document": "Přesunout dokument", "Moving": "Přesou<PERSON>", "Moving the document <em>{{ title }}</em> to the {{ newCollectionName }} collection will change permission for all workspace members from <em>{{ prevPermission }}</em> to <em>{{ newPermission }}</em>.": "Přesunutím dokumentu <em>{{ title }}</em> do sb<PERSON>rky {{ newCollectionName }} se změní oprávnění pro všechny členy pracovního prostoru z <em>{{ prevPermission }}</em> na <em>{{ newPermission }}</em>.", "Submenu": "Podmenu", "Collections could not be loaded, please reload the app": "Sbírky se nepo<PERSON><PERSON>, prosím načtěte aplikaci znovu", "Default collection": "Výchozí sbírka", "Start view": "Domovská obrazovka", "Install now": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Deleted Collection": "Odstraněná sbírka", "Untitled": "Bez názvu", "Unpin": "Zrušit připnutí", "{{ minutes }}m read": "{{ minutes }}m read", "Select a location to copy": "<PERSON><PERSON><PERSON><PERSON>, kam ch<PERSON>te zkopírovat", "Document copied": "Dokument byl zkopírován", "Couldn’t copy the document, try again?": "Dokument nelze zkopírovat, chcete to zkusit znovu?", "Include nested documents": "Zahrnout vnořené dokumenty", "Copy to <em>{{ location }}</em>": "Zkopírovat do <em>{{ location }}</em>", "Search collections & documents": "Prohledat sbírky a dokumenty", "No results found": "Nebyly nalezeny žádné výsledky", "New": "Nový", "Only visible to you": "Viditelné pouze pro vás", "Draft": "Návrh", "Template": "Šablona", "You updated": "Aktualizovali jste", "{{ userName }} updated": "{{ userName }} aktualizoval", "You deleted": "Odstranili jste", "{{ userName }} deleted": "{{ userName }} odstranil", "You archived": "Archivovali jste", "{{ userName }} archived": "{{ userName }} archivoval", "Imported": "I<PERSON>rt<PERSON><PERSON>", "You created": "Vyt<PERSON><PERSON><PERSON> jste", "{{ userName }} created": "{{ userName }} vytvořil", "You published": "Zveřejnili jste", "{{ userName }} published": "{{ userName }} zve<PERSON><PERSON><PERSON>l", "Never viewed": "<PERSON><PERSON>", "Viewed": "Zobrazeno", "in": "v", "nested document": "vnořený dokument", "nested document_plural": "vnořené do<PERSON>", "{{ total }} task": "{{ total }} ú<PERSON>l", "{{ total }} task_plural": "{{ total }} <PERSON><PERSON><PERSON>ů", "{{ completed }} task done": "{{ completed }} dokončený úkol", "{{ completed }} task done_plural": "{{ completed }} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "{{ completed }} of {{ total }} tasks": "{{ completed }} z {{ total }} <PERSON><PERSON><PERSON>ů", "Currently editing": "Právě <PERSON>vu<PERSON>", "Currently viewing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Viewed {{ timeAgo }}": "<PERSON><PERSON><PERSON><PERSON> před {{ timeAgo }}", "Module failed to load": "Modul se nepodařilo <PERSON>", "Loading Failed": "Načít<PERSON><PERSON> se<PERSON>", "Sorry, part of the application failed to load. This may be because it was updated since you opened the tab or because of a failed network request. Please try reloading.": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>t aplikace se nepodařilo nač<PERSON>. <PERSON><PERSON><PERSON><PERSON> to být způsobeno tím, že byla aktualizována od chvíle, kdy jste kartu otevřeli, nebo kvůli neúspěšnému síťovému požadavku. Zkuste znovu načíst.", "Reload": "Znovu načíst", "Something Unexpected Happened": "Stalo se něco nečekaného", "Sorry, an unrecoverable error occurred{{notified}}. Please try reloading the page, it may have been a temporary glitch.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> se, do<PERSON><PERSON> k neopravitelné chybě{{notified}}. Zkuste prosím stránku z<PERSON> na<PERSON>, m<PERSON><PERSON><PERSON> to být dočasná závada.", "our engineers have been notified": "naši technici byli <PERSON>", "Show detail": "Zobrazit podrobnosti", "Revision deleted": "Revision deleted", "Current version": "Aktuální verze", "{{userName}} edited": "{{userName}} akutalizoval", "{{userName}} archived": "{{userName}} archivoval", "{{userName}} restored": "{{userName}} obnovil", "{{userName}} deleted": "{{userName}} odstranil", "{{userName}} added {{addedUserName}}": "{{userName}} přidal(a) {{addedUserName}}", "{{userName}} removed {{removedUserName}}": "{{userName}} odebral {{removedUserName}}", "{{userName}} moved from trash": "{{userName}} p<PERSON><PERSON><PERSON><PERSON> z koše", "{{userName}} published": "{{userName}} zve<PERSON><PERSON><PERSON>l", "{{userName}} unpublished": "{{userName}} zru<PERSON><PERSON>í", "{{userName}} moved": "{{userName}} př<PERSON>unul", "Export started": "Export byl <PERSON>n", "Your file will be available in {{ location }} soon": "<PERSON><PERSON><PERSON> soubor bude brzy k dispozici v {{ location }}", "View": "Zobrazit", "A ZIP file containing the images, and documents in the Markdown format.": "Soubor ZIP obsahující obrázky a dokumenty ve formátu Markdown.", "A ZIP file containing the images, and documents as HTML files.": "Soubor ZIP obsahující obrázky a dokumenty ve formátu HTML.", "Structured data that can be used to transfer data to another compatible {{ appName }} instance.": "Strukturovaná data, která lze použít k přenosu dat do kompatibilní aplikace {{ appName }}.", "Export": "Export", "Exporting the collection <em>{{collectionName}}</em> may take some time.": "Export sbírky <em>{{collectionName}}</em> m<PERSON><PERSON><PERSON> nějak<PERSON> dobu trvat.", "You will receive an email when it's complete.": "Po dokončení obdržíte e-mail.", "Include attachments": "Zahrnout př<PERSON>lohy", "Including uploaded images and files in the exported data": "<PERSON><PERSON><PERSON>ut nahrané obrázky a soubory do exportovaných dat", "{{count}} more user": "{{count}} more user", "{{count}} more user_plural": "{{count}} more users", "Filter": "Filtr", "No results": "Žádné v<PERSON>dky", "{{authorName}} created <3></3>": "{{authorName}} vyt<PERSON>řil <3></3>", "{{authorName}} opened <3></3>": "{{authorName}} ote<PERSON><PERSON><PERSON> <3></3>", "Search emoji": "Hledat emoji", "Search icons": "Hledat ikony", "Choose default skin tone": "Vyberte výchozí tón motivu", "Show menu": "Zobrazit menu", "Icon Picker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Icons": "<PERSON><PERSON><PERSON>", "Emojis": "<PERSON><PERSON><PERSON>", "Remove": "Odstranit", "All": "<PERSON><PERSON><PERSON>", "Frequently Used": "Často používané", "Search Results": "Výsledky vyhledávání", "Smileys & People": "Smajlíci a lidé", "Animals & Nature": "Zvířata a příroda", "Food & Drink": "Jídlo a nápoje", "Activity": "Aktivita", "Travel & Places": "Cestování a místa", "Objects": "Objekty", "Symbols": "Symboly", "Flags": "V<PERSON>jk<PERSON>", "Select a color": "<PERSON><PERSON><PERSON><PERSON> bar<PERSON>", "Loading": "Načítání", "Permission": "Oprávnění", "View only": "Pouze zobrazit", "Can edit": "Může upravovat", "No access": "Bez přístupu", "Default access": "Výchozí přístup", "Change Language": "Změnit jazyk", "Dismiss": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "You’re offline.": "Jste offline.", "Sorry, an error occurred.": "<PERSON> n<PERSON> l<PERSON>, <PERSON><PERSON><PERSON> k <PERSON>.", "Click to retry": "Kliknutím zkuste znovu", "Back": "<PERSON><PERSON><PERSON><PERSON>", "Unknown": "Neznámý", "Mark all as read": "Označit vše jako přečtené", "You're all caught up": "<PERSON><PERSON> nic nového", "Icon": "Icon", "My App": "My App", "Tagline": "Tagline", "A short description": "A short description", "Callback URLs": "Callback URLs", "Published": "Zveřejněno", "Allow this app to be installed by other workspaces": "Allow this app to be installed by other workspaces", "{{ username }} reacted with {{ emoji }}": "{{ username }} reagoval s {{ emoji }}", "{{ firstUsername }} and {{ secondUsername }} reacted with {{ emoji }}": "{{ firstUsername }} a {{ secondUsername }} reagovali s {{ emoji }}", "{{ firstUsername }} and {{ count }} others reacted with {{ emoji }}": "{{ firstUsername }} a {{ count }} reagovali s {{ emoji }}", "{{ firstUsername }} and {{ count }} others reacted with {{ emoji }}_plural": "{{ firstUsername }} a {{ count }} reagovali s {{ emoji }}", "Add reaction": "<PERSON><PERSON><PERSON><PERSON>", "Reaction picker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Could not load reactions": "Nepodařilo se načíst reakce", "Reaction": "<PERSON><PERSON><PERSON>", "Results": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "No results for {{query}}": "<PERSON><PERSON><PERSON><PERSON> v<PERSON> pro {{query}}", "Manage": "Spravovat", "All members": "Všichni <PERSON>", "Everyone in the workspace": "Všichni v pracovišti", "{{ count }} member": "{{ count }} <PERSON><PERSON>", "{{ count }} member_plural": "{{ count }} <PERSON><PERSON><PERSON>", "Invite": "Pozvat", "{{ userName }} was added to the collection": "{{ userName }} by<PERSON> <PERSON> do s<PERSON>", "{{ count }} people added to the collection": "{{ count }} lid<PERSON> přidáno do kolekce", "{{ count }} people added to the collection_plural": "{{ count }} lid<PERSON> přidáno do kolekce", "{{ count }} people and {{ count2 }} groups added to the collection": "{{ count }} lidí a {{ count2 }} sku<PERSON> přidáno do kolekce", "{{ count }} people and {{ count2 }} groups added to the collection_plural": "{{ count }} lidí a {{ count2 }} sku<PERSON> přidáno do kolekce", "Add": "<PERSON><PERSON><PERSON><PERSON>", "Add or invite": "Přidat nebo pozvat", "Viewer": "Prohlížející", "Editor": "Editor", "Suggestions for invitation": "Návrhy na pozvánku", "No matches": "Žádné v<PERSON>dky", "Can view": "<PERSON><PERSON><PERSON><PERSON>", "Everyone in the collection": "Všichni v kolekci", "You have full access": "<PERSON><PERSON><PERSON>řís<PERSON>", "Created the document": "Dokument vytvořen", "Other people": "Ostatní <PERSON>", "Other workspace members may have access": "Ostatní členové pracoviště mohou mít přístup", "This document may be shared with more workspace members through a parent document or collection you do not have access to": "Tento dokument může být sdílen s více členy pracovního prostoru prostřednictvím nadřazeného dokumentu nebo sbírky, ke které nemáte přístup", "Access inherited from collection": "Přístup zděděný z kolekce", "{{ userName }} was removed from the document": "{{ userName }} byl z dokumentu odstraněn", "Could not remove user": "Uživatele nelze odstranit", "Permissions for {{ userName }} updated": "Oprávnění pro {{ userName }} by<PERSON> a<PERSON>ual<PERSON>", "Could not update user": "Uživatele nelze upravit", "Has access through <2>parent</2>": "Má přístup prostřednictvím <2><PERSON><PERSON><PERSON><PERSON></2>", "Suspended": "Pozastaven", "Invited": "Pozvaní", "Active <1></1> ago": "Aktiv<PERSON><PERSON> před <1></1>", "Never signed in": "<PERSON><PERSON>", "Leave": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Only lowercase letters, digits and dashes allowed": "<PERSON><PERSON>u povolena pouze mal<PERSON> pís<PERSON>a, číslice a pomlčky", "Sorry, this link has already been used": "<PERSON> ná<PERSON> lí<PERSON>, tento odkaz již byl použit", "Public link copied to clipboard": "Veřejný odkaz zkopírován do schránky", "Web": "Web", "Anyone with the link can access because the parent document, <2>{{documentTitle}}</2>, is shared": "Kdokoli s odkazem má přístup, protože dokument dědí oprávnění po nadřazeném dokumentu <2>{{documentTitle}}</2>", "Allow anyone with the link to access": "Povolit přístup komukoliv s odkazem", "Publish to internet": "Zveřejnit na internetu", "Search engine indexing": "Indexace vyhledávání", "Disable this setting to discourage search engines from indexing the page": "Zakažte toto nastavení, abyste odradili vyhledávače od indexování stránky", "Show last modified": "Show last modified", "Display the last modified timestamp on the shared page": "Display the last modified timestamp on the shared page", "Nested documents are not shared on the web. Toggle sharing to enable access, this will be the default behavior in the future": "Vložené dokumenty nejsou sdíleny na webu. Změnit sdílení pro povolení přístupu (toto bude v budoucnu výchozí chování)", "{{ userName }} was added to the document": "{{ userName }} byl přidán do dokumentu", "{{ count }} people added to the document": "{{ count }} lid<PERSON> bylo přidáno do dokumentu", "{{ count }} people added to the document_plural": "{{ count }} lid<PERSON> bylo přidáno do dokumentu", "{{ count }} groups added to the document": "{{ count }} skupin bylo přidáno do dokumentu", "{{ count }} groups added to the document_plural": "{{ count }} skupin bylo přidáno do dokumentu", "Logo": "Logo", "Archived collections": "Archivovan<PERSON>", "New doc": "Nový dokument", "Empty": "Prázdné", "Collapse": "Sbal<PERSON>", "Expand": "Rozbalit", "Document not supported – try Markdown, Plain text, HTML, or Word": "Dokument není <PERSON> – zkuste Markdown, Plain text, HTML nebo Word", "Go back": "<PERSON><PERSON><PERSON>", "Go forward": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Could not load shared documents": "Sdílené dokumenty nelze načíst", "Shared with me": "Sdíleno se mnou", "Show more": "Zobrazit více", "Could not load starred documents": "Nelze načíst dokumenty s hvězdičkou", "Starred": "Oblíben<PERSON>", "Up to date": "Aktuální", "{{ releasesBehind }} versions behind": "Zastaralá verze {{ releasesBehind }}", "{{ releasesBehind }} versions behind_plural": "Zastaralé verze {{ releases<PERSON>eh<PERSON> }}", "Change permissions?": "Změnit práva?", "{{ documentName }} cannot be moved within {{ parentDocumentName }}": "{{ documentName }} cannot be moved within {{ parentDocumentName }}", "You can't reorder documents in an alphabetically sorted collection": "Nemůžete změnit pořadí dokumentů v abecedně seřazené sbírce", "The {{ documentName }} cannot be moved here": "The {{ documentName }} cannot be moved here", "Return to App": "Zpět do aplikace", "Installation": "Instalace", "Unstar document": "Odznačit dokument", "Star document": "Označit dokument", "Template created, go ahead and customize it": "Šablona vytvořena, pokračujte a přizpůsobte ji", "Creating a template from <em>{{titleWithDefault}}</em> is a non-destructive action – we'll make a copy of the document and turn it into a template that can be used as a starting point for new documents.": "Vytvoření <PERSON> z <em>{{titleWithDefault}}</em> je nedestruktivn<PERSON> ak<PERSON> – vytvoříme kopii dokumentu a přeměníme ji na šablonu, kterou lze použít jako výchozí bod pro nové dokumenty.", "Enable other members to use the template immediately": "Enable other members to use the template immediately", "Location": "Location", "Admins can manage the workspace and access billing.": "Administrá<PERSON><PERSON><PERSON> mohou spravovat pracovní prostor a přistupovat k fakturacím.", "Editors can create, edit, and comment on documents.": "Edit<PERSON><PERSON><PERSON> moh<PERSON> v<PERSON>, upravovat a komentovat dokumenty.", "Viewers can only view and comment on documents.": "Prohlížející mohou pouze prohlížet a komentovat dokumenty.", "Are you sure you want to make {{ userName }} a {{ role }}?": "Opravdu chcete uživateli {{ userName }} nastavit roli {{ role }}?", "I understand, delete": "Rozumím, odstranit", "Are you sure you want to permanently delete {{ userName }}? This operation is unrecoverable, consider suspending the user instead.": "Opravdu chcete trvale smazat {{ userName }}? Tuto operaci nelze obnovit, zvažte místo toho pozastavení uživatele.", "Are you sure you want to suspend {{ userName }}? Suspended users will be prevented from logging in.": "Opravdu chcete pozastavit {{ userName }}? Pozastaveným uživatelům bude zabráněno v přihlášení.", "New name": "Nový název", "Name can't be empty": "Název nemůže být prázdný", "Check your email to verify the new address.": "Check your email to verify the new address.", "The email will be changed once verified.": "The email will be changed once verified.", "You will receive an email to verify your new address. It must be unique in the workspace.": "You will receive an email to verify your new address. It must be unique in the workspace.", "A confirmation email will be sent to the new address before it is changed.": "A confirmation email will be sent to the new address before it is changed.", "New email": "New email", "Email can't be empty": "Email can't be empty", "Your import completed": "Váš import by<PERSON>", "Previous match": "Předchozí shoda", "Next match": "<PERSON><PERSON><PERSON> shoda", "Find and replace": "<PERSON><PERSON><PERSON><PERSON> a na<PERSON>", "Find": "<PERSON><PERSON><PERSON><PERSON>", "Match case": "Rozlišit malá a velká písmena", "Enable regex": "Povolit regex", "Replace options": "Možnosti nahrazení", "Replacement": "Náhrada", "Replace": "Nahradit", "Replace all": "Nahradit vše", "Profile picture": "Profilový obrázek", "Create a new doc": "Vytvořit nový dokument", "{{ userName }} won't be notified, as they do not have access to this document": "{{ userName }} won't be notified, as they do not have access to this document", "Keep as link": "Zachovat jako odkaz", "Mention": "Mention", "Embed": "Embed", "Add column after": "Přidat sloupec za", "Add column before": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> před", "Add row after": "<PERSON><PERSON><PERSON><PERSON>a", "Add row before": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>ed", "Align center": "Zarovnat na střed", "Align left": "Zarovnat vlevo", "Align right": "Zarovnat vpravo", "Default width": "Výchozí šířka", "Full width": "Plná šířka", "Bulleted list": "Seznam s odrážkami", "Todo list": "Seznam úkolů", "Code block": "Blok kódu", "Copied to clipboard": "Zkopírováno do schránky", "Code": "<PERSON><PERSON><PERSON>", "Comment": "<PERSON><PERSON><PERSON><PERSON>", "Create link": "Vytvořit odkaz", "Sorry, an error occurred creating the link": "Omlouváme se, při vytváření odkazu došlo k ch<PERSON>bě", "Create a new child doc": "Vytvořit nový podřazený dokument", "Delete table": "Odstranit tabulku", "Delete file": "<PERSON><PERSON><PERSON><PERSON> so<PERSON>or", "Width x Height": "Width x Height", "Download file": "<PERSON><PERSON><PERSON><PERSON>", "Replace file": "<PERSON><PERSON><PERSON> soubor", "Delete image": "Odstranit obrázek", "Download image": "Stáhnout obrázek", "Replace image": "Nahradit obrázek", "Italic": "<PERSON><PERSON><PERSON><PERSON>", "Sorry, that link won’t work for this embed type": "<PERSON> nám líto, tento odkaz nebude pro tento typ vložení fungovat", "File attachment": "Příloha", "Enter a link": "Enter a link", "Big heading": "Velký nadpis", "Medium heading": "Střední nadpis", "Small heading": "Malý nadpis", "Extra small heading": "Extra malý nadpis", "Heading": "Záhlaví", "Divider": "Dělí<PERSON><PERSON>", "Image": "Obrázek", "Sorry, an error occurred uploading the file": "<PERSON><PERSON>lou<PERSON><PERSON><PERSON> se, při nahrávání souboru došlo k ch<PERSON>bě", "Write a caption": "Napsat popisek", "Info": "Informace", "Info notice": "Informační upozornění", "Link": "Odkaz", "Highlight": "Zvýraznění", "Type '/' to insert": "Zadejte '/' pro vložení", "Keep typing to filter": "Chcete-li filtrovat, pokračujte v psaní", "Open link": "Otevřít od<PERSON>z", "Go to link": "Přejít na odkaz", "Sorry, that type of link is not supported": "<PERSON> n<PERSON> lí<PERSON>, ale tento typ obsahu není pod<PERSON>n<PERSON>", "Ordered list": "Uspořádaný seznam", "Page break": "<PERSON><PERSON><PERSON>", "Paste a link": "Vložit odkaz", "Paste a {{service}} link…": "Vložte {{service}} odkaz…", "Placeholder": "<PERSON><PERSON><PERSON> pole", "Quote": "Citace", "Remove link": "Odstranit odkaz", "Search or paste a link": "Vyhledat nebo vložit odkaz", "Strikethrough": "Přeškrtnutí", "Bold": "Tučně", "Subheading": "Podnadpis", "Sort ascending": "Seřadit vzestupně", "Sort descending": "<PERSON><PERSON><PERSON><PERSON>", "Table": "Tabulka", "Export as CSV": "Export as CSV", "Toggle header": "Toggle header", "Math inline (LaTeX)": "Matematický vzorec (LaTeX)", "Math block (LaTeX)": "Matematický vzorec (LaTeX)", "Merge cells": "Merge cells", "Split cell": "Split cell", "Tip": "Tip", "Tip notice": "Tip", "Warning": "Varování", "Warning notice": "Varování", "Success": "Úspěch", "Success notice": "Úspěch", "Current date": "Dnešní datum", "Current time": "Aktuální čas", "Current date and time": "Aktuální datum a čas", "Indent": "Odsazení", "Outdent": "Předsazení", "Video": "Video", "None": "None", "Could not import file": "<PERSON><PERSON><PERSON> ne<PERSON><PERSON>t", "Unsubscribed from document": "Upozornění vypnuta", "Unsubscribed from collection": "Unsubscribed from collection", "Account": "Účet", "API & Apps": "API & Apps", "Details": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Security": "Zabezpečení", "Features": "Funkce", "Members": "Uživatelé", "Groups": "Skupiny", "API Keys": "API Keys", "Applications": "Applications", "Shared Links": "Sdílené <PERSON>", "Import": "Import", "Install": "Install", "Integrations": "Integrace", "Revoke token": "Odvolat tokeny", "Revoke": "Zrušit", "Show path to document": "Zobrazit cestu k dokumentu", "Path to document": "Cesta k dokumentu", "Group member options": "Nastavení členů skupiny", "Export collection": "Exportovat sbírku", "Rename": "Př<PERSON>menovat", "Sort in sidebar": "Seřadit v postranním panelu", "A-Z sort": "A-Z sort", "Z-A sort": "Z-A sort", "Manual sort": "Manuální řazení", "Comment options": "Nastavení <PERSON>", "Show document menu": "Show document menu", "{{ documentName }} restored": "{{ documentName }} restored", "Document options": "Možnosti dokumentů", "Choose a collection": "Vybrat sbírku", "Subscription inherited from collection": "Subscription inherited from collection", "Apply template": "Apply template", "Enable embeds": "Povolit embed vkládání", "Export options": "Možnosti exportu", "Group members": "Členové skupiny", "Edit group": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "Delete group": "Odstranit skupinu", "Group options": "Nastavení skupin", "Cancel": "Zrušit", "Import menu options": "Import menu options", "Member options": "Uživatelská nastavení", "New document in <em>{{ collectionName }}</em>": "Nový dokument v <em>{{ collectionName }}</em>", "New child document": "Nový vložený dokument", "Save in workspace": "Save in workspace", "Notification settings": "Nastavení oznámení", "Revoke {{ appName }}": "Revoke {{ appName }}", "Revoking": "Odvolávání", "Are you sure you want to revoke access?": "Are you sure you want to revoke access?", "Delete app": "Delete app", "Revision options": "Možnosti revize", "Share link revoked": "Odkaz na sdílení zrušen", "Share link copied": "Odkaz pro sdílení zkopírován", "Share options": "Možnosti sdílení", "Go to document": "Přejít do dokumentu", "Revoke link": "Zrušit odkaz", "Contents": "<PERSON><PERSON><PERSON>", "Headings you add to the document will appear here": "Zde se zobrazí nadpisy, které přidáte do dokumentu", "Table of contents": "<PERSON><PERSON><PERSON>", "Change name": "Př<PERSON>menovat", "Change email": "Change email", "Suspend user": "Pozastavit <PERSON>", "An error occurred while sending the invite": "Při odesílání pozvánky došlo k chybě", "User options": "Uživatelské nastavení", "Change role": "Změnit roli", "Resend invite": "Znovu poslat pozvánku", "Revoke invite": "Zrušit pozvání", "Activate user": "Aktivovat uživatele", "template": "šablona", "document": "dokument", "published": "zveřejněno", "edited": "upraveno", "created the collection": "vyt<PERSON><PERSON>il <PERSON>í<PERSON>u", "mentioned you in": "zmínil vás v", "left a comment on": "<PERSON><PERSON><PERSON> k<PERSON> k", "resolved a comment on": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> komentář dne", "shared": "sdíleno", "invited you to": "vás pozval/a do", "Choose a date": "<PERSON><PERSON><PERSON><PERSON> datum", "API key created. Please copy the value now as it will not be shown again.": "API key created. Please copy the value now as it will not be shown again.", "Scopes": "<PERSON><PERSON><PERSON>", "Space-separated scopes restrict the access of this API key to specific parts of the API. Leave blank for full access": "Space-separated scopes restrict the access of this API key to specific parts of the API. Leave blank for full access", "Expiration": "Vypršení p<PERSON>nosti", "Never expires": "<PERSON><PERSON>", "7 days": "7 dní", "30 days": "30 dní", "60 days": "60 dní", "90 days": "90 dní", "Custom": "Vlastní", "No expiration": "Bez data vypršení platnosti", "The document archive is empty at the moment.": "Archiv dokumentů je v tuto chvíli prázdný.", "Collection menu": "Nabídka s<PERSON>írek", "Drop documents to import": "Přetáhněte dokumenty k importu", "<em>{{ collectionName }}</em> doesn’t contain any\n                    documents yet.": "<em>{{ collectionName }}</em> zatím neobsahu<PERSON> ž<PERSON> do<PERSON>.", "{{ usersCount }} users and {{ groupsCount }} groups with access": "{{ usersCount }} uživatel a {{ groupsCount }} sku<PERSON>y s přístupem", "{{ usersCount }} users and {{ groupsCount }} groups with access_plural": "{{ usersCount }} uživatelů a {{ groupsCount }} sku<PERSON>y s přístupem", "{{ usersCount }} users and a group have access": "{{ usersCount }} uživatel a skupina s přístupem", "{{ usersCount }} users and a group have access_plural": "{{ usersCount }} uživatel a skupina s přístupem", "{{ usersCount }} users with access": "{{ usersCount }} uživatel s přístupem", "{{ usersCount }} users with access_plural": "{{ usersCount }} uživatelů s přístupem", "{{ groupsCount }} groups with access": "{{ groupsCount }} s<PERSON><PERSON> s p<PERSON>m", "{{ groupsCount }} groups with access_plural": "{{ groupsCount }} sku<PERSON> s p<PERSON>", "Archived by {{userName}}": "Archivoval {{userName}}", "Sorry, an error occurred saving the collection": "O<PERSON>louv<PERSON><PERSON> se, p<PERSON>i ukládání sbírky došlo k chybě", "Add a description": "<PERSON><PERSON><PERSON><PERSON> pop<PERSON>", "Share": "Sdílet", "Overview": "<PERSON><PERSON><PERSON><PERSON>", "Recently updated": "Nedávno aktualizováno", "Recently published": "Nedávno zveřejněné", "Least recently updated": "Naposledy aktualizováno", "A–Z": "A–Z", "Signing in": "Přihlašování", "You can safely close this window once the Outline desktop app has opened": "Po otevření desktopové aplikace Outline můžete toto okno bezpečně zavřít", "Error creating comment": "Chyba při vytváření komentáře", "Add a comment": "Přidat komentář", "Add a reply": "Přidat odpověď", "Reply": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Post": "<PERSON><PERSON><PERSON>", "Upload image": "<PERSON><PERSON><PERSON><PERSON>", "No resolved comments": "Žádné vyřešené komentáře", "No comments yet": "Doposud ž<PERSON>dn<PERSON> k<PERSON>ntář<PERSON>", "New comments": "New comments", "Most recent": "Most recent", "Order in doc": "Order in doc", "Resolved": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Sort comments": "Sort comments", "Show {{ count }} reply": "Show {{ count }} reply", "Show {{ count }} reply_plural": "Show {{ count }} replies", "Error updating comment": "Chyba při aktualizaci komentáře", "Document is too large": "Dokument je p<PERSON><PERSON><PERSON> velk<PERSON>", "This document has reached the maximum size and can no longer be edited": "Tento dokument dosáhl maximální velikosti a nelze jej dále upravovat", "Authentication failed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> se<PERSON>", "Please try logging out and back in again": "Zkuste se prosím odhlásit a znovu přihlásit", "Authorization failed": "Autorizace sel<PERSON>", "You may have lost access to this document, try reloading": "Možná jste ztratili přístup k tomuto dokumentu, zkuste jej znovu načíst", "Too many users connected to document": "<PERSON><PERSON><PERSON><PERSON>š mnoho uživatelů připojených k dokumentu", "Your edits will sync once other users leave the document": "<PERSON><PERSON><PERSON><PERSON> budou synchr<PERSON>, j<PERSON><PERSON> u<PERSON> opustí dokument", "Server connection lost": "Připojení k <PERSON>u bylo <PERSON>", "Edits you make will sync once you’re online": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, se synchroni<PERSON><PERSON><PERSON>, jak<PERSON> b<PERSON> online", "Document restored": "Dokument obnoven", "Images are still uploading.\nAre you sure you want to discard them?": "Obrá<PERSON><PERSON> se stále nahrávají.\nOpravdu je chcete zahodit?", "{{ count }} comment": "{{ count }} k<PERSON><PERSON><PERSON><PERSON>", "{{ count }} comment_plural": "{{ count }} k<PERSON><PERSON><PERSON><PERSON><PERSON>", "Viewed by": "Zobrazili si", "only you": "pouze vy", "person": "osoba", "people": "<PERSON><PERSON>", "Last updated": "Naposledy aktualizováno", "Type '/' to insert, or start writing…": "Zadejte '/' pro vložení nebo začněte psát…", "Hide contents": "<PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>", "Show contents": "Zobrazit obsah", "available when headings are added": "k <PERSON>spoz<PERSON>, k<PERSON><PERSON> jsou přidávány nadpisy", "Edit {{noun}}": "Upravit {{noun}}", "Switch to dark": "Přepnout do tmavého režimu", "Switch to light": "Přepnout do světlého režimu", "Archived": "Archivované", "Save draft": "Uložit koncept", "Done editing": "Uložit úpravy", "Restore version": "Obnovit verzi", "No history yet": "Zatím žádná historie", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Imported from {{ source }}": "Importováno z {{ source }}", "Stats": "Statistiky", "{{ count }} minute read": "{{ count }} minut <PERSON>", "{{ count }} minute read_plural": "{{ count }} minut <PERSON>", "{{ count }} words": "{{ count }} slovo", "{{ count }} words_plural": "{{ count }} slov", "{{ count }} characters": "{{ count }} znak", "{{ count }} characters_plural": "{{ count }} zna<PERSON><PERSON>", "{{ number }} emoji": "{{ number }} emotikonů", "No text selected": "Není vybrán <PERSON> text", "{{ count }} words selected": "{{ count }} v<PERSON><PERSON><PERSON><PERSON> slovo", "{{ count }} words selected_plural": "{{ count }} v<PERSON><PERSON><PERSON><PERSON><PERSON> slov", "{{ count }} characters selected": "{{ count }} znak vybrán", "{{ count }} characters selected_plural": "{{ count }} z<PERSON><PERSON><PERSON> v<PERSON>r<PERSON>", "Contributors": "Přispěvatelé", "Created": "Vytvořeno", "Creator": "Tvů<PERSON><PERSON>", "Last edited": "<PERSON><PERSON><PERSON><PERSON> up<PERSON>no", "Previously edited": "<PERSON><PERSON><PERSON><PERSON>", "No one else has viewed yet": "<PERSON><PERSON> to zatím neviděl", "Viewed {{ count }} times by {{ teamMembers }} people": "<PERSON><PERSON><PERSON><PERSON> {{ count }} kr<PERSON><PERSON> {{ teamMembers }} lid<PERSON>", "Viewed {{ count }} times by {{ teamMembers }} people_plural": "<PERSON><PERSON><PERSON><PERSON> {{ count }} kr<PERSON><PERSON> {{ teamMembers }} lid<PERSON>", "Viewer insights are disabled.": "Analytika prohlížení jsou vypnuta.", "Sorry, the last change could not be persisted – please reload the page": "<PERSON> n<PERSON><PERSON>, pos<PERSON><PERSON><PERSON> změna ne<PERSON>a být zach<PERSON> - prosím znovu načtěte stránku", "{{ count }} days": "{{count}} den", "{{ count }} days_plural": "{{count}} dní", "This template will be permanently deleted in <2></2> unless restored.": "Tato <PERSON> bude trvale odstraněna v <2></2> dokud nebude obnovena.", "This document will be permanently deleted in <2></2> unless restored.": "Tento dokument bude trvale odstraněn v <2></2> dokud nebude obnoven.", "Highlight some text and use the <1></1> control to add placeholders that can be filled out when creating new documents": "Zvýrazněte nějaký text a použijte <1></1> ovládací prvek pro přidání zástupných symbolů, které lze vyplnit při vytváření nových dokumentů", "You’re editing a template": "Upravujete šablonu", "Deleted by {{userName}}": "Odstranil {{userName}}", "Observing {{ userName }}": "Pozorovatel {{ userName }}", "Backlinks": "Zpětné o<PERSON>", "Close": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "This document is large which may affect performance": "This document is large which may affect performance", "{{ teamName }} is using {{ appName }} to share documents, please login to continue.": "{{ teamName }} použív<PERSON> {{ appName }} ke s<PERSON><PERSON><PERSON><PERSON>, pro pokračování se prosím přihlaste.", "Are you sure you want to delete the <em>{{ documentTitle }}</em> template?": "Opravdu chcete odstranit šablonu <em>{{ documentTitle }}</em>?", "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history</em>.": "Jsi si tím jisti? <PERSON>ři odstranění dokumentu <em>{{ documentTitle }}</em> se smaže i celá jeho historie</em>.", "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history and <em>{{ any }} nested document</em>.": "Jsi si tím jisti? Odstraněním dokumentu <em>{{ documentTitle }}</em> se smaže i celá jeho historie a <em>{{ any }} vnořený dokument</em>.", "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history and <em>{{ any }} nested document</em>._plural": "Jsi si tím jisti? Odstraněním dokumentu <em>{{ documentTitle }}</em> se smaže i celá jeho historie a <em>{{ any }} vnořené dokumenty</em>.", "If you’d like the option of referencing or restoring the {{noun}} in the future, consider archiving it instead.": "Pokud byste v budoucnu chtěli mít možnost odkazovat nebo obnovit {{noun}}, zvažte archivaci.", "Select a location to move": "<PERSON><PERSON><PERSON><PERSON>, kam <PERSON><PERSON>te p<PERSON><PERSON>", "Document moved": "Dokument byl p<PERSON>", "Couldn’t move the document, try again?": "Dokument nelze př<PERSON>unout, chcete to zkusit znovu?", "Move to <em>{{ location }}</em>": "Přesunout do <em>{{ location }}</em>", "Couldn’t create the document, try again?": "Nepodařilo se vytvořit dokument, chcete to zkusit znovu?", "Document permanently deleted": "Dokument trvale s<PERSON>án", "Are you sure you want to permanently delete the <em>{{ documentTitle }}</em> document? This action is immediate and cannot be undone.": "Opravdu chcete trvale smazat dokument <em>{{ documentTitle }}</em> ? Tato akce je okamžit<PERSON> a nelze ji vrátit zpět.", "Select a location to publish": "Vyberte umístění pro zveřejnění", "Document published": "Zveřejněný dokument", "Couldn’t publish the document, try again?": "Dokument se nepodařilo z<PERSON>ř<PERSON>t, chcete to zkusit znovu?", "Publish in <em>{{ location }}</em>": "Zveřejnit v <em>{{ location }}</em>", "Search documents": "Hledat v dokumentech", "No documents found for your filters.": "Pro zadaný požadavek nebyly nalezeny žádné dokumenty.", "You’ve not got any drafts at the moment.": "Momentálně nemáte žádné konce<PERSON>.", "Payment Required": "Vyžadována platba", "No access to this doc": "No access to this doc", "It doesn’t look like you have permission to access this document.": "It doesn’t look like you have permission to access this document.", "Please request access from the document owner.": "Please request access from the document owner.", "Not found": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "The page you’re looking for cannot be found. It might have been deleted or the link is incorrect.": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ne<PERSON><PERSON>. Možná byla odstraněna nebo odkaz není spr<PERSON>ý.", "Offline": "Offline", "We were unable to load the document while offline.": "V režimu offline se nepodařilo načíst dokument.", "Your account has been suspended": "<PERSON><PERSON><PERSON> byl poz<PERSON>", "Warning Sign": "<PERSON><PERSON><PERSON>ha", "A workspace admin (<em>{{ suspendedContactEmail }}</em>) has suspended your account. To re-activate your account, please reach out to them directly.": "S<PERSON><PERSON><PERSON><PERSON><PERSON> pracovnho prostoru (<em>{{ suspendedContactEmail }}</em>) pozastavil V<PERSON>. Chcete-li svůj účet znovu aktivovat, kontaktujte správce.", "Created by me": "Vytvořeno mnou", "Weird, this shouldn’t ever be empty": "<PERSON><PERSON><PERSON><PERSON>, tohle by nik<PERSON> <PERSON><PERSON><PERSON> b<PERSON><PERSON> pr<PERSON><PERSON>", "You haven’t created any documents yet": "Zatím jste nevytvořili žádné dokumenty", "Documents you’ve recently viewed will be here for easy access": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> jste si ne<PERSON>á<PERSON>, zde budou pro snadný přístup", "We sent out your invites!": "Rozeslali jsme vaše pozvánky!", "Those email addresses are already invited": "Tyto e-mailové adresy jsou již p<PERSON>vány", "Sorry, you can only send {{MAX_INVITES}} invites at a time": "<PERSON><PERSON><PERSON><PERSON>, mů<PERSON><PERSON> odes<PERSON> pouze {{MAX_INVITES}} pozvánek najednou", "Invited {{roleName}} will receive access to": "Pozvaní u<PERSON> s rolí {{roleName}} získají přístup k", "{{collectionCount}} collections": "{{collectionCount}} kolek<PERSON><PERSON>", "Admin": "S<PERSON>rá<PERSON><PERSON>", "Can manage all workspace settings": "Může spravovat všechna nastavení pracovního prostoru", "Can create, edit, and delete documents": "Může vytv<PERSON>řet, upravovat a mazat dokumenty", "Can view and comment": "Může prohlížet a komentovat", "Invite people to join your workspace. They can sign in with {{signinMethods}} or use their email address.": "Pozvěte lidi, aby se připojili k Vašemu pracovnímu prostoru. Mohou se přihlásit pomocí {{signinMethods}} nebo pomocí jejich e-mailové adresy.", "Invite members to join your workspace. They will need to sign in with {{signinMethods}}.": "Pozvěte členy, aby se připojili k vašemu pracovnímu prostoru. Budou se muset přihlásit pomocí {{signinMethods}}.", "As an admin you can also <2>enable email sign-in</2>.": "<PERSON>ak<PERSON> spr<PERSON> můžete také <2>povolit přihlašování e-mailem</2>.", "Invite as": "<PERSON>zvat jako", "Role": "Role", "Email": "E-mail", "Add another": "<PERSON><PERSON><PERSON><PERSON>", "Inviting": "Pozvání", "Send Invites": "<PERSON><PERSON><PERSON>", "Open command menu": "Otevřít příkazovou řádku", "Forward": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Edit current document": "Upravit tento dokument", "Move current document": "Přesunout tento dokument", "Open document history": "Otevřít historii dokumentu", "Jump to search": "Přejít na hledání", "Jump to home": "Přejít na domovskou stránku", "Focus search input": "Vyhledávací lišta", "Open this guide": "Otevřít průvodce", "Enter": "Enter", "Publish document and exit": "Zveřejnit dokument a ukončit", "Save document": "Uložit dokument", "Cancel editing": "Zrušit <PERSON>", "Collaboration": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Formatting": "Formátování", "Paragraph": "Odstavec", "Large header": "Velké <PERSON>", "Medium header": "Střední záhlaví", "Small header": "<PERSON><PERSON>", "Underline": "Podtržení", "Undo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Redo": "Opakovat", "Move block up": "Move block up", "Move block down": "Move block down", "Lists": "Seznamy", "Toggle task list item": "Toggle task list item", "Tab": "Tab", "Indent list item": "Odsadit položku seznamu", "Outdent list item": "Předsadit položku seznamu", "Move list item up": "Posunout položku seznamu nahoru", "Move list item down": "Posunout položku seznamu dolů", "Tables": "Tabulky", "Insert row": "Vlož<PERSON>", "Next cell": "<PERSON><PERSON><PERSON>", "Previous cell": "Předchozí buňka", "Space": "Mezerník", "Numbered list": "Číslovaný seznam", "Blockquote": "Citace", "Horizontal divider": "Vodorovný oddělov<PERSON>č", "LaTeX block": "Blok LaTeX", "Inline code": "Vložený kód", "Inline LaTeX": "Vložený LaTeX", "Triggers": "Ak<PERSON>", "Mention users and more": "Zmínit uživatele a další", "Emoji": "<PERSON><PERSON><PERSON>", "Insert block": "Přidat blok", "Sign In": "Přihlásit se", "Continue with Email": "Pokračovat pomocí e-mailu", "Continue with {{ authProviderName }}": "Pokračovat s {{ authProviderName }}", "Back to home": "Zpět na úvodní stránku", "The workspace could not be found": "Pracovní prostor ne<PERSON>l na<PERSON>", "To continue, enter your workspace’s subdomain.": "Pro pokračování zadejte subdoménu Vašeho pracovního prostoru.", "subdomain": "subdoména", "Continue": "Po<PERSON><PERSON><PERSON><PERSON>", "The domain associated with your email address has not been allowed for this workspace.": "Doména spo<PERSON>ná s vaší e-mailovou adresou nebyla pro tento pracovní prostor povolena.", "Unable to sign-in. Please navigate to your workspace's custom URL, then try to sign-in again.<1></1>If you were invited to a workspace, you will find a link to it in the invite email.": "Nelze se přihlásit. Přejděte prosím na vlastní adresu URL svého pracovního prostoru a zkuste se znovu přihlásit.<1></1> Pokud jste byli pozváni do pracovního prostoru, najdete odkaz na něj v e-mailu s pozvánkou.", "Sorry, a new account cannot be created with a personal Gmail address.<1></1>Please use a Google Workspaces account instead.": "Je nám líto, nový účet nelze vytvořit s osobní Gmail adresou.<1></1>Použijte místo toho účet Google Workspace.", "The workspace associated with your user is scheduled for deletion and cannot be accessed at this time.": "Pracoviště spojené s vaším uživatelským účtem je naplánováno ke smazání a tudíž je momentálně nepřístupné.", "The workspace you authenticated with is not authorized on this installation. Try another?": "Prac<PERSON><PERSON><PERSON> pro<PERSON>, pomo<PERSON><PERSON> k<PERSON> jste se ov<PERSON><PERSON><PERSON>, není v této instalaci autorizován. Zkuste jiný.", "We could not read the user info supplied by your identity provider.": "Nepodařilo se nám přečíst informace o uživateli poskytnuté vaším poskytovatelem identity.", "Your account uses email sign-in, please sign-in with email to continue.": "Váš <PERSON> používá e-mailové přihlašování. Chcete-li pok<PERSON>, přihlaste se pomocí e-mailu.", "An email sign-in link was recently sent, please check your inbox or try again in a few minutes.": "Nedávno byl odeslán odkaz pro přihlášení do vašeho e-mailu, zkontrolujte prosím svou doručenou poštu nebo to zkuste znovu za několik minut.", "Authentication failed – we were unable to sign you in at this time. Please try again.": "Ověření se nezdařilo – v tuto chvíli jsme vás nemohli přihlásit. Zkuste to prosím znovu.", "Authentication failed – you do not have permission to access this workspace.": "Ověření se nezdařilo – nemáte oprávnění k přístupu k tomuto pracovnímu prostoru.", "Sorry, it looks like that sign-in link is no longer valid, please try requesting another.": "<PERSON> ná<PERSON> lí<PERSON>, z<PERSON><PERSON> <PERSON>, že tento odkaz pro přihlášení již nen<PERSON> p<PERSON>, zkuste prosím požádat o jiný.", "Your account has been suspended. To re-activate your account, please contact a workspace admin.": "Váš <PERSON> byl pozastaven. Chcete-li znovu aktivovat svůj účet, kontaktujte správce pracovního prostoru.", "This workspace has been suspended. Please contact support to restore access.": "Tento pracovní prostor byl pozastaven. Prosím kontaktujte podporu pro obnovení přístupu.", "Authentication failed – this login method was disabled by a workspace admin.": "Ověření se nezdařilo – tento způsob přihlášení byl zakázán správcem týmu.", "The workspace you are trying to join requires an invite before you can create an account.<1></1>Please request an invite from your workspace admin and try again.": "Pracovní prostor, ke kterému se pokoušíte připojit, vyžaduje před vytvořením účtu pozvánku.<1></1> Požádejte správce pracovního prostoru o pozvánku a zkuste to znovu.", "Sorry, an unknown error occurred.": "Sorry, an unknown error occurred.", "Choose a workspace": "Choose a workspace", "Choose an {{ appName }} workspace or login to continue connecting this app": "Choose an {{ appName }} workspace or login to continue connecting this app", "Create workspace": "Create workspace", "Setup your workspace by providing a name and details for admin login. You can change these later.": "Setup your workspace by providing a name and details for admin login. You can change these later.", "Workspace name": "Název pracovního prostoru", "Admin name": "Admin name", "Admin email": "Admin email", "Login": "Přihlášení", "Error": "Chyba", "Failed to load configuration.": "Nepodařilo se načíst konfiguraci.", "Check the network requests and server logs for full details of the error.": "Zkontrolujte síťové požadavky a protokoly serveru pro úplné podrobnosti o chybě.", "Custom domain setup": "Nastavení vlastní <PERSON>", "Almost there": "Téměř <PERSON>ovo", "Your custom domain is successfully pointing at Outline. To complete the setup process please contact support.": "Vaše vlastní doména úspěšně odkazuje na Outline. Chcete-li dokončit proces nastavení, kontaktujte podporu.", "Choose workspace": "Vyberte pracovní prostor", "This login method requires choosing your workspace to continue": "Chcete-l<PERSON>, tato metoda přihlášení vyžaduje výběr pracovního prostoru", "Check your email": "Zkontrolujte svůj e-mail", "A magic sign-in link has been sent to the email <em>{{ emailLinkSentTo }}</em> if an account exists.": "Na e-mail <em>{{ emailLinkSentTo }}</em> byl odeslán odkaz pro přihlášení, pokud účet existuje.", "Back to login": "Zpět na přihlášení", "Get started by choosing a sign-in method for your new workspace below…": "Začněte výběrem způsobu přihlášení pro svůj nový pracovní prostor pod…", "Login to {{ authProviderName }}": "<PERSON>řihlaste se na {{ authProviderName }}", "You signed in with {{ authProviderName }} last time.": "Naposledy jste se přihlásili pomocí {{ authProviderName }}.", "Or": "Nebo", "Already have an account? Go to <1>login</1>.": "<PERSON>áte již <PERSON>? <1>Přihlaste se</1>.", "An error occurred": "An error occurred", "The OAuth client could not be found, please check the provided client ID": "The OAuth client could not be found, please check the provided client ID", "The OAuth client could not be loaded, please check the redirect URI is valid": "The OAuth client could not be loaded, please check the redirect URI is valid", "Required OAuth parameters are missing": "Required OAuth parameters are missing", "Authorize": "Authorize", "{{ appName }} wants to access {{ teamName }}": "{{ appName }} wants to access {{ teamName }}", "By <em>{{ developerName }}</em>": "By <em>{{ developerName }}</em>", "{{ appName }} will be able to access your account and perform the following actions": "{{ appName }} will be able to access your account and perform the following actions", "read": "read", "write": "write", "read and write": "read and write", "API keys": "API keys", "attachments": "attachments", "collections": "collections", "comments": "comments", "documents": "documents", "events": "events", "groups": "groups", "integrations": "integrations", "notifications": "notifications", "reactions": "reactions", "pins": "pins", "shares": "shares", "users": "users", "teams": "teams", "workspace": "workspace", "Read all data": "Read all data", "Write all data": "Write all data", "Any collection": "Jakákoli sbírka", "All time": "All time", "Past day": "Včera", "Past week": "<PERSON><PERSON><PERSON> t<PERSON>", "Past month": "<PERSON><PERSON><PERSON>", "Past year": "Minulý rok", "Any time": "Kdykoliv", "Remove document filter": "Odebrat filtr dokumentů", "Any status": "Libovolný stav", "Remove search": "Odstranit vyhledávání", "Any author": "Jakýkoliv autor", "Search titles only": "Hledat pouze názvy", "Something went wrong": "Something went wrong", "Please try again or contact support if the problem persists": "Please try again or contact support if the problem persists", "No documents found for your search filters.": "Pro zadaný požadavek nebyly nalezeny žádné dokumenty.", "Create personal API keys to authenticate with the API and programatically control\n      your workspace's data. For more details see the <em>developer documentation</em>.": "Create personal API keys to authenticate with the API and programatically control\n      your workspace's data. For more details see the <em>developer documentation</em>.", "API keys have been disabled by an admin for your account": "API keys have been disabled by an admin for your account", "Application access": "Application access", "Manage which third-party and internal applications have been granted access to your {{ appName }} account.": "Manage which third-party and internal applications have been granted access to your {{ appName }} account.", "API": "API", "API keys can be used to authenticate with the API and programatically control\n          your workspace's data. For more details see the <em>developer documentation</em>.": "API keys can be used to authenticate with the API and programatically control\n          your workspace's data. For more details see the <em>developer documentation</em>.", "Application published": "Application published", "Application updated": "Application updated", "Client secret rotated": "Client secret rotated", "Rotate secret": "Rotate secret", "Rotating the client secret will invalidate the current secret. Make sure to update any applications using these credentials.": "Rotating the client secret will invalidate the current secret. Make sure to update any applications using these credentials.", "Displayed to users when authorizing": "Displayed to users when authorizing", "Developer information shown to users when authorizing": "Developer information shown to users when authorizing", "Developer name": "Developer name", "Developer URL": "Developer URL", "Allow users from other workspaces to authorize this app": "Allow users from other workspaces to authorize this app", "Credentials": "Credentials", "OAuth client ID": "OAuth client ID", "The public identifier for this app": "The public identifier for this app", "OAuth client secret": "OAuth client secret", "Store this value securely, do not expose it publicly": "Store this value securely, do not expose it publicly", "Where users are redirected after authorizing this app": "Where users are redirected after authorizing this app", "Authorization URL": "Authorization URL", "Where users are redirected to authorize this app": "Where users are redirected to authorize this app", "Applications allow you to build internal or public integrations with Outline and provide secure access via OAuth. For more details see the <em>developer documentation</em>.": "Applications allow you to build internal or public integrations with Outline and provide secure access via OAuth. For more details see the <em>developer documentation</em>.", "by {{ name }}": "by {{ name }}", "Last used": "<PERSON><PERSON><PERSON><PERSON>", "No expiry": "Bez vypršení platnosti", "Restricted scope": "Restricted scope", "API key copied to clipboard": "Klíč API byl zkopírován do schránky", "Copied": "Zkopírováno", "Are you sure you want to revoke the {{ tokenName }} token?": "Skuteč<PERSON>ě chcete zrušit token {{ tokenName }}?", "Disconnect integration": "<PERSON><PERSON><PERSON><PERSON> integra<PERSON>", "Connected": "Připojeno", "Disconnect": "<PERSON><PERSON><PERSON><PERSON>", "Disconnecting": "Od<PERSON>jován<PERSON>", "Allowed domains": "<PERSON><PERSON><PERSON><PERSON>", "The domains which should be allowed to create new accounts using SSO. Changing this setting does not affect existing user accounts.": "<PERSON><PERSON><PERSON>, které by m<PERSON><PERSON> mít povoleno vytvářet nové účty pomocí SSO. Změna tohoto nastavení nemá vliv na stávající uživatelské účty.", "Remove domain": "Odstranit doménu", "Add a domain": "<PERSON><PERSON><PERSON><PERSON>", "Save changes": "Uložit změny", "Please choose a single file to import": "Vyberte prosím jeden soubor k <PERSON>u", "Your import is being processed, you can safely leave this page": "Váš import se zpracovává, můžete tuto stránku bezpečně opustit", "File not supported – please upload a valid ZIP file": "So<PERSON>or nen<PERSON> – nahrajte prosím platný soubor ZIP", "Set the default permission level for collections created from the import": "Set the default permission level for collections created from the import", "Uploading": "Nahrávání", "Start import": "Spustit import", "Processing": "Zpracovává se", "Expired": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Completed": "Dokončeno", "Failed": "Chyba", "All collections": "Všechny sbírky", "Import deleted": "Import odstraněn", "Export deleted": "Export odstraněn", "Are you sure you want to delete this import?": "Jste si jisti, že chcete tento import smazat?", "Deleting this import will also delete all collections and documents that were created from it. This cannot be undone.": "Smazáním tohoto importu také smažete všechny kolekce a dokumenty, které z něj byly vytvořeny. Tuto akci nelze vrátit zpět.", "Check server logs for more details.": "Zkontrolujte logy serveru pro více podrobností.", "{{userName}} requested": "{{userName}} žá<PERSON><PERSON> o", "Groups are for organizing your team. They work best when centered around a function or a responsibility — Support or Engineering for example.": "Skupiny slouží k uspořádání vašeho týmu. <PERSON><PERSON><PERSON><PERSON>, jsou-li soustředěny kolem funkce nebo odpovědnosti – například podpory nebo inženýrství.", "You’ll be able to add people to the group next.": "D<PERSON>le budete moci přidávat uživatele do skupiny.", "You can edit the name of this group at any time, however doing so too often might confuse your team mates.": "Název této skupiny můžete kdykoli upravit, ale příliš časté změny mohou způsobit zmatení ostatních už<PERSON>ů.", "Are you sure about that? Deleting the <em>{{groupName}}</em> group will cause its members to lose access to collections and documents that it is associated with.": "Jsi si tím jisti? Odstranění skupiny <em>{{groupName}}</em> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, že její členové ztratí přístup ke sbírkám a dokumentům, se kterými je spojena.", "Add people to {{groupName}}": "P<PERSON><PERSON>t už<PERSON> do {{groupName}}", "{{userName}} was removed from the group": "Uživatel {{userName}} byl odstraněn ze skupiny", "Add and remove members to the <em>{{groupName}}</em> group. Members of the group will have access to any collections this group has been added to.": "Přidejte a odeberte členy do skupiny <em>{{groupName}}</em>. Členové skupiny budou mít přístup ke všem sbírkám, do kterých byla tato skupina přidána.", "Add people": "Přidat <PERSON>", "Listing members of the <em>{{groupName}}</em> group.": "<PERSON><PERSON><PERSON> skupiny <em>{{groupName}}</em>.", "This group has no members.": "<PERSON><PERSON> sku<PERSON> nemá žádné <PERSON>.", "{{userName}} was added to the group": "{{userName}} byl přidán do skupiny", "Could not add user": "Uživatele se nepodařilo p<PERSON>", "Add members below to give them access to the group. Need to add someone who’s not yet a member?": "Přidejte členy níže a udělte jim přístup do skupiny. Potřebujete přidat někoho, kdo ještě není <PERSON>?", "Invite them to {{teamName}}": "Pozvat do {{teamName}}", "Ask an admin to invite them first": "Požáde<PERSON><PERSON>, aby je nejd<PERSON><PERSON><PERSON> p<PERSON>", "Search by name": "Hledat podle názvu", "Search people": "Hledat uživatele", "No people matching your search": "Vašemu vyhledávání neodpovídají žá<PERSON>", "No people left to add": "Nezbývají <PERSON><PERSON>, kter<PERSON> by by<PERSON> m<PERSON>", "Date created": "Datum v<PERSON>", "Crop Image": "Crop Image", "Crop image": "Crop image", "How does this work?": "Jak to funguje?", "You can import a zip file that was previously exported from the JSON option in another instance. In {{ appName }}, open <em>Export</em> in the Settings sidebar and click on <em>Export Data</em>.": "Můžete importovat soubor zip, který byl dříve exportován z možnosti JSON v jiné instanci. V {{ appName }} otevřete <em>Export</em> v postranním panelu Nastavení a klikněte na <em>Exportovat data</em>.", "Drag and drop the zip file from the JSON export option in {{appName}}, or click to upload": "Přetáhněte soubor zip z možnosti exportu JSON v {{appName}} nebo kliknutím nahrajte", "Canceled": "<PERSON><PERSON><PERSON><PERSON>", "Import canceled": "Import byl <PERSON>en", "Are you sure you want to cancel this import?": "Jste si jisti, že chcete zrušit tento import?", "Canceling": "Probíhá zrušení", "Canceling this import will discard any progress made. This cannot be undone.": "Zrušením tohoto importu bude odstraněn jakýkoliv pokrok. Toto nelze vrátit zpět.", "{{ count }} document imported": "{{ count }} dokument importován", "{{ count }} document imported_plural": "{{ count }} <PERSON><PERSON><PERSON><PERSON>", "You can import a zip file that was previously exported from an Outline installation – collections, documents, and images will be imported. In Outline, open <em>Export</em> in the Settings sidebar and click on <em>Export Data</em>.": "Můžete importovat soubor zip, který byl dříve exportován z instalace Outline – budou importovány sbírky, dokumenty a obrázky. V aplikaci Outline otevřete <em>Export</em> na postranním panelu Nastavení a klikněte na <em>Exportovat data</em>.", "Drag and drop the zip file from the Markdown export option in {{appName}}, or click to upload": "Přetáhněte soubor zip z možnosti exportu Markdown z {{appName}} nebo kliknutím nahrajte", "Configure": "Configure", "Connect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Last active": "Poslední aktivita", "Guest": "Host", "Never used": "Never used", "Are you sure you want to delete the {{ appName }} application? This cannot be undone.": "Are you sure you want to delete the {{ appName }} application? This cannot be undone.", "Shared by": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Date shared": "Datum zveřejnění", "Last accessed": "Poslední přístup", "Domain": "<PERSON><PERSON><PERSON>", "Views": "Zobrazení", "All roles": "<PERSON><PERSON><PERSON><PERSON> role", "Admins": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Editors": "<PERSON><PERSON><PERSON><PERSON>", "All status": "All status", "Active": "Aktivní", "Left": "<PERSON><PERSON><PERSON>", "Right": "Vpravo", "Settings saved": "Nastavení <PERSON>", "Logo updated": "Logo aktualizováno", "Unable to upload new logo": "Nelze nahrát nové logo", "Delete workspace": "Odstranit pracovní prostor", "These settings affect the way that your workspace appears to everyone on the team.": "Tato nastavení ovlivňují z<PERSON>, jak<PERSON><PERSON> se váš pracovní prostor zobrazuje všem v týmu.", "Display": "Zobrazit", "The logo is displayed at the top left of the application.": "Logo se zobrazí v levém horním rohu aplikace.", "The workspace name, usually the same as your company name.": "Název pracovn<PERSON><PERSON> pro<PERSON>, ob<PERSON><PERSON> stejný jako název vaš<PERSON> společnosti.", "Description": "Description", "A short description of your workspace.": "A short description of your workspace.", "Theme": "Motiv", "Customize the interface look and feel.": "Přizpůsobit vzhled a chování.", "Reset theme": "Obnovit motiv", "Accent color": "Barva zvýraznění", "Accent text color": "Doplňková barva textu", "Public branding": "Veřejný vzhled", "Show your workspace logo, description, and branding on publicly shared pages.": "Show your workspace logo, description, and branding on publicly shared pages.", "Table of contents position": "Umístění obs<PERSON> ta<PERSON>ky", "The side to display the table of contents in relation to the main content.": "The side to display the table of contents in relation to the main content.", "Behavior": "Chování", "Subdomain": "Subdoména", "Your workspace will be accessible at": "Váš pracovní prostor bude přístupný na", "Choose a subdomain to enable a login page just for your team.": "Vyberte subdoménu a povolte přihlašovací stránku pouze pro svůj tým.", "This is the screen that workspace members will first see when they sign in.": "<PERSON><PERSON> je <PERSON>, k<PERSON><PERSON> členové pracovního prostoru uvidí jako první, k<PERSON><PERSON> se přihlásí.", "Danger": "Nebezpečí", "You can delete this entire workspace including collections, documents, and users.": "Můžete odstranit celý tento pracovní prostor včetně kolekcí, dokumentů a uživatelů.", "Export data": "Exportovat data", "A full export might take some time, consider exporting a single document or collection. You may leave this page once the export has started – if you have notifications enabled, we will email a link to <em>{{ userEmail }}</em> when it’s complete.": "A full export might take some time, consider exporting a single document or collection. You may leave this page once the export has started – if you have notifications enabled, we will email a link to <em>{{ userEmail }}</em> when it’s complete.", "Recent exports": "Nedávné exporty", "Manage optional and beta features. Changing these settings will affect the experience for all members of the workspace.": "Spravujte volitelné funkce a funkce beta. Změna těchto nastavení ovlivní prostředí všech členů pracovního prostoru.", "Separate editing": "Odd<PERSON><PERSON><PERSON>", "When enabled documents have a separate editing mode by default instead of being always editable. This setting can be overridden by user preferences.": "Pokud je povoleno, dokumenty mají ve výchozím nastavení oddělený re<PERSON>, místo aby byly v<PERSON>dy upraviteln<PERSON>. Toto nastavení může být přepsáno uživatelskými nastaveními.", "When enabled team members can add comments to documents.": "<PERSON><PERSON><PERSON><PERSON> <PERSON>, mohou <PERSON> týmu přidávat komentáře k dokumentům.", "Create a group": "Vytvořit skupinu", "Could not load groups": "Could not load groups", "New group": "<PERSON><PERSON> skupina", "Groups can be used to organize and manage the people on your team.": "Skupiny lze použít k organizaci a správě lidí ve vašem týmu.", "No groups have been created yet": "<PERSON><PERSON><PERSON> vytvořeny žádné skupiny", "Import a zip file of Markdown documents (exported from version 0.67.0 or earlier)": "Importujte soubor zip s dokumenty Markdown (exportované z verze 0.67.0 nebo starší)", "Import data": "Importovat data", "Import a JSON data file exported from another {{ appName }} instance": "Importujte datový soubor JSON exportovaný z instance {{ appName }}", "Import pages from a Confluence instance": "Importujte stránky z aplikace Confluence", "Enterprise": "Podnik", "Quickly transfer your existing documents, pages, and files from other tools and services into {{appName}}. You can also drag and drop any HTML, Markdown, and text documents directly into Collections in the app.": "R<PERSON>le přeneste své stávající dokumenty, str<PERSON><PERSON> a soubory z jiných nástrojů a služeb do {{appName}}. <PERSON><PERSON><PERSON><PERSON>li HTML, <PERSON>down a textové dokumenty můžete také přetáhnout přímo do sbírky v aplikaci.", "Recent imports": "Nedávné importy", "Configure a variety of integrations with third-party services.": "Configure a variety of integrations with third-party services.", "Could not load members": "Could not load members", "Everyone that has signed into {{appName}} is listed here. It’s possible that there are other users who have access through {{signinMethods}} but haven’t signed in yet.": "Zde je uveden <PERSON>, kdo se přihlásil do {{appName}}. <PERSON>, že existuje více u<PERSON>, k<PERSON><PERSON><PERSON> mají p<PERSON> přes {{signinMethods}}, ale ještě se nepřihlásili.", "Receive a notification whenever a new document is published": "Dostat upozornění, k<PERSON><PERSON> bude publikován nový obsah", "Document updated": "Dokument aktualizován", "Receive a notification when a document you are subscribed to is edited": "Dostat upozornění, k<PERSON><PERSON> je dokument, k jehož odběru jste přihlášeni, upraven", "Comment posted": "<PERSON><PERSON><PERSON><PERSON> zveřej<PERSON>ě<PERSON>", "Receive a notification when a document you are subscribed to or a thread you participated in receives a comment": "Dostat upozornění, <PERSON><PERSON><PERSON>, k jehož odběru jste přihlášeni, nebo v<PERSON>, jehož jste se účastnili, ob<PERSON><PERSON><PERSON> k<PERSON>", "Mentioned": "<PERSON><PERSON><PERSON><PERSON>", "Receive a notification when someone mentions you in a document or comment": "Dostat upozornění, k<PERSON>ž se o vás někdo zmíní v dokumentu nebo komentáři", "Receive a notification when a comment thread you were involved in is resolved": "Dostat upozornění, k<PERSON><PERSON> bude vyřešeno vlákno komentářů, do kterého jste byli zapojeni", "Collection created": "Sbírka vytvořena", "Receive a notification whenever a new collection is created": "Dostat upozornění, k<PERSON><PERSON> bude vytvořena nová sbírka", "Invite accepted": "Pozvánka přijata", "Receive a notification when someone you invited creates an account": "Dostat upozornění, k<PERSON><PERSON>, koho j<PERSON> p<PERSON>, v<PERSON><PERSON><PERSON><PERSON>", "Invited to document": "Pozván/a do dokumentu", "Receive a notification when a document is shared with you": "Dostat oznámení, k<PERSON>ž získáte přístup k dokumentu", "Invited to collection": "Pozván do kolekce", "Receive a notification when you are given access to a collection": "Dostat upozornění, k<PERSON>ž získáte přístup k nové kolekci", "Export completed": "Export dokončen", "Receive a notification when an export you requested has been completed": "Dostat upozornění, k<PERSON>ž byl vámi požadovaný export dokončen", "Getting started": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Tips on getting started with features and functionality": "<PERSON><PERSON><PERSON>, jak za<PERSON><PERSON>", "New features": "<PERSON><PERSON>", "Receive an email when new features of note are added": "Dostat e-mail, k<PERSON><PERSON> budou přidány nové funk<PERSON> poznámky", "Notifications saved": "Upozornění ul<PERSON>ž<PERSON>", "Unsubscription successful. Your notification settings were updated": "Odhlášení bylo <PERSON>. Nastavení oznámení bylo aktualiz<PERSON>", "Manage when and where you receive email notifications.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kdy a kde budete dostávat e-mailová upozornění.", "The email integration is currently disabled. Please set the associated environment variables and restart the server to enable notifications.": "Integrace e-mailu je momentálně zakázána. Nastavte přidružené proměnné parametry a restartujte server, abyste povolili oznámení.", "Preferences saved": "Nastavení uložena", "Delete account": "Odstranit účet", "Manage settings that affect your personal experience.": "Spra<PERSON>jte nastavení, která ovlivňují vaše uživatelské rozhraní.", "Language": "Jazyk", "Choose the interface language. Community translations are accepted though our <2>translation portal</2>.": "Vyberte jazyk rozhraní. Překlady komunity jsou přijímány prostřednictvím našeho <2>překladatelského portálu</2>.", "Choose your preferred interface color scheme.": "<PERSON><PERSON><PERSON><PERSON> si <PERSON>ované téma rozhraní.", "Use pointer cursor": "Použít kurzor", "Show a hand cursor when hovering over interactive elements.": "Při najetí myší na interaktivní prvky zobrazit kurzor ruky.", "Show line numbers": "Zobrazit čísla řádků", "Show line numbers on code blocks in documents.": "Zobrazit čísla řádků na blocích kódu v dokumentech.", "When enabled, documents have a separate editing mode. When disabled, documents are always editable when you have permission.": "Pokud je povoleno, dokumenty maj<PERSON> sa<PERSON> re<PERSON><PERSON>. Pokud je z<PERSON>, dokumenty jsou v<PERSON><PERSON> upraviteln<PERSON>, pokud máte oprávnění.", "Remember previous location": "Zapamatovat předchozí umístění", "Automatically return to the document you were last viewing when the app is re-opened.": "Automaticky se vracet k dokumentu, kter<PERSON> jste si naposledy prohlédli před ukončením aplikace.", "Smart text replacements": "Chytré nahrazení textu", "Auto-format text by replacing shortcuts with symbols, dashes, smart quotes, and other typographical elements.": "Automatické <PERSON>í textu nahrazením symboly, poml<PERSON><PERSON><PERSON>, chytrými uvozovkami a dalšími typografickými prvky.", "You may delete your account at any time, note that this is unrecoverable": "Účet můžete kdykoliv odstranit, tento krok je neobnovitelný", "Profile saved": "<PERSON><PERSON>", "Profile picture updated": "Profilový obrázek byl úspěšně aktualizován", "Unable to upload new profile picture": "Nelze nahrát nový profilový obrázek", "Manage how you appear to other members of the workspace.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, jak se zobrazujete ostatním členům pracovního prostoru.", "Photo": "Fotka", "Choose a photo or image to represent yourself.": "<PERSON><PERSON>berte si fotografii nebo obr<PERSON>zek, k<PERSON><PERSON> vás bude reprezentovat.", "This could be your real name, or a nickname — however you’d like people to refer to you.": "<PERSON><PERSON><PERSON><PERSON> to být vaše skutečné jméno nebo přezdívka – jak<PERSON><PERSON> ch<PERSON>, aby vás <PERSON>.", "Email address": "E-mailová adresa", "Are you sure you want to require invites?": "Opravdu chcete vyžadovat pozvánky?", "New users will first need to be invited to create an account. <em>Default role</em> and <em>Allowed domains</em> will no longer apply.": "K vytvoření účtu bude nutné nejprve pozvat nové už<PERSON>le. <em><PERSON><PERSON><PERSON><PERSON><PERSON> role</em> a <em>Po<PERSON><PERSON><PERSON> do<PERSON></em> již nebudou platit.", "Settings that impact the access, security, and content of your workspace.": "Nastavení ovlivňující přístup, bezpečnost a obsah vašeho pracovního prostoru.", "Allow members to sign-in with {{ authProvider }}": "Povolit členům přihlásit se pomocí {{ authProvider }}", "Disabled": "Deaktivováno", "Allow members to sign-in using their email address": "Povolit členům přihlášení pomocí jejich e-mailové adresy", "The server must have SMTP configured to enable this setting": "<PERSON>by bylo mo<PERSON> toto nastavení povo<PERSON>, musí mít server nakonfigurován SMTP", "Access": "Přístup", "Allow users to send invites": "Povolit uživatelům posílat <PERSON>", "Allow editors to invite other people to the workspace": "Povolit editorům posílat pozvánky do pracoviště", "Require invites": "Vyžadovat pozvání", "Require members to be invited to the workspace before they can create an account using SSO.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, aby členové byli pozváni do pracovního prostoru, než si vytvoří účet pomocí jednotného přihlášení.", "Default role": "Výchozí <PERSON> už<PERSON><PERSON>", "The default user role for new accounts. Changing this setting does not affect existing user accounts.": "Výchozí uživatelská role pro nové účty. Změna tohoto nastavení nemá vliv na stávající uživatelské účty.", "When enabled, documents can be shared publicly on the internet by any member of the workspace": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>, doku<PERSON>y mohou být veřejně sdíleny na internetu kterýmkoli členem pracovního prostoru", "Viewer document exports": "Exportování <PERSON>ů", "When enabled, viewers can see download options for documents": "<PERSON><PERSON><PERSON> je tato mo<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>í<PERSON><PERSON> uvidí možnosti stahování dokumentů", "Users can delete account": "Users can delete account", "When enabled, users can delete their own account from the workspace": "When enabled, users can delete their own account from the workspace", "Rich service embeds": "Bohaté vložené s<PERSON>", "Links to supported services are shown as rich embeds within your documents": "Odkazy na podporované služby se ve vašich dokumentech zobrazují jako rozšířené vložení", "Collection creation": "Vytvoření sbírky", "Allow editors to create new collections within the workspace": "Umožnit členům vytvářet v rámci pracoviště nové kolekce", "Workspace creation": "Vytvoření pracovního prostoru", "Allow editors to create new workspaces": "Povolit editor<PERSON><PERSON> v<PERSON> nové pracovní prostory", "Could not load shares": "Could not load shares", "Sharing is currently disabled.": "Sdílení je momentálně zakázáno.", "You can globally enable and disable public document sharing in the <em>security settings</em>.": "Můžete globálně povolit a zakázat sdílení dokumentů v nastavení <em>zabezpečení</em>.", "Documents that have been shared are listed below. Anyone that has the public link can access a read-only version of the document until the link has been revoked.": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, j<PERSON><PERSON> n<PERSON>. <PERSON><PERSON><PERSON><PERSON>, kdo má veřejný od<PERSON>z, má přístup k verzi dokumentu pouze pro čtení, dokud nebude odkaz odstranět.", "You can create templates to help your team create consistent and accurate documentation.": "Můžete vytvářet šablony, kter<PERSON> vašemu týmu pomohou vytvořit konzistentní a přesnou dokumentaci.", "Alphabetical": "Abecedně", "There are no templates just yet.": "Zatím nejsou k dispozici žádné šablony.", "A confirmation code has been sent to your email address, please enter the code below to permanently destroy this workspace.": "Potvrzovací kód byl odeslán na vaši e-mailovou adresu, zadejte jej prosím níže pro trvalé odstranění tohoto pracovního prostoru.", "Confirmation code": "Potvrzovací kód", "Deleting the <1>{{workspaceName}}</1> workspace will destroy all collections, documents, users, and associated data. You will be immediately logged out of {{appName}}.": "Smazání <1>{{workspaceName}}</1> pracovního prostoru odstraní všechny kolekce, dokumenty, uživatele a související data. Budete okamžitě odhlášeni z {{appName}}.", "Please note that workspaces are completely separated. They can have a different domain, settings, users, and billing.": "Vezměte prosím na vědomí, že pracoviště jsou zcela odděleny. <PERSON><PERSON> mít ji<PERSON>, na<PERSON><PERSON><PERSON>, u<PERSON><PERSON><PERSON> a fakturaci.", "You are creating a new workspace using your current account — <em>{{email}}</em>": "Vytváříte nový pracovní prostor pomocí svého aktuálního <PERSON> — <em>{{email}}</em>", "To create a workspace under another email please sign up from the homepage": "Chcete-li vytvořit pracovní prostor pod jiným e-mailem, zaregistrujte se z domovské stránky", "Trash emptied": "<PERSON>š vyprázdněn", "Are you sure you want to permanently delete all the documents in Trash? This action is immediate and cannot be undone.": "Opravdu chcete trvale odstranit všechny dokumenty v koši? Tato akce je okamžitá a nelze ji vrátit zpět.", "Recently deleted": "<PERSON><PERSON><PERSON><PERSON>", "Trash is empty at the moment.": "<PERSON><PERSON> je <PERSON> pr<PERSON>dný.", "A confirmation code has been sent to your email address, please enter the code below to permanently destroy your account.": "Na vaši e-mailovou adresu byl z<PERSON>lán <PERSON>rz<PERSON> kód, zadejte jej prosím níže, aby byl vá<PERSON> trvale odstraněn.", "Are you sure? Deleting your account will destroy identifying data associated with your user and cannot be undone. You will be immediately logged out of {{appName}} and all your API tokens will be revoked.": "Jste si jisti? Smazání účtu zničí identifikující data spojená s vaším uživatelem a nelze je vrátit zpět. Budete okamžitě odhlášeni z {{appName}} a všechny vaše API tokeny budou zrušeny.", "Delete my account": "Odstranit můj účet", "Today": "Dnes", "Yesterday": "Včera", "Last week": "<PERSON><PERSON><PERSON> t<PERSON>", "This month": "<PERSON><PERSON>", "Last month": "<PERSON><PERSON><PERSON>", "This year": "<PERSON>to rok", "Expired yesterday": "Vypršel včera", "Expired {{ date }}": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{ date }}", "Expires today": "<PERSON>ypr<PERSON><PERSON>", "Expires tomorrow": "Vyprší zí<PERSON>", "Expires {{ date }}": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{ date }}", "Whoops, you need to accept the permissions in GitHub to connect {{appName}} to your workspace. Try again?": "<PERSON><PERSON><PERSON>, pro připojení {{appName}} k vašemu týmu musíte přijmout oprávnění ve Slacku. Zkusit znovu?", "Something went wrong while authenticating your request. Please try logging in again.": "Při ověřování vašeho požadavku se něco pokazilo. Zkuste se prosím přihlásit znovu.", "The owner of GitHub account has been requested to install the {{githubAppName}} GitHub app. Once approved, previews will be shown for respective links.": "Vlastník GitHub účtu byl požádán o instalaci aplikace {{githubAppName}} GitHub. Po schválení budou náhledy zobrazeny pro příslušné odkazy.", "Enable previews of GitHub issues and pull requests in documents by connecting a GitHub organization or specific repositories to {appName}.": "Povolit náhledy GitHub problémů a požadavky na natažení v dokumentech připojením GitHub organizace nebo konkrétních repositářů k {appName}.", "Enabled by {{integrationCreatedBy}}": "Povoleno {{integrationCreatedBy}}", "Disconnecting will prevent previewing GitHub links from this organization in documents. Are you sure?": "Odpojení zabrání zobrazení náhledu odkazů na GitHub z této organizace v dokumentech. Jste si jisti?", "The GitHub integration is currently disabled. Please set the associated environment variables and restart the server to enable the integration.": "Integrace e-mailu je momentálně zakázána. Pokud chcete p<PERSON>, nejdříve nastavte příslušné proměnné a restartujte server.", "Google Analytics": "Google Analytics", "Add a Google Analytics 4 measurement ID to send document views and analytics from the workspace to your own Google Analytics account.": "Přidejte ID měření Google Analytics 4, aby<PERSON> mohli odesílat zobrazení dokumentů a analýzy z pracovního prostoru do svého vlastního účtu Google Analytics.", "Measurement ID": "ID měření", "Create a \"Web\" stream in your Google Analytics admin dashboard and copy the measurement ID from the generated code snippet to install.": "Na hlavním panelu administrátora Google Analytics vytvořte stream „Web“ a zkopírujte ID měření z vygenerovaného fragmentu kódu k instalaci.", "Whoops, you need to accept the permissions in Linear to connect {{appName}} to your workspace. Try again?": "Whoops, you need to accept the permissions in Linear to connect {{appName}} to your workspace. Try again?", "Something went wrong while processing your request. Please try again.": "Something went wrong while processing your request. Please try again.", "Enable previews of Linear issues in documents by connecting a Linear workspace to {appName}.": "Enable previews of Linear issues in documents by connecting a Linear workspace to {appName}.", "Disconnecting will prevent previewing Linear links from this workspace in documents. Are you sure?": "Disconnecting will prevent previewing Linear links from this workspace in documents. Are you sure?", "The Linear integration is currently disabled. Please set the associated environment variables and restart the server to enable the integration.": "The Linear integration is currently disabled. Please set the associated environment variables and restart the server to enable the integration.", "Configure a Matomo installation to send views and analytics from the workspace to your own Matomo instance.": "Nastavte si instalaci Matomo pro odesílání zobrazení a analýz pracovního prostoru do vlastní Matomo instance.", "Instance URL": "URL instance", "The URL of your Matomo instance. If you are using Matomo Cloud it will end in matomo.cloud/": "URL adresa Matomo instance. Pokud používáte Matomo Cloud, adresa má na konci matomo.cloud/", "Site ID": "<PERSON> str<PERSON>", "An ID that uniquely identifies the website in your Matomo instance.": "An ID that uniquely identifies the website in your Matomo instance.", "Whoops, you need to accept the permissions in Notion to connect {{ appName }} to your workspace. Try again?": "Whoops, you need to accept the permissions in Notion to connect {{ appName }} to your workspace. Try again?", "Import pages from Notion": "Import pages from Notion", "Add to Slack": "Přidat do Slacku", "document published": "dokument zveřejněn", "document updated": "dokument aktualizován", "Posting to the <em>{{ channelName }}</em> channel on": "Odesílání příspěvků na kanál <em>{{ channelName }}</em> zapnuto", "These events should be posted to Slack": "<PERSON><PERSON> by m<PERSON><PERSON> b<PERSON><PERSON> zveřejněny na Slacku", "This will prevent any future updates from being posted to this Slack channel. Are you sure?": "Tímto zastavíte zasílání budoucích aktualizací do tohoto kanálu Slack. Chcete pokračovat?", "Whoops, you need to accept the permissions in Slack to connect {{appName}} to your workspace. Try again?": "<PERSON><PERSON><PERSON>, pro připojení {{appName}} k vašemu pracovnímu prostoru musíte přijmout oprávnění ve Slacku. Zkusit znovu?", "Personal account": "Osobní účet", "Link your {{appName}} account to Slack to enable searching and previewing the documents you have access to, directly within chat.": "Napojte váš {{appName}} účet na Slack pro možnost vyhledávání a zobrazení dokumentů, ke kterým máte přístup, a to přímo v chatu.", "Disconnecting your personal account will prevent searching for documents from Slack. Are you sure?": "Odpojení vašeho osobního účtu znepřístupní funkci hledání dokumentů ve Slacku. Pokračovat?", "Slash command": "Příkaz", "Get rich previews of {{ appName }} links shared in Slack and use the <em>{{ command }}</em> slash command to search for documents without leaving your chat.": "<PERSON><PERSON><PERSON><PERSON><PERSON> bohaté n<PERSON> {{ appName }} o<PERSON><PERSON><PERSON><PERSON> sdí<PERSON>ých ve Slacku a pomocí příkazu <em>{{ command }}</em> lo<PERSON><PERSON><PERSON><PERSON> v<PERSON>hledejte dokumenty, ani<PERSON> byste opustili chat.", "This will remove the Outline slash command from your Slack workspace. Are you sure?": "Tímto odstraníte příkaz z vašeho Slack pracoviště. Opravdu chcete pokračovat?", "Connect {{appName}} collections to Slack channels. Messages will be automatically posted to Slack when documents are published or updated.": "Připojte {{appName}} sb<PERSON><PERSON> ke kanálům Slack. Zpr<PERSON>vy budou automaticky odesílány do Slacku, jak<PERSON> budou dokumenty zveřejněny nebo aktualizovány.", "Comment by {{ author }} on \"{{ title }}\"": "{{ author }} okomentoval \"{{ title }}\"", "How to use {{ command }}": "<PERSON><PERSON> {{ command }}", "To search your workspace use {{ command }}. \nType {{ command2 }} help to display this help text.": "Pro vyhledávání ve vašem pracovním prostoru použijte {{ command }}. \nZadejte {{ command2 }} nápovědu k zobrazení tohoto nápovědy.", "Post to Channel": "Zveřejnit v kanálu", "This is what we found for \"{{ term }}\"": "<PERSON><PERSON> jsme <PERSON> pro \"{{ term }}\"", "No results for \"{{ term }}\"": "<PERSON><PERSON><PERSON><PERSON> v<PERSON> pro \"{{ term }}\"", "It looks like you haven’t linked your {{ appName }} account to Slack yet": "<PERSON><PERSON><PERSON><PERSON> to, že jste ještě nepropojili váš {{ appName }} ú<PERSON><PERSON> se <PERSON>", "Link your account": "<PERSON><PERSON><PERSON> v<PERSON>", "Link your account in {{ appName }} settings to search from Slack": "Napojte svůj účet v nastavení {{ appName }} pro možnost hledání ve Slacku", "Configure a Umami installation to send views and analytics from the workspace to your own Umami instance.": "Configure a Umami installation to send views and analytics from the workspace to your own Umami instance.", "The URL of your Umami instance. If you are using Umami Cloud it will begin with {{ url }}": "The URL of your Umami instance. If you are using Umami Cloud it will begin with {{ url }}", "Script name": "Script name", "The name of the script file that Umami uses to track analytics.": "The name of the script file that <PERSON><PERSON> uses to track analytics.", "An ID that uniquely identifies the website in your Umami instance.": "An ID that uniquely identifies the website in your Umami instance.", "Are you sure you want to delete the {{ name }} webhook?": "Opravdu chcete odstranit {{ name }} webhook?", "Webhook updated": "Webhook byl aktualizován", "Update": "Aktualizovat", "Updating": "Aktualizace", "Provide a descriptive name for this webhook and the URL we should send a POST request to when matching events are created.": "Zadejte popisný název tohoto webhooku a adresu URL, na kterou bychom měli odeslat požadavek POST, k<PERSON><PERSON> budou vytvořeny odpovídající události.", "A memorable identifer": "Zapamatovatelný identifikátor", "URL": "URL", "Signing secret": "Přihlašovací klíč", "Subscribe to all events, groups, or individual events. We recommend only subscribing to the minimum amount of events that your application needs to function.": "Přihlaste se k odběru všech událostí, sku<PERSON> nebo jednotlivých událostí. Doporučujeme přihlásit se pouze k minimálnímu počtu událostí, které vaše aplikace potřebuje ke svému fungování.", "All events": "Všechny události", "All {{ groupName }} events": "Všechny události {{ groupName }}", "Delete webhook": "Odstranit webhook", "Subscribed events": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Edit webhook": "Upravit webhook", "Webhook created": "Webhook vytvořen", "Webhooks": "Webhooky", "New webhook": "Nový webhook", "Webhooks can be used to notify your application when events happen in {{appName}}. Events are sent as a https request with a JSON payload in near real-time.": "Webhooky lze použít k upozornění vaší aplikace, k<PERSON>ž dojde k události v {{appName}}. Události jsou odesílány jako požadavek https s datovou částí JSON téměř v reálném čase.", "Inactive": "Neaktivní", "Create a webhook": "Vytvořit webhook", "Zapier is a platform that allows {{appName}} to easily integrate with thousands of other business tools. Automate your workflows, sync data, and more.": "<PERSON><PERSON><PERSON> je <PERSON>a, k<PERSON><PERSON> um<PERSON> {{appName}} snadn<PERSON> integraci s tisíci dalš<PERSON>ch obchodních nástrojů. Automatizujte své pracovní postupy, synchronizujte data a další.", "Never logged in": "<PERSON><PERSON>", "Online now": "Online", "Online {{ timeAgo }}": "Online {{ timeAgo }}", "Viewed just now": "Právě zobrazeno", "You updated {{ timeAgo }}": "Naposledy aktualizováno před {{ timeAgo }}", "{{ user }} updated {{ timeAgo }}": "{{ user }} aktualizova<PERSON> před {{ timeAgo }}", "You created {{ timeAgo }}": "<PERSON><PERSON><PERSON><PERSON><PERSON> jste před {{ timeAgo }}", "{{ user }} created {{ timeAgo }}": "{{ user }} v<PERSON><PERSON><PERSON><PERSON> před {{ timeAgo }}", "Error loading data": "Error loading data"}
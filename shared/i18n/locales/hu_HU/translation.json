{"New API key": "Új API-kulcs", "Open collection": "<PERSON>yűj<PERSON><PERSON><PERSON>", "New collection": "Új g<PERSON>j<PERSON>y", "Create a collection": "Gyűjtemény létrehozása", "Edit": "Szerkesztés", "Edit collection": "Gyűjtemény szerkesztése", "Permissions": "Jogosultságok", "Collection permissions": "Gyűj<PERSON><PERSON><PERSON>", "Share this collection": "Gyűjtemény megosztása", "Search in collection": "Keresés a gyűjteményben", "Star": "Csillagozás", "Unstar": "Csillagozás megszüntetése", "Subscribe": "Felirat<PERSON>z<PERSON>", "Subscribed to document notifications": "Feliratkozva a dokumentum értesítéseire", "Unsubscribe": "Leiratkozás", "Unsubscribed from document notifications": "Leiratkozva a dokumentum értesítéseiről", "Archive": "Archiválás", "Archive collection": "Gyűjtemény archiválása", "Collection archived": "Gyűjtemény archiválva", "Archiving": "Archiválás", "Archiving this collection will also archive all documents within it. Documents from the collection will no longer be visible in search results.": "A gyűjtemény archiválása a benne lévő összes dokumentumot is archiválja. A gyűjteményből származó dokumentumok többé nem lesznek láthatóak a keresési eredményekben.", "Restore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Collection restored": "<PERSON>yűj<PERSON><PERSON><PERSON>", "Delete": "Törlés", "Delete collection": "Gyűjtemény törlése", "New template": "<PERSON><PERSON> sablon", "Delete comment": "Hozzászólás törlése", "Mark as resolved": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Thread resolved": "<PERSON><PERSON><PERSON><PERSON>", "Mark as unresolved": "Meg<PERSON><PERSON><PERSON><PERSON><PERSON>", "View reactions": "Reagálások megtekintése", "Reactions": "Reag<PERSON>lások", "Copy ID": "Azonosító másolása", "Clear IndexedDB cache": "IndexedDB gyorsítótárának törlése", "IndexedDB cache cleared": "Az IndexedDB gyorsítótára törölve", "Toggle debug logging": "Hibakeresési naplózás be- vagy k<PERSON>a", "Debug logging enabled": "Hibakeresési naplózás engedélyezve", "Debug logging disabled": "Hibakeresési naplózás letiltva", "Development": "Fe<PERSON>lesz<PERSON>s", "Open document": "Dokumentum megnyitása", "New document": "Új dokumentum", "New draft": "<PERSON>j p<PERSON>zko<PERSON>t", "New from template": "Új, sablonból", "New nested document": "Új beágyazott dokumentum", "Publish": "Közzététel", "Published {{ documentName }}": "A(z) {{ documentName }} közzétéve", "Publish document": "Dokumentum közzététele", "Unpublish": "Közzététel visszavonása", "Unpublished {{ documentName }}": "A(z) {{ documentName }} közzététele v<PERSON>vonva", "Share this document": "Dokumentum megosztása", "HTML": "HTML", "PDF": "PDF", "Exporting": "Exportálás", "Markdown": "<PERSON><PERSON>", "Download": "Letöltés", "Download document": "Dokumentum letöltése", "Copy as Markdown": "Másolás Markdown szövegként", "Markdown copied to clipboard": "Markdown vágólapra másolva", "Copy as text": "Másolás szövegként", "Text copied to clipboard": "Szöveg másolva a vágólapra", "Copy public link": "Nyilvános hivatko<PERSON>ás másolása", "Link copied to clipboard": "Hivatkozás vágólapra másolva", "Copy link": "Hivatkozás másolása", "Copy": "Másolás", "Duplicate": "Kettőzés", "Duplicate document": "Dokumentum kettőzése", "Copy document": "Dokumentum másolása", "collection": "gyűjtemény", "Pin to {{collectionName}}": "Rögzítés ide: {{collectionName}}", "Pinned to collection": "Gyűjteményhez rögzítve", "Pin to home": "Nyitólaphoz rögzítve", "Pinned to home": "Kitűzve a Kezdőlapra", "Pin": "Rög<PERSON><PERSON><PERSON><PERSON>", "Search in document": "Keresés a dokumentumban", "Print": "Nyomtatás", "Print document": "Dokumentum nyomtatása", "Import document": "Dokumentum importálása", "Templatize": "Sablonná alakítás", "Create template": "Sablon létrehozása", "Open random document": "Véletlenszerű dokumentum megnyítása", "Search documents for \"{{searchQuery}}\"": "\"{{searchQuery}}\" keresése a dokumentumokban", "Move to workspace": "Move to workspace", "Move": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Move to collection": "Mozgatás a kollekcióba", "Move {{ documentType }}": "{{ documentType }} áthelyezése", "Are you sure you want to archive this document?": "Are you sure you want to archive this document?", "Document archived": "Dokumentum archiválva", "Archiving this document will remove it from the collection and search results.": "Archiving this document will remove it from the collection and search results.", "Delete {{ documentName }}": "{{ documentName }} törlése", "Permanently delete": "Végleges törlés", "Permanently delete {{ documentName }}": "{{ documentName }} végleges törlése", "Empty trash": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Permanently delete documents in trash": "Permanently delete documents in trash", "Comments": "Hozzászólások", "History": "Előzmények", "Insights": "Részletek", "Disable viewer insights": "Disable viewer insights", "Enable viewer insights": "Enable viewer insights", "Leave document": "Dokumentum mentése", "You have left the shared document": "You have left the shared document", "Could not leave document": "<PERSON>em lehet menteni a dokumentumot", "Home": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Drafts": "Piszkozatok", "Search": "Keresés", "Trash": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Settings": "Beállítások", "Profile": "Profil", "Templates": "Sablonok", "Notifications": "Értesítések", "Preferences": "Beállítások", "Documentation": "Do<PERSON>ment<PERSON><PERSON>ó", "API documentation": "API-dokumentáció", "Toggle sidebar": "Oldalsáv be- vagy k<PERSON>", "Send us feedback": "Visszajelzés küldése", "Report a bug": "Hiba bejelentése", "Changelog": "Változások listája", "Keyboard shortcuts": "Gyorsbillentyűk", "Download {{ platform }} app": "{{ platform }} app letöltése", "Log out": "Kijelentkezés", "Mark notifications as read": "Értesítések olvasottnak jelölése", "Archive all notifications": "Összes értesítés archiválása", "New App": "New App", "New Application": "New Application", "This version of the document was deleted": "This version of the document was deleted", "Link copied": "Hivatkozás másolva", "Dark": "<PERSON><PERSON><PERSON><PERSON>", "Light": "<PERSON>il<PERSON><PERSON>", "System": "Rendszer", "Appearance": "<PERSON><PERSON><PERSON><PERSON>", "Change theme": "Téma váltása", "Change theme to": "Téma váltása erre:", "Switch workspace": "Munkaterület váltása", "Select a workspace": "Válasszon munkaterületet", "New workspace": "<PERSON><PERSON>", "Create a workspace": "Munkaterület létrehozása", "Login to workspace": "Login to workspace", "Invite people": "Mások meghívása", "Invite to workspace": "Meghívása a munkaterületbe", "Promote to {{ role }}": "Előléptetés {{ role }} szerepbe", "Demote to {{ role }}": "Lefokozás {{ role }} szerepbe", "Update role": "<PERSON><PERSON><PERSON>", "Delete user": "Felhasz<PERSON><PERSON><PERSON> törl<PERSON>", "Collection": "Gyűjtemény", "Collections": "Gyűjtemények", "Debug": "Hibakeresés", "Document": "Dokumentum", "Documents": "Dokumentumok", "Recently viewed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Revision": "<PERSON><PERSON><PERSON><PERSON>", "Navigation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Notification": "Értesítés", "People": "Emberek", "Workspace": "Munkaterület", "Recent searches": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "currently editing": "j<PERSON>nleg szerkeszti", "currently viewing": "j<PERSON><PERSON><PERSON>", "previously edited": "előzőleg szerkesztette", "You": "<PERSON><PERSON>", "Viewers": "Megtekintők", "Collections are used to group documents and choose permissions": "A gyűjtemények dokumentuok csoportosítására és jogosultságok beállítására szolgálnak", "Name": "Név", "The default access for workspace members, you can share with more users or groups later.": "The default access for workspace members, you can share with more users or groups later.", "Public document sharing": "Nyilvános dokumentum megosztás", "Allow documents within this collection to be shared publicly on the internet.": "Gyűjteményhez tartozó dokumentumok inteneten való publikus megosztásának engedélyezése.", "Commenting": "Commenting", "Allow commenting on documents within this collection.": "Allow commenting on documents within this collection.", "Saving": "Men<PERSON>s", "Save": "Men<PERSON>s", "Creating": "Létrehozás", "Create": "Létrehozás", "Collection deleted": "Gyűjtemény törölve", "I’m sure – Delete": "Biztos vagyok benne - Törlés", "Deleting": "Törlés", "Are you sure about that? Deleting the <em>{{collectionName}}</em> collection is permanent and cannot be restored, however all published documents within will be moved to the trash.": "Biztos benne? A(z) <em>{{collectionName}}</em> gyűjtemény törlése végleges, és nem lehet majd v<PERSON>, azonban minden benne <PERSON>, publikált dokumentum a kukába kerül.", "Also, <em>{{collectionName}}</em> is being used as the start view – deleting it will reset the start view to the Home page.": "Ezen felül a(z) <em>{{collectionName}}</em> gyűjtemény nyitólapként van beállítva – a törlése visszaállítja a nyitólapot is az eredetire.", "Type a command or search": "<PERSON><PERSON><PERSON> be egy parancsot vagy kere<PERSON>t", "Choose a template": "<PERSON><PERSON><PERSON><PERSON> egy sablont", "Are you sure you want to permanently delete this entire comment thread?": "<PERSON><PERSON><PERSON> ben<PERSON>, hogy véglegesen törölni szeretné az egész hozzászólás s<PERSON>?", "Are you sure you want to permanently delete this comment?": "<PERSON><PERSON><PERSON> ben<PERSON>, hogy véglegesen törölni szeretné a hozzászólást?", "Confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "manage access": "manage access", "view and edit access": "view and edit access", "view only access": "view only access", "no access": "nincs ho<PERSON>", "You do not have permission to move {{ documentName }} to the {{ collectionName }} collection": "You do not have permission to move {{ documentName }} to the {{ collectionName }} collection", "Move document": "Dokumentum áthelyezése", "Moving": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Moving the document <em>{{ title }}</em> to the {{ newCollectionName }} collection will change permission for all workspace members from <em>{{ prevPermission }}</em> to <em>{{ newPermission }}</em>.": "Moving the document <em>{{ title }}</em> to the {{ newCollectionName }} collection will change permission for all workspace members from <em>{{ prevPermission }}</em> to <em>{{ newPermission }}</em>.", "Submenu": "Almenü", "Collections could not be loaded, please reload the app": "A gyűjtemények nem tölthetők be, kérem töltse újra a programot", "Default collection": "Alapértelmezett gyűjtemény", "Start view": "Kezdje el", "Install now": "Telepítés most", "Deleted Collection": "Törölt gyűjtemény", "Untitled": "Névtelen", "Unpin": "<PERSON><PERSON><PERSON><PERSON>", "{{ minutes }}m read": "{{ minutes }}m read", "Select a location to copy": "Select a location to copy", "Document copied": "Dokumentum átmásolva", "Couldn’t copy the document, try again?": "Couldn’t copy the document, try again?", "Include nested documents": "Beágyazott dokumentumokkal együtt", "Copy to <em>{{ location }}</em>": "Copy to <em>{{ location }}</em>", "Search collections & documents": "Keresés a gyűteményekben és dokumentumokban", "No results found": "<PERSON><PERSON><PERSON>", "New": "<PERSON><PERSON>", "Only visible to you": "Csak az Ön számára látható", "Draft": "Piszkozat", "Template": "Sablon", "You updated": "<PERSON><PERSON>", "{{ userName }} updated": "{{ userName }} módosította", "You deleted": "<PERSON><PERSON>", "{{ userName }} deleted": "{{ userName }} t<PERSON><PERSON><PERSON><PERSON>e", "You archived": "<PERSON>n archiv<PERSON>", "{{ userName }} archived": "{{ userName }} archiválta", "Imported": "Import<PERSON>lt", "You created": "<PERSON>n ho<PERSON> l<PERSON>", "{{ userName }} created": "{{ userName }} hozta létre", "You published": "<PERSON><PERSON>", "{{ userName }} published": "{{ userName }} publikálta", "Never viewed": "Sohasem nézték meg", "Viewed": "Megnézve", "in": "itt:", "nested document": "beágyazott dokumentum", "nested document_plural": "beágyazott dokumentumok", "{{ total }} task": "{{ total }} teendő", "{{ total }} task_plural": "{{ total }} teendő", "{{ completed }} task done": "{{ completed }} <PERSON><PERSON><PERSON>", "{{ completed }} task done_plural": "{{ completed }} <PERSON><PERSON><PERSON>", "{{ completed }} of {{ total }} tasks": "{{ completed }} k<PERSON>z {{ total }} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Currently editing": "<PERSON><PERSON><PERSON> szerkeszti", "Currently viewing": "<PERSON><PERSON><PERSON>", "Viewed {{ timeAgo }}": "Megtekintve: {{ timeAgo }}", "Module failed to load": "A modult nem sikerült betölteni", "Loading Failed": "Betöltés si<PERSON>telen", "Sorry, part of the application failed to load. This may be because it was updated since you opened the tab or because of a failed network request. Please try reloading.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, az alkalmazás egy részének betöltése sikertelen. <PERSON><PERSON><PERSON>, hogy f<PERSON><PERSON>, <PERSON><PERSON><PERSON> me<PERSON> a fü<PERSON>, v<PERSON>y h<PERSON><PERSON><PERSON><PERSON><PERSON> kap<PERSON>olati hiba történt. Kérem próbálja újratölteni.", "Reload": "Újratöltés", "Something Unexpected Happened": "Valami váratlan tö<PERSON>ént", "Sorry, an unrecoverable error occurred{{notified}}. Please try reloading the page, it may have been a temporary glitch.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, egy he<PERSON><PERSON>hat<PERSON> hiba történt {{notified}}. K<PERSON>rem próbá<PERSON>ja <PERSON> a lapot, le<PERSON>t, hogy csak <PERSON> a probléma.", "our engineers have been notified": "a mérnökeinket éretsítettük", "Show detail": "Részletek mutatása", "Revision deleted": "Revision deleted", "Current version": "<PERSON><PERSON><PERSON><PERSON> verzi<PERSON>", "{{userName}} edited": "{{userName}} módosította", "{{userName}} archived": "{{userName}} archiválta", "{{userName}} restored": "{{userName}} v<PERSON><PERSON><PERSON>llította", "{{userName}} deleted": "{{userName}} törölve", "{{userName}} added {{addedUserName}}": "{{userName}} added {{addedUserName}}", "{{userName}} removed {{removedUserName}}": "{{userName}} removed {{removedUserName}}", "{{userName}} moved from trash": "{{userName}} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "{{userName}} published": "{{userName}} publikálta", "{{userName}} unpublished": "{{userName}} v<PERSON><PERSON><PERSON><PERSON>", "{{userName}} moved": "{{userName}} <PERSON><PERSON><PERSON><PERSON><PERSON>", "Export started": "<PERSON>z export<PERSON><PERSON> el<PERSON>", "Your file will be available in {{ location }} soon": "Your file will be available in {{ location }} soon", "View": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "A ZIP file containing the images, and documents in the Markdown format.": "<PERSON><PERSON> <PERSON>, melyben a képek és Markdown formátumú dokumenumok találhatóak.", "A ZIP file containing the images, and documents as HTML files.": "<PERSON><PERSON> <PERSON>, melyben a képek és HTML formátumú dokumenumok találhatóak.", "Structured data that can be used to transfer data to another compatible {{ appName }} instance.": "<PERSON><PERSON><PERSON><PERSON><PERSON> adat, mely adatátvitelre has<PERSON>nálható egy másik, kompatibilis {{ appName }} példányba.", "Export": "Exportálás", "Exporting the collection <em>{{collectionName}}</em> may take some time.": "A(z) <em>{{collectionName}}</em> gyűjtemény exportálása egy kis időbe telik.", "You will receive an email when it's complete.": "Egy e-mail-t fog kapni, mi<PERSON> el<PERSON>.", "Include attachments": "Csatolmányokkal együtt", "Including uploaded images and files in the exported data": "A feltöltött képek és fájlok is exportálva lesznek", "{{count}} more user": "{{count}} more user", "{{count}} more user_plural": "{{count}} more users", "Filter": "Szűrő", "No results": "<PERSON><PERSON><PERSON>", "{{authorName}} created <3></3>": "{{authorName}} hozta lére <3></3>", "{{authorName}} opened <3></3>": "{{authorName}} megnyitotta <3></3>", "Search emoji": "Emojik keresése", "Search icons": "Ikonok keresése", "Choose default skin tone": "Alapértelmezett bő<PERSON><PERSON>ín kiválasztása", "Show menu": "<PERSON><PERSON>", "Icon Picker": "Ikonválasztó", "Icons": "Ikonok", "Emojis": "Emojik", "Remove": "Eltávolítás", "All": "Összes", "Frequently Used": "<PERSON><PERSON><PERSON><PERSON>", "Search Results": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Smileys & People": "Hangulatjelek és emberek", "Animals & Nature": "Állatok és természet", "Food & Drink": "Étel, ital", "Activity": "Tevékenység", "Travel & Places": "Utazás és he<PERSON>ek", "Objects": "<PERSON><PERSON><PERSON><PERSON>", "Symbols": "Szimbólumok", "Flags": "Zászlók", "Select a color": "Válasszon egy <PERSON>", "Loading": "Betöltés", "Permission": "Jogosultságok", "View only": "Csak megtekintés", "Can edit": "Szerkesztheti", "No access": "<PERSON><PERSON><PERSON>", "Default access": "Alapértelmezett hozz<PERSON>", "Change Language": "Nyelv megváltoztatása", "Dismiss": "<PERSON><PERSON><PERSON><PERSON>", "You’re offline.": "<PERSON>z Ön <PERSON> offline.", "Sorry, an error occurred.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, valami hiba <PERSON>.", "Click to retry": "Kattintson az újrapróbáláshoz", "Back": "<PERSON><PERSON><PERSON>", "Unknown": "Ismeretlen", "Mark all as read": "Összes megjelölése o<PERSON>ént", "You're all caught up": "Minden hozzászó<PERSON><PERSON><PERSON>", "Icon": "Icon", "My App": "My App", "Tagline": "Tagline", "A short description": "A short description", "Callback URLs": "Callback URLs", "Published": "Közzétéve", "Allow this app to be installed by other workspaces": "Allow this app to be installed by other workspaces", "{{ username }} reacted with {{ emoji }}": "{{ username }} reacted with {{ emoji }}", "{{ firstUsername }} and {{ secondUsername }} reacted with {{ emoji }}": "{{ firstUsername }} and {{ secondUsername }} reacted with {{ emoji }}", "{{ firstUsername }} and {{ count }} others reacted with {{ emoji }}": "{{ firstUsername }} and {{ count }} other reacted with {{ emoji }}", "{{ firstUsername }} and {{ count }} others reacted with {{ emoji }}_plural": "{{ firstUsername }} and {{ count }} others reacted with {{ emoji }}", "Add reaction": "Reagá<PERSON>ás hozz<PERSON>adása", "Reaction picker": "Reagálásválasztó", "Could not load reactions": "<PERSON><PERSON> betölteni a reagálásokat", "Reaction": "Reagálás", "Results": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "No results for {{query}}": "<PERSON><PERSON><PERSON> ta<PERSON>: {{query}}", "Manage": "Kezelés", "All members": "Összes tag", "Everyone in the workspace": "Everyone in the workspace", "{{ count }} member": "{{ count }} tag", "{{ count }} member_plural": "{{ count }} tag", "Invite": "Meghívás", "{{ userName }} was added to the collection": "{{ userName }} hozzáadva a gyűjteményhez", "{{ count }} people added to the collection": "{{ count }} s<PERSON><PERSON><PERSON> a gyűjteményhez", "{{ count }} people added to the collection_plural": "{{ count }} s<PERSON><PERSON><PERSON> a gyűjteményhez", "{{ count }} people and {{ count2 }} groups added to the collection": "{{ count }} s<PERSON><PERSON><PERSON> {{ count2 }} csoport ho<PERSON>adva a gyűjteményhez", "{{ count }} people and {{ count2 }} groups added to the collection_plural": "{{ count }} s<PERSON><PERSON><PERSON> {{ count2 }} csoport ho<PERSON>adva a gyűjteményhez", "Add": "Hozzáadás", "Add or invite": "Add or invite", "Viewer": "Megtekintő", "Editor": "Szerkesztő", "Suggestions for invitation": "Suggestions for invitation", "No matches": "<PERSON><PERSON><PERSON>", "Can view": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Everyone in the collection": "Everyone in the collection", "You have full access": "You have full access", "Created the document": "Dokumentum létrehozása", "Other people": "Other people", "Other workspace members may have access": "Other workspace members may have access", "This document may be shared with more workspace members through a parent document or collection you do not have access to": "This document may be shared with more workspace members through a parent document or collection you do not have access to", "Access inherited from collection": "Access inherited from collection", "{{ userName }} was removed from the document": "{{ userName }} eltávolítva a dokumentumból", "Could not remove user": "<PERSON>em lehet a felhasználót eltávolítani", "Permissions for {{ userName }} updated": "{{ userName }} jogosultságai frissítve", "Could not update user": "<PERSON>em lehet a felhasználót frissíteni", "Has access through <2>parent</2>": "Has access through <2>parent</2>", "Suspended": "Felfüggesztve", "Invited": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Active <1></1> ago": "Utoljára aktív: <1></1>", "Never signed in": "<PERSON><PERSON><PERSON> lé<PERSON> be", "Leave": "<PERSON><PERSON><PERSON>", "Only lowercase letters, digits and dashes allowed": "Csak kisbetűk, számok és kötőjelek használhatók", "Sorry, this link has already been used": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ez a hivatkozás már has<PERSON> van", "Public link copied to clipboard": "Public link copied to clipboard", "Web": "Web", "Anyone with the link can access because the parent document, <2>{{documentTitle}}</2>, is shared": "Anyone with the link can access because the parent document, <2>{{documentTitle}}</2>, is shared", "Allow anyone with the link to access": "Allow anyone with the link to access", "Publish to internet": "Publikálás interneten", "Search engine indexing": "Keresőmotor indexelése", "Disable this setting to discourage search engines from indexing the page": "Disable this setting to discourage search engines from indexing the page", "Show last modified": "Show last modified", "Display the last modified timestamp on the shared page": "Display the last modified timestamp on the shared page", "Nested documents are not shared on the web. Toggle sharing to enable access, this will be the default behavior in the future": "A beágyazott dokumentumok nem kerülnek megosztásra a weben. Kapcsolja be a megosztást a hozzáférés engedélyezéséhez, ez lesz az alapértelmezett viselkedés a jövőben.", "{{ userName }} was added to the document": "{{ userName }} eltávolítva a dokumentumból", "{{ count }} people added to the document": "{{ count }} eltávolítva a dokumentumból", "{{ count }} people added to the document_plural": "{{ count }} eltávolítva a dokumentumból", "{{ count }} groups added to the document": "{{ count }} eltávolítva a dokumentumból", "{{ count }} groups added to the document_plural": "{{ count }} eltávolítva a dokumentumból", "Logo": "Logó", "Archived collections": "Keresés a gyűjteményben", "New doc": "Új doku", "Empty": "Üres", "Collapse": "Összezárás", "Expand": "Kinyitás", "Document not supported – try Markdown, Plain text, HTML, or Word": "Ez a dokumentum nem tá<PERSON>ott – <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Markdown, egyszerű szöveg, HTML vagy Word formátumot", "Go back": "Ugrás v<PERSON>", "Go forward": "Előrelépés", "Could not load shared documents": "<PERSON>em lehet menteni a dokumentumot", "Shared with me": "Velem megosztva", "Show more": "<PERSON><PERSON><PERSON>", "Could not load starred documents": "<PERSON>em lehet menteni a dokumentumot", "Starred": "Csillagozott", "Up to date": "Naprak<PERSON><PERSON>", "{{ releasesBehind }} versions behind": "{{ <PERSON><PERSON><PERSON><PERSON> }} ve<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "{{ releasesBehind }} versions behind_plural": "{{ <PERSON><PERSON><PERSON><PERSON> }} ve<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Change permissions?": "Engedélyek módosítása?", "{{ documentName }} cannot be moved within {{ parentDocumentName }}": "{{ documentName }} cannot be moved within {{ parentDocumentName }}", "You can't reorder documents in an alphabetically sorted collection": "Nem tudja egy ABC szerint rendezett gyűjtemény elemeinek sorrendjét megváltoztatni", "The {{ documentName }} cannot be moved here": "The {{ documentName }} cannot be moved here", "Return to App": "Vissza az alkalmazáshoz", "Installation": "Telepítés", "Unstar document": "Dokumentum csillagozásának törlése", "Star document": "Dokumentum csillagozása", "Template created, go ahead and customize it": "A sablon elkészült, fogjon bele a testreszabásba", "Creating a template from <em>{{titleWithDefault}}</em> is a non-destructive action – we'll make a copy of the document and turn it into a template that can be used as a starting point for new documents.": "Sablon létrehozása a(z) <em>{{titleWithDefault}}</em> dokumentumból \"roncsolásmentes\" művelet – egy másolatot készítünk a dokumentumból, me<PERSON><PERSON><PERSON><PERSON> sablon lesz, ami új dokumentumok kiinduló pontjának használható.", "Enable other members to use the template immediately": "Enable other members to use the template immediately", "Location": "<PERSON><PERSON>", "Admins can manage the workspace and access billing.": "Admins can manage the workspace and access billing.", "Editors can create, edit, and comment on documents.": "Editors can create, edit, and comment on documents.", "Viewers can only view and comment on documents.": "Viewers can only view and comment on documents.", "Are you sure you want to make {{ userName }} a {{ role }}?": "<PERSON><PERSON><PERSON><PERSON> {{ role }} szerep<PERSON> szeretn<PERSON> {{ userName }} felhasználót?", "I understand, delete": "Megértettem, töröljük", "Are you sure you want to permanently delete {{ userName }}? This operation is unrecoverable, consider suspending the user instead.": "<PERSON><PERSON><PERSON>, hogy {{ userName }} véglegesen törölve legyen? Ez a művelet nem vonható vissza, fontolja meg a felfüggesztést helyette.", "Are you sure you want to suspend {{ userName }}? Suspended users will be prevented from logging in.": "Bizthogy, hogy {{ userName }} fel legyen függesztve? A felfüggesztett felhasználók belépni sem tudnak.", "New name": "<PERSON>j név", "Name can't be empty": "A név nem lehet üres", "Check your email to verify the new address.": "Check your email to verify the new address.", "The email will be changed once verified.": "The email will be changed once verified.", "You will receive an email to verify your new address. It must be unique in the workspace.": "You will receive an email to verify your new address. It must be unique in the workspace.", "A confirmation email will be sent to the new address before it is changed.": "A confirmation email will be sent to the new address before it is changed.", "New email": "Új e-mail cím", "Email can't be empty": "Az e-mail mező nem lehet üres", "Your import completed": "Your import completed", "Previous match": "Előző találat", "Next match": "Következő találat", "Find and replace": "Keresés és csere", "Find": "Keresés", "Match case": "Kis- és nagybetűk megkülönböztetése", "Enable regex": "Regex engedélyezése", "Replace options": "Csere opciók", "Replacement": "Csere", "Replace": "<PERSON><PERSON><PERSON><PERSON>", "Replace all": "Összeset c<PERSON>éli", "Profile picture": "Profilkép", "Create a new doc": "Új doku létrehozása", "{{ userName }} won't be notified, as they do not have access to this document": "{{ userName }} won't be notified, as they do not have access to this document", "Keep as link": "Keep as link", "Mention": "Mention", "Embed": "Beágyazott", "Add column after": "Oszlop ho<PERSON>áadása utána", "Add column before": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> el<PERSON>", "Add row after": "Add row after", "Add row before": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> el<PERSON>", "Align center": "Középre igazítás", "Align left": "Balra igazítás", "Align right": "Jobbra igazítás", "Default width": "Alapértelmezett szélesség", "Full width": "<PERSON><PERSON><PERSON>", "Bulleted list": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lista", "Todo list": "Feladatlista", "Code block": "Kódblokk", "Copied to clipboard": "Vágólapra másolva", "Code": "<PERSON><PERSON><PERSON>", "Comment": "Hozzászólás", "Create link": "Hivatkozás létrehozása", "Sorry, an error occurred creating the link": "<PERSON><PERSON><PERSON><PERSON><PERSON>, valami hiba merült fel a hivatkozás létrehozása közben", "Create a new child doc": "Új doku létrehozása", "Delete table": "Táblázat törlése", "Delete file": "Fájl törlése", "Width x Height": "Width x Height", "Download file": "Fájl letöltése", "Replace file": "<PERSON><PERSON><PERSON><PERSON>", "Delete image": "<PERSON><PERSON><PERSON>", "Download image": "<PERSON><PERSON><PERSON>", "Replace image": "<PERSON><PERSON><PERSON>", "Italic": "<PERSON><PERSON><PERSON>", "Sorry, that link won’t work for this embed type": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ez a hivatkozás nem működik ennél a beágyazott típusnál", "File attachment": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Enter a link": "Enter a link", "Big heading": "<PERSON><PERSON>", "Medium heading": "Közepes címsor", "Small heading": "<PERSON><PERSON>", "Extra small heading": "Nagyon kicsi cí<PERSON>or", "Heading": "<PERSON><PERSON><PERSON><PERSON>", "Divider": "Elválasztó", "Image": "<PERSON><PERSON><PERSON>", "Sorry, an error occurred uploading the file": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, valami hiba tört<PERSON>t a fájl feltöltése közben", "Write a caption": "<PERSON><PERSON><PERSON> e<PERSON>", "Info": "Infó", "Info notice": "Információs <PERSON>", "Link": "Hivatkozás", "Highlight": "<PERSON><PERSON><PERSON><PERSON>", "Type '/' to insert": "<PERSON><PERSON><PERSON><PERSON><PERSON> be egy „/” karaktert a beszúráshoz", "Keep typing to filter": "Gépeljen tovább a szűréshez", "Open link": "Hivatkozás me<PERSON>", "Go to link": "Ugrás a hivatkozásra", "Sorry, that type of link is not supported": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ez a hivatkoz<PERSON> típus nem támogatott", "Ordered list": "<PERSON><PERSON><PERSON> lista", "Page break": "Oldaltö<PERSON><PERSON>", "Paste a link": "<PERSON><PERSON><PERSON><PERSON><PERSON> be egy <PERSON>", "Paste a {{service}} link…": "<PERSON><PERSON><PERSON><PERSON><PERSON> be egy {{service}} hivatkozást…", "Placeholder": "Helykitöltő", "Quote": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Remove link": "Hivatkozás eltávolítása", "Search or paste a link": "<PERSON><PERSON><PERSON> vagy s<PERSON> be egy hi<PERSON>", "Strikethrough": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Bold": "F<PERSON>lkövér", "Subheading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Sort ascending": "Rendezés növekvő sorrendben", "Sort descending": "Rendezés csökkenő sorrendben", "Table": "T<PERSON>b<PERSON><PERSON><PERSON><PERSON>", "Export as CSV": "CSV exportálás", "Toggle header": "<PERSON>j<PERSON>c be- vag<PERSON> k<PERSON>", "Math inline (LaTeX)": "<PERSON>ek k<PERSON>t a sorban (LaTeX)", "Math block (LaTeX)": "Matematikablokk (LaTeX)", "Merge cells": "Merge cells", "Split cell": "Split cell", "Tip": "<PERSON><PERSON><PERSON>", "Tip notice": "<PERSON><PERSON><PERSON>", "Warning": "Figyelmeztetés", "Warning notice": "Figyelmeztető értesítés", "Success": "Siker", "Success notice": "<PERSON><PERSON>", "Current date": "<PERSON><PERSON><PERSON><PERSON>", "Current time": "<PERSON><PERSON><PERSON><PERSON>", "Current date and time": "Jelenlegi dátum é<PERSON>", "Indent": "Behúzás növelése", "Outdent": "Behúzás csökkentése", "Video": "<PERSON><PERSON><PERSON>", "None": "<PERSON><PERSON><PERSON>", "Could not import file": "Fájl importálása nem lehetséges", "Unsubscribed from document": "Leiratkozott a dokumentumról", "Unsubscribed from collection": "Leiratkozott a dokumentumról", "Account": "<PERSON>ók", "API & Apps": "API és alkalmazások", "Details": "<PERSON><PERSON><PERSON><PERSON>", "Security": "Biztonság", "Features": "Funkciók", "Members": "Tagok", "Groups": "Csoportok", "API Keys": "API-kulcsok", "Applications": "Applications", "Shared Links": "Megosztott hivat<PERSON>zások", "Import": "Importálás", "Install": "Install", "Integrations": "Integrációk", "Revoke token": "<PERSON><PERSON> v<PERSON>", "Revoke": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Show path to document": "Dokumentum elérési útjának megjelenítése", "Path to document": "Dokumentum elérési útja", "Group member options": "Csoport tagság op<PERSON>ók", "Export collection": "Gyűjtemény exportálása", "Rename": "Átnevezés", "Sort in sidebar": "Rendezés az oldalsávban", "A-Z sort": "A-Z sort", "Z-A sort": "Z-A sort", "Manual sort": "<PERSON><PERSON><PERSON><PERSON> sorrend", "Comment options": "Hozzászólás beállítások", "Show document menu": "Show document menu", "{{ documentName }} restored": "{{ documentName}} visszaállította", "Document options": "Dokumentum beállítások", "Choose a collection": "Gyűjtemény kiválasztása", "Subscription inherited from collection": "Subscription inherited from collection", "Apply template": "Apply template", "Enable embeds": "Beágyazások engedélyezése", "Export options": "Exportálási beállí<PERSON>ások", "Group members": "Csoport tagjai", "Edit group": "Csoport szerkesztése", "Delete group": "Csoport törlése", "Group options": "Csoport beállítások", "Cancel": "<PERSON><PERSON><PERSON><PERSON>", "Import menu options": "Import menu options", "Member options": "Tag beállítások", "New document in <em>{{ collectionName }}</em>": "Új dokumentum a(z) <em>{{ collectionName }}</em> gyűjteményben.", "New child document": "Új gyermek dokumentum", "Save in workspace": "Mentés a munkaterületen", "Notification settings": "Értesítési be<PERSON>", "Revoke {{ appName }}": "Revoke {{ appName }}", "Revoking": "Revoking", "Are you sure you want to revoke access?": "Are you sure you want to revoke access?", "Delete app": "Delete app", "Revision options": "<PERSON><PERSON><PERSON><PERSON>", "Share link revoked": "Hivatkozás megosztás v<PERSON>zavonva", "Share link copied": "Megosztási hivatkozás másolva", "Share options": "Megosztási beállítások", "Go to document": "Tovább a dokumentumhoz", "Revoke link": "Hivatkozás visszavonása", "Contents": "Tartalom", "Headings you add to the document will appear here": "A dokumentumhoz hozzáadott címsorok itt jelennek meg", "Table of contents": "Tartalomjegyzék", "Change name": "Név módosítása", "Change email": "Email-cím megv<PERSON>oztat<PERSON>a", "Suspend user": "Felfüggesztett felhasználó", "An error occurred while sending the invite": "Valami hiba történt a meghívó küldése közben", "User options": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Change role": "Szerep megváltoztatása", "Resend invite": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Revoke invite": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>", "Activate user": "Felhasználó aktiválása", "template": "sablon", "document": "dokumentum", "published": "publikálva", "edited": "módosítva", "created the collection": "létrehozta a gyűjteményt", "mentioned you in": "megemlített téged itt:", "left a comment on": "hozzászólt ehhez:", "resolved a comment on": "resolved a comment on", "shared": "megosztva", "invited you to": "invited you to", "Choose a date": "<PERSON><PERSON><PERSON><PERSON>", "API key created. Please copy the value now as it will not be shown again.": "API-kulcs létrehozva. Most másolja ki az értéket, mivel ez többé nem jelenik meg.", "Scopes": "Hatáskörök", "Space-separated scopes restrict the access of this API key to specific parts of the API. Leave blank for full access": "Space-separated scopes restrict the access of this API key to specific parts of the API. Leave blank for full access", "Expiration": "Expiration", "Never expires": "Never expires", "7 days": "7 nap", "30 days": "30 nap", "60 days": "60 nap", "90 days": "90 nap", "Custom": "<PERSON><PERSON><PERSON><PERSON>", "No expiration": "<PERSON><PERSON><PERSON>", "The document archive is empty at the moment.": "A dokumentum archívum jelenleg üres.", "Collection menu": "Gyűj<PERSON><PERSON><PERSON>", "Drop documents to import": "Engedje el itt a dokumentumot az importáláshoz", "<em>{{ collectionName }}</em> doesn’t contain any\n                    documents yet.": "<em>{{ collectionName }}</em> nem tartalmaz egyetlen\n                    dokumentumot sem.", "{{ usersCount }} users and {{ groupsCount }} groups with access": "{{ usersCount }} felhasználó és {{ groupsCount }} csoport rendelkezik hozzáféréssel", "{{ usersCount }} users and {{ groupsCount }} groups with access_plural": "{{ usersCount }} felhasz<PERSON><PERSON><PERSON> és {{ groupsCount }} rendelkezik hozzáféréssel", "{{ usersCount }} users and a group have access": "{{ usersCount }} felhasználó és csoport rendelkezik hozzáféréssel", "{{ usersCount }} users and a group have access_plural": "{{ usersCount }} felhasználó és csoport rendelkezik hozzáféréssel", "{{ usersCount }} users with access": "{{ usersCount }} felhasználó rendelkezik hozzáféréssel", "{{ usersCount }} users with access_plural": "{{ usersCount }} felhasználó rendelkezik hozzáféréssel", "{{ groupsCount }} groups with access": "{{ groupsCount }} csoport rendelkezik hozzáféréssel", "{{ groupsCount }} groups with access_plural": "{{ groupsCount }} csoport rendelkezik hozzáféréssel", "Archived by {{userName}}": "Archiválta: {{userName}}", "Sorry, an error occurred saving the collection": "<PERSON><PERSON><PERSON><PERSON><PERSON>, valami hiba merült fel a gyűjtemény mentése közben", "Add a description": "<PERSON>ja meg a leí<PERSON>", "Share": "Megosztás", "Overview": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Recently updated": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>", "Recently published": "Nemrég publik<PERSON>l<PERSON>", "Least recently updated": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "A–Z": "A–Z", "Signing in": "Bejelentkezés", "You can safely close this window once the Outline desktop app has opened": "You can safely close this window once the Outline desktop app has opened", "Error creating comment": "Error creating comment", "Add a comment": "Megjegyzés hozzáadása", "Add a reply": "<PERSON><PERSON><PERSON>z <PERSON>", "Reply": "<PERSON><PERSON><PERSON><PERSON>", "Post": "Hozzászólás", "Upload image": "<PERSON><PERSON><PERSON>", "No resolved comments": "No resolved comments", "No comments yet": "Még nincsenek hozzászólások", "New comments": "<PERSON>j megjegyzések", "Most recent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Order in doc": "Order in doc", "Resolved": "<PERSON><PERSON><PERSON>", "Sort comments": "Sort comments", "Show {{ count }} reply": "Show {{ count }} reply", "Show {{ count }} reply_plural": "Show {{ count }} replies", "Error updating comment": "Error updating comment", "Document is too large": "A dokumentum túl nagy", "This document has reached the maximum size and can no longer be edited": "Ez a dokumentum elérte a maximális méretet és nem szerkeszthető tovább", "Authentication failed": "<PERSON><PERSON><PERSON><PERSON> hitelesíté<PERSON>", "Please try logging out and back in again": "<PERSON><PERSON><PERSON> pró<PERSON>á<PERSON>jon meg ki- majd ú<PERSON> be<PERSON>", "Authorization failed": "A hitelesítés si<PERSON>en", "You may have lost access to this document, try reloading": "<PERSON><PERSON><PERSON>, hogy nincs hozzáférése a dokumentumhoz, próbá<PERSON>ja meg újra betölteni", "Too many users connected to document": "Túl sok felhasználó kapcsolódik a dokumentumhoz", "Your edits will sync once other users leave the document": "A módosításai szinkronizálódnak, amikor a többi felhasználó elhagyja a dokumentumot", "Server connection lost": "A szerverrel a kapcsolat megszakadt", "Edits you make will sync once you’re online": "<PERSON>z elvégzett módosítások szinkronizálódnak, amint online lesz", "Document restored": "Dokumentum visszaállítva", "Images are still uploading.\nAre you sure you want to discard them?": "Images are still uploading.\nAre you sure you want to discard them?", "{{ count }} comment": "{{ count }} tag", "{{ count }} comment_plural": "{{ count }} tag", "Viewed by": "Viewed by", "only you": "only you", "person": "<PERSON><PERSON><PERSON><PERSON>", "people": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Last updated": "<PERSON><PERSON><PERSON><PERSON> f<PERSON>", "Type '/' to insert, or start writing…": "<PERSON><PERSON><PERSON><PERSON><PERSON> be egy „/” karaktert a beszúr<PERSON>z, vagy kezdjen el írni…", "Hide contents": "Tartalomjegyzék elrejtése", "Show contents": "Tartalomjegyzék megjelenítése", "available when headings are added": "available when headings are added", "Edit {{noun}}": "Edit {{noun}}", "Switch to dark": "Switch to dark", "Switch to light": "Switch to light", "Archived": "Archiválva", "Save draft": "Piszkozat mentése", "Done editing": "Változtatások mentése", "Restore version": "<PERSON><PERSON><PERSON><PERSON>í<PERSON>", "No history yet": "M<PERSON>g ninc<PERSON>ek előzmények", "Source": "<PERSON><PERSON><PERSON>", "Imported from {{ source }}": "Imported from {{ source }}", "Stats": "Statisztikák", "{{ count }} minute read": "{{ count }} perc o<PERSON><PERSON>", "{{ count }} minute read_plural": "{{ count }} perc o<PERSON><PERSON>", "{{ count }} words": "{{ count }} szó", "{{ count }} words_plural": "{{ count }} szó", "{{ count }} characters": "{{ count }} karakter", "{{ count }} characters_plural": "{{ count }} karakter", "{{ number }} emoji": "{{ number }} emoji", "No text selected": "<PERSON><PERSON><PERSON> szöveg kijelölve", "{{ count }} words selected": "{{ count }} s<PERSON><PERSON>", "{{ count }} words selected_plural": "{{ count }} s<PERSON><PERSON>", "{{ count }} characters selected": "{{ count }} karakter kiválasztva", "{{ count }} characters selected_plural": "{{ count }} karakter kiválasztva", "Contributors": "Közreműködők", "Created": "Létrehozva", "Creator": "Létrehozó", "Last edited": "Utoljára szerkesztve", "Previously edited": "Korábban szerkesztve", "No one else has viewed yet": "Senki más nem látta", "Viewed {{ count }} times by {{ teamMembers }} people": "Megtekintette {{ team<PERSON>embers }} személy {{ count }} alkalommal", "Viewed {{ count }} times by {{ teamMembers }} people_plural": "Megtekintette {{ team<PERSON>embers }} személy {{ count }} alkalommal", "Viewer insights are disabled.": "Viewer insights are disabled.", "Sorry, the last change could not be persisted – please reload the page": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, az utolsó változás nem menthető – kérem töltse újra az oldalt", "{{ count }} days": "{{ count }} szó", "{{ count }} days_plural": "{{ count }} szó", "This template will be permanently deleted in <2></2> unless restored.": "Ez a sablon véglegesen törölve lesz <2></2>, hacsak nem álí<PERSON>ja v<PERSON>.", "This document will be permanently deleted in <2></2> unless restored.": "Ez a dokumentum véglegesen törölve lesz <2></2>, hacsak nem álítja v<PERSON>.", "Highlight some text and use the <1></1> control to add placeholders that can be filled out when creating new documents": "Jelöljön ki valamilyen s<PERSON>öveget, és használja a <1></1> vezérlőt olyan helykitöltők hozzáadásához, amelyek kitöltésre kerülhetnek új dokumentumok létrehozásakor", "You’re editing a template": "<PERSON>n egy sablont szerkeszt", "Deleted by {{userName}}": "T<PERSON>r<PERSON><PERSON>e: {{userName}}", "Observing {{ userName }}": "Vizsgálja: {{ userName }}", "Backlinks": "Vissza-hivatkozások", "Close": "<PERSON><PERSON><PERSON><PERSON>", "This document is large which may affect performance": "This document is large which may affect performance", "{{ teamName }} is using {{ appName }} to share documents, please login to continue.": "{{ teamName }} is using {{ appName }} to share documents, please login to continue.", "Are you sure you want to delete the <em>{{ documentTitle }}</em> template?": "Are you sure you want to delete the <em>{{ documentTitle }}</em> template?", "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history</em>.": "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history</em>.", "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history and <em>{{ any }} nested document</em>.": "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history and <em>{{ any }} nested document</em>.", "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history and <em>{{ any }} nested document</em>._plural": "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history and <em>{{ any }} nested documents</em>.", "If you’d like the option of referencing or restoring the {{noun}} in the future, consider archiving it instead.": "If you’d like the option of referencing or restoring the {{noun}} in the future, consider archiving it instead.", "Select a location to move": "Select a location to move", "Document moved": "Document moved", "Couldn’t move the document, try again?": "Couldn’t move the document, try again?", "Move to <em>{{ location }}</em>": "Move to <em>{{ location }}</em>", "Couldn’t create the document, try again?": "Couldn’t create the document, try again?", "Document permanently deleted": "Document permanently deleted", "Are you sure you want to permanently delete the <em>{{ documentTitle }}</em> document? This action is immediate and cannot be undone.": "Are you sure you want to permanently delete the <em>{{ documentTitle }}</em> document? This action is immediate and cannot be undone.", "Select a location to publish": "Select a location to publish", "Document published": "Dokumentum közzétéve", "Couldn’t publish the document, try again?": "Couldn’t publish the document, try again?", "Publish in <em>{{ location }}</em>": "Publish in <em>{{ location }}</em>", "Search documents": "Search documents", "No documents found for your filters.": "No documents found for your filters.", "You’ve not got any drafts at the moment.": "You’ve not got any drafts at the moment.", "Payment Required": "Payment Required", "No access to this doc": "No access to this doc", "It doesn’t look like you have permission to access this document.": "It doesn’t look like you have permission to access this document.", "Please request access from the document owner.": "Please request access from the document owner.", "Not found": "Not found", "The page you’re looking for cannot be found. It might have been deleted or the link is incorrect.": "The page you’re looking for cannot be found. It might have been deleted or the link is incorrect.", "Offline": "Offline", "We were unable to load the document while offline.": "We were unable to load the document while offline.", "Your account has been suspended": "A fiókja felfüggesztésre került", "Warning Sign": "Warning Sign", "A workspace admin (<em>{{ suspendedContactEmail }}</em>) has suspended your account. To re-activate your account, please reach out to them directly.": "A workspace admin (<em>{{ suspendedContactEmail }}</em>) has suspended your account. To re-activate your account, please reach out to them directly.", "Created by me": "Created by me", "Weird, this shouldn’t ever be empty": "Weird, this shouldn’t ever be empty", "You haven’t created any documents yet": "You haven’t created any documents yet", "Documents you’ve recently viewed will be here for easy access": "Documents you’ve recently viewed will be here for easy access", "We sent out your invites!": "We sent out your invites!", "Those email addresses are already invited": "Those email addresses are already invited", "Sorry, you can only send {{MAX_INVITES}} invites at a time": "Sorry, you can only send {{MAX_INVITES}} invites at a time", "Invited {{roleName}} will receive access to": "Invited {{roleName}} will receive access to", "{{collectionCount}} collections": "{{collectionCount}} collections", "Admin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Can manage all workspace settings": "Can manage all workspace settings", "Can create, edit, and delete documents": "Can create, edit, and delete documents", "Can view and comment": "Can view and comment", "Invite people to join your workspace. They can sign in with {{signinMethods}} or use their email address.": "Invite people to join your workspace. They can sign in with {{signinMethods}} or use their email address.", "Invite members to join your workspace. They will need to sign in with {{signinMethods}}.": "Invite members to join your workspace. They will need to sign in with {{signinMethods}}.", "As an admin you can also <2>enable email sign-in</2>.": "As an admin you can also <2>enable email sign-in</2>.", "Invite as": "Invite as", "Role": "Szerep", "Email": "Email", "Add another": "Add another", "Inviting": "Inviting", "Send Invites": "Meghívók küldése", "Open command menu": "Parancsmenü <PERSON>", "Forward": "Továbbítás", "Edit current document": "Jelenlegi dokumentum szerkesztése", "Move current document": "Jelenlegi dokumentum áthelyezése", "Open document history": "Open document history", "Jump to search": "Jump to search", "Jump to home": "Jump to home", "Focus search input": "Focus search input", "Open this guide": "Open this guide", "Enter": "Bevitel", "Publish document and exit": "Publish document and exit", "Save document": "Dokumentum mentése", "Cancel editing": "Szerkesztés megszakítása", "Collaboration": "Együttműködés", "Formatting": "Formázás", "Paragraph": "Bekezdés", "Large header": "<PERSON><PERSON>", "Medium header": "Közepes címsor", "Small header": "<PERSON><PERSON>", "Underline": "Aláhúzás", "Undo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Redo": "Redo", "Move block up": "Move block up", "Move block down": "Move block down", "Lists": "Listák", "Toggle task list item": "Feladatlista-elem be- vagy k<PERSON>", "Tab": "Tab", "Indent list item": "<PERSON><PERSON><PERSON>", "Outdent list item": "<PERSON><PERSON><PERSON>", "Move list item up": "<PERSON><PERSON><PERSON> f<PERSON>", "Move list item down": "<PERSON><PERSON><PERSON>", "Tables": "Táblázatok", "Insert row": "<PERSON><PERSON> <PERSON>", "Next cell": "Következő cella", "Previous cell": "Előző cella", "Space": "Space", "Numbered list": "Számozott lista", "Blockquote": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Horizontal divider": "<PERSON><PERSON><PERSON><PERSON>", "LaTeX block": "LaTeX-blokk", "Inline code": "Beágyazott kód", "Inline LaTeX": "Beágyazott LaTeX", "Triggers": "Triggers", "Mention users and more": "Mention users and more", "Emoji": "<PERSON><PERSON><PERSON>", "Insert block": "Blokk beszúrása", "Sign In": "Bejelentkezés", "Continue with Email": "Continue with <PERSON>ail", "Continue with {{ authProviderName }}": "Continue with {{ authProviderName }}", "Back to home": "Vissza a főoldalra", "The workspace could not be found": "The workspace could not be found", "To continue, enter your workspace’s subdomain.": "To continue, enter your workspace’s subdomain.", "subdomain": "aldomain", "Continue": "Continue", "The domain associated with your email address has not been allowed for this workspace.": "The domain associated with your email address has not been allowed for this workspace.", "Unable to sign-in. Please navigate to your workspace's custom URL, then try to sign-in again.<1></1>If you were invited to a workspace, you will find a link to it in the invite email.": "Unable to sign-in. Please navigate to your workspace's custom URL, then try to sign-in again.<1></1>If you were invited to a workspace, you will find a link to it in the invite email.", "Sorry, a new account cannot be created with a personal Gmail address.<1></1>Please use a Google Workspaces account instead.": "Sorry, a new account cannot be created with a personal Gmail address.<1></1>Please use a Google Workspaces account instead.", "The workspace associated with your user is scheduled for deletion and cannot be accessed at this time.": "The workspace associated with your user is scheduled for deletion and cannot be accessed at this time.", "The workspace you authenticated with is not authorized on this installation. Try another?": "The workspace you authenticated with is not authorized on this installation. Try another?", "We could not read the user info supplied by your identity provider.": "We could not read the user info supplied by your identity provider.", "Your account uses email sign-in, please sign-in with email to continue.": "Your account uses email sign-in, please sign-in with email to continue.", "An email sign-in link was recently sent, please check your inbox or try again in a few minutes.": "An email sign-in link was recently sent, please check your inbox or try again in a few minutes.", "Authentication failed – we were unable to sign you in at this time. Please try again.": "Authentication failed – we were unable to sign you in at this time. Please try again.", "Authentication failed – you do not have permission to access this workspace.": "Authentication failed – you do not have permission to access this workspace.", "Sorry, it looks like that sign-in link is no longer valid, please try requesting another.": "Sorry, it looks like that sign-in link is no longer valid, please try requesting another.", "Your account has been suspended. To re-activate your account, please contact a workspace admin.": "Your account has been suspended. To re-activate your account, please contact a workspace admin.", "This workspace has been suspended. Please contact support to restore access.": "This workspace has been suspended. Please contact support to restore access.", "Authentication failed – this login method was disabled by a workspace admin.": "Authentication failed – this login method was disabled by a workspace admin.", "The workspace you are trying to join requires an invite before you can create an account.<1></1>Please request an invite from your workspace admin and try again.": "The workspace you are trying to join requires an invite before you can create an account.<1></1>Please request an invite from your workspace admin and try again.", "Sorry, an unknown error occurred.": "Sorry, an unknown error occurred.", "Choose a workspace": "Choose a workspace", "Choose an {{ appName }} workspace or login to continue connecting this app": "Choose an {{ appName }} workspace or login to continue connecting this app", "Create workspace": "Create workspace", "Setup your workspace by providing a name and details for admin login. You can change these later.": "Setup your workspace by providing a name and details for admin login. You can change these later.", "Workspace name": "Workspace name", "Admin name": "Admin name", "Admin email": "Admin email", "Login": "<PERSON><PERSON>", "Error": "Error", "Failed to load configuration.": "Failed to load configuration.", "Check the network requests and server logs for full details of the error.": "Check the network requests and server logs for full details of the error.", "Custom domain setup": "Custom domain setup", "Almost there": "Almost there", "Your custom domain is successfully pointing at Outline. To complete the setup process please contact support.": "Your custom domain is successfully pointing at Outline. To complete the setup process please contact support.", "Choose workspace": "Choose workspace", "This login method requires choosing your workspace to continue": "This login method requires choosing your workspace to continue", "Check your email": "Check your email", "A magic sign-in link has been sent to the email <em>{{ emailLinkSentTo }}</em> if an account exists.": "A magic sign-in link has been sent to the email <em>{{ emailLinkSentTo }}</em> if an account exists.", "Back to login": "Back to login", "Get started by choosing a sign-in method for your new workspace below…": "Get started by choosing a sign-in method for your new workspace below…", "Login to {{ authProviderName }}": "Login to {{ authProviderName }}", "You signed in with {{ authProviderName }} last time.": "You signed in with {{ authProviderName }} last time.", "Or": "Or", "Already have an account? Go to <1>login</1>.": "Already have an account? Go to <1>login</1>.", "An error occurred": "An error occurred", "The OAuth client could not be found, please check the provided client ID": "The OAuth client could not be found, please check the provided client ID", "The OAuth client could not be loaded, please check the redirect URI is valid": "The OAuth client could not be loaded, please check the redirect URI is valid", "Required OAuth parameters are missing": "Required OAuth parameters are missing", "Authorize": "Authorize", "{{ appName }} wants to access {{ teamName }}": "{{ appName }} wants to access {{ teamName }}", "By <em>{{ developerName }}</em>": "By <em>{{ developerName }}</em>", "{{ appName }} will be able to access your account and perform the following actions": "{{ appName }} will be able to access your account and perform the following actions", "read": "read", "write": "write", "read and write": "read and write", "API keys": "API-kulcsok", "attachments": "attachments", "collections": "collections", "comments": "comments", "documents": "documents", "events": "events", "groups": "groups", "integrations": "integrations", "notifications": "értesítések", "reactions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pins": "pins", "shares": "shares", "users": "users", "teams": "teams", "workspace": "workspace", "Read all data": "Read all data", "Write all data": "Write all data", "Any collection": "Any collection", "All time": "All time", "Past day": "Past day", "Past week": "Past week", "Past month": "Past month", "Past year": "Past year", "Any time": "Any time", "Remove document filter": "Remove document filter", "Any status": "Any status", "Remove search": "Remove search", "Any author": "Any author", "Search titles only": "Search titles only", "Something went wrong": "Something went wrong", "Please try again or contact support if the problem persists": "Please try again or contact support if the problem persists", "No documents found for your search filters.": "No documents found for your search filters.", "Create personal API keys to authenticate with the API and programatically control\n      your workspace's data. For more details see the <em>developer documentation</em>.": "Személyes API-kulcsok létrehozása az API-val való hitelesítéshez és a munkaterület adatainak programozott vezérléséhez. További részletekért nézze meg a <em>fejlesztői dokumentációt</em>.", "API keys have been disabled by an admin for your account": "Egy adminisztrátor letiltotta az API-kulcsokat az Ön fiókjánál", "Application access": "Application access", "Manage which third-party and internal applications have been granted access to your {{ appName }} account.": "Manage which third-party and internal applications have been granted access to your {{ appName }} account.", "API": "API", "API keys can be used to authenticate with the API and programatically control\n          your workspace's data. For more details see the <em>developer documentation</em>.": "Az API-kulcsok az API-val való hitelesítéshez és a munkaterület adatainak programozott vezérléséhez használhatók. További részletekért nézze meg a <em>fejlesztői dokumentációt</em>.", "Application published": "Application published", "Application updated": "Application updated", "Client secret rotated": "Client secret rotated", "Rotate secret": "Rotate secret", "Rotating the client secret will invalidate the current secret. Make sure to update any applications using these credentials.": "Rotating the client secret will invalidate the current secret. Make sure to update any applications using these credentials.", "Displayed to users when authorizing": "Displayed to users when authorizing", "Developer information shown to users when authorizing": "Developer information shown to users when authorizing", "Developer name": "Developer name", "Developer URL": "Developer URL", "Allow users from other workspaces to authorize this app": "Allow users from other workspaces to authorize this app", "Credentials": "Credentials", "OAuth client ID": "OAuth client ID", "The public identifier for this app": "The public identifier for this app", "OAuth client secret": "OAuth client secret", "Store this value securely, do not expose it publicly": "Store this value securely, do not expose it publicly", "Where users are redirected after authorizing this app": "Where users are redirected after authorizing this app", "Authorization URL": "Authorization URL", "Where users are redirected to authorize this app": "Where users are redirected to authorize this app", "Applications allow you to build internal or public integrations with Outline and provide secure access via OAuth. For more details see the <em>developer documentation</em>.": "Applications allow you to build internal or public integrations with Outline and provide secure access via OAuth. For more details see the <em>developer documentation</em>.", "by {{ name }}": "by {{ name }}", "Last used": "Last used", "No expiry": "No expiry", "Restricted scope": "Restricted scope", "API key copied to clipboard": "API-kulcs a vágólapra másolva", "Copied": "<PERSON>pied", "Are you sure you want to revoke the {{ tokenName }} token?": "Are you sure you want to revoke the {{ tokenName }} token?", "Disconnect integration": "Disconnect integration", "Connected": "Connected", "Disconnect": "Disconnect", "Disconnecting": "Disconnecting", "Allowed domains": "Allowed domains", "The domains which should be allowed to create new accounts using SSO. Changing this setting does not affect existing user accounts.": "The domains which should be allowed to create new accounts using SSO. Changing this setting does not affect existing user accounts.", "Remove domain": "Remove domain", "Add a domain": "Add a domain", "Save changes": "Változtatások mentése", "Please choose a single file to import": "Please choose a single file to import", "Your import is being processed, you can safely leave this page": "Your import is being processed, you can safely leave this page", "File not supported – please upload a valid ZIP file": "File not supported – please upload a valid ZIP file", "Set the default permission level for collections created from the import": "Set the default permission level for collections created from the import", "Uploading": "Uploading", "Start import": "Start import", "Processing": "Processing", "Expired": "<PERSON><PERSON><PERSON><PERSON>", "Completed": "Completed", "Failed": "Failed", "All collections": "All collections", "Import deleted": "Import deleted", "Export deleted": "Export deleted", "Are you sure you want to delete this import?": "Are you sure you want to delete this import?", "Deleting this import will also delete all collections and documents that were created from it. This cannot be undone.": "Deleting this import will also delete all collections and documents that were created from it. This cannot be undone.", "Check server logs for more details.": "Check server logs for more details.", "{{userName}} requested": "{{userName}} requested", "Groups are for organizing your team. They work best when centered around a function or a responsibility — Support or Engineering for example.": "Groups are for organizing your team. They work best when centered around a function or a responsibility — Support or Engineering for example.", "You’ll be able to add people to the group next.": "You’ll be able to add people to the group next.", "You can edit the name of this group at any time, however doing so too often might confuse your team mates.": "You can edit the name of this group at any time, however doing so too often might confuse your team mates.", "Are you sure about that? Deleting the <em>{{groupName}}</em> group will cause its members to lose access to collections and documents that it is associated with.": "Are you sure about that? Deleting the <em>{{groupName}}</em> group will cause its members to lose access to collections and documents that it is associated with.", "Add people to {{groupName}}": "Add people to {{groupName}}", "{{userName}} was removed from the group": "{{userName}} was removed from the group", "Add and remove members to the <em>{{groupName}}</em> group. Members of the group will have access to any collections this group has been added to.": "Add and remove members to the <em>{{groupName}}</em> group. Members of the group will have access to any collections this group has been added to.", "Add people": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Listing members of the <em>{{groupName}}</em> group.": "A(z) <em>{{groupName}}</em> csoport tagjainak felsorolása.", "This group has no members.": "This group has no members.", "{{userName}} was added to the group": "{{userName}} was added to the group", "Could not add user": "A felhasználó nem adható hozzá", "Add members below to give them access to the group. Need to add someone who’s not yet a member?": "Add members below to give them access to the group. Need to add someone who’s not yet a member?", "Invite them to {{teamName}}": "Invite them to {{teamName}}", "Ask an admin to invite them first": "Ask an admin to invite them first", "Search by name": "Keresés név szerint", "Search people": "Emberek keresése", "No people matching your search": "<PERSON><PERSON> s<PERSON><PERSON> sem felel meg a keresési szempontoknak", "No people left to add": "<PERSON><PERSON><PERSON> tö<PERSON> ho<PERSON><PERSON><PERSON><PERSON><PERSON>", "Date created": "Létrehozás <PERSON>", "Crop Image": "Crop Image", "Crop image": "Crop image", "How does this work?": "How does this work?", "You can import a zip file that was previously exported from the JSON option in another instance. In {{ appName }}, open <em>Export</em> in the Settings sidebar and click on <em>Export Data</em>.": "You can import a zip file that was previously exported from the JSON option in another instance. In {{ appName }}, open <em>Export</em> in the Settings sidebar and click on <em>Export Data</em>.", "Drag and drop the zip file from the JSON export option in {{appName}}, or click to upload": "Drag and drop the zip file from the JSON export option in {{appName}}, or click to upload", "Canceled": "Canceled", "Import canceled": "Import canceled", "Are you sure you want to cancel this import?": "Are you sure you want to cancel this import?", "Canceling": "Canceling", "Canceling this import will discard any progress made. This cannot be undone.": "Canceling this import will discard any progress made. This cannot be undone.", "{{ count }} document imported": "{{ count }} document imported", "{{ count }} document imported_plural": "{{ count }} documents imported", "You can import a zip file that was previously exported from an Outline installation – collections, documents, and images will be imported. In Outline, open <em>Export</em> in the Settings sidebar and click on <em>Export Data</em>.": "You can import a zip file that was previously exported from an Outline installation – collections, documents, and images will be imported. In Outline, open <em>Export</em> in the Settings sidebar and click on <em>Export Data</em>.", "Drag and drop the zip file from the Markdown export option in {{appName}}, or click to upload": "Drag and drop the zip file from the Markdown export option in {{appName}}, or click to upload", "Configure": "Configure", "Connect": "Connect", "Last active": "Utoljára a<PERSON>ív", "Guest": "Vendég", "Never used": "Never used", "Are you sure you want to delete the {{ appName }} application? This cannot be undone.": "Are you sure you want to delete the {{ appName }} application? This cannot be undone.", "Shared by": "Megosztotta: ", "Date shared": "Date shared", "Last accessed": "Last accessed", "Domain": "Domain", "Views": "Megtekintések", "All roles": "Összes szerep", "Admins": "Adminisztrátorok", "Editors": "Szerkesztők", "All status": "<PERSON><PERSON><PERSON>llapot", "Active": "Aktív", "Left": "Left", "Right": "Right", "Settings saved": "Beállítások elmentve", "Logo updated": "Logo updated", "Unable to upload new logo": "<PERSON><PERSON> le<PERSON>", "Delete workspace": "Delete workspace", "These settings affect the way that your workspace appears to everyone on the team.": "These settings affect the way that your workspace appears to everyone on the team.", "Display": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "The logo is displayed at the top left of the application.": "The logo is displayed at the top left of the application.", "The workspace name, usually the same as your company name.": "The workspace name, usually the same as your company name.", "Description": "Description", "A short description of your workspace.": "A short description of your workspace.", "Theme": "<PERSON><PERSON><PERSON>", "Customize the interface look and feel.": "Customize the interface look and feel.", "Reset theme": "Reset theme", "Accent color": "Accent color", "Accent text color": "Accent text color", "Public branding": "Public branding", "Show your workspace logo, description, and branding on publicly shared pages.": "Show your workspace logo, description, and branding on publicly shared pages.", "Table of contents position": "Table of contents position", "The side to display the table of contents in relation to the main content.": "The side to display the table of contents in relation to the main content.", "Behavior": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Subdomain": "Subdomain", "Your workspace will be accessible at": "Your workspace will be accessible at", "Choose a subdomain to enable a login page just for your team.": "Choose a subdomain to enable a login page just for your team.", "This is the screen that workspace members will first see when they sign in.": "This is the screen that workspace members will first see when they sign in.", "Danger": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "You can delete this entire workspace including collections, documents, and users.": "You can delete this entire workspace including collections, documents, and users.", "Export data": "Export data", "A full export might take some time, consider exporting a single document or collection. You may leave this page once the export has started – if you have notifications enabled, we will email a link to <em>{{ userEmail }}</em> when it’s complete.": "A full export might take some time, consider exporting a single document or collection. You may leave this page once the export has started – if you have notifications enabled, we will email a link to <em>{{ userEmail }}</em> when it’s complete.", "Recent exports": "Recent exports", "Manage optional and beta features. Changing these settings will affect the experience for all members of the workspace.": "Manage optional and beta features. Changing these settings will affect the experience for all members of the workspace.", "Separate editing": "Separate editing", "When enabled documents have a separate editing mode by default instead of being always editable. This setting can be overridden by user preferences.": "When enabled documents have a separate editing mode by default instead of being always editable. This setting can be overridden by user preferences.", "When enabled team members can add comments to documents.": "When enabled team members can add comments to documents.", "Create a group": "Csoport létrehozása", "Could not load groups": "Could not load groups", "New group": "New group", "Groups can be used to organize and manage the people on your team.": "A csoportok a csapatban lévő emberek szervezésére és kezelésére használhatók.", "No groups have been created yet": "No groups have been created yet", "Import a zip file of Markdown documents (exported from version 0.67.0 or earlier)": "Import a zip file of Markdown documents (exported from version 0.67.0 or earlier)", "Import data": "Import data", "Import a JSON data file exported from another {{ appName }} instance": "Import a JSON data file exported from another {{ appName }} instance", "Import pages from a Confluence instance": "Import pages from a Confluence instance", "Enterprise": "Enterprise", "Quickly transfer your existing documents, pages, and files from other tools and services into {{appName}}. You can also drag and drop any HTML, Markdown, and text documents directly into Collections in the app.": "Quickly transfer your existing documents, pages, and files from other tools and services into {{appName}}. You can also drag and drop any HTML, Markdown, and text documents directly into Collections in the app.", "Recent imports": "Recent imports", "Configure a variety of integrations with third-party services.": "Configure a variety of integrations with third-party services.", "Could not load members": "Could not load members", "Everyone that has signed into {{appName}} is listed here. It’s possible that there are other users who have access through {{signinMethods}} but haven’t signed in yet.": "<PERSON>t van minden<PERSON> fels<PERSON>, aki be<PERSON>tt az {{appName}} alkalmazásba. Lehetséges, hogy vannak olyan egy<PERSON><PERSON> f<PERSON>, akik {{signinMethods}} módon rendelkeznek ho<PERSON><PERSON><PERSON>, de még nem jelentkeztek be.", "Receive a notification whenever a new document is published": "Érte<PERSON><PERSON><PERSON><PERSON>, amikor új dokumentumot tesznek közzé", "Document updated": "Dokumentum frissítve", "Receive a notification when a document you are subscribed to is edited": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, amikor egy feliratkozott dokumentumot szerkesztenek", "Comment posted": "Hozzászólás <PERSON>üldve", "Receive a notification when a document you are subscribed to or a thread you participated in receives a comment": "Értesí<PERSON><PERSON>, amikor egy felirat<PERSON>zott dokumentumhoz vagy egy olyan té<PERSON>, am<PERSON><PERSON> r<PERSON> vett, hozzászólás érkezik", "Mentioned": "Megemlítve", "Receive a notification when someone mentions you in a document or comment": "Értes<PERSON><PERSON><PERSON>, amikor valaki mege<PERSON><PERSON><PERSON><PERSON> egy dokumentumban vagy hozzászólásban", "Receive a notification when a comment thread you were involved in is resolved": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, amikor <PERSON> egy olyan ho<PERSON><PERSON><PERSON>, am<PERSON><PERSON> is r<PERSON>zt vett", "Collection created": "Gyűjtemény l<PERSON>va", "Receive a notification whenever a new collection is created": "Értesí<PERSON><PERSON>, amikor új gyűjteményt hoznak létre", "Invite accepted": "Meghívás elfogadva", "Receive a notification when someone you invited creates an account": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, am<PERSON><PERSON>, a<PERSON><PERSON>, l<PERSON><PERSON><PERSON><PERSON> egy <PERSON>", "Invited to document": "Meghívva doku<PERSON>umba", "Receive a notification when a document is shared with you": "É<PERSON><PERSON><PERSON><PERSON><PERSON>, amikor egy dokumentumot megosztanak Ö<PERSON>l", "Invited to collection": "Meghívva gyűjteménybe", "Receive a notification when you are given access to a collection": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, am<PERSON>r hozzáférést kap egy gyűjteményhez", "Export completed": "Exportá<PERSON><PERSON> be<PERSON>", "Receive a notification when an export you requested has been completed": "Értesí<PERSON>s <PERSON>, amikor az Ön által kért export<PERSON><PERSON><PERSON>", "Getting started": "Első lépések", "Tips on getting started with features and functionality": "Tips on getting started with features and functionality", "New features": "<PERSON><PERSON>", "Receive an email when new features of note are added": "Receive an email when new features of note are added", "Notifications saved": "Értesítések elmentve", "Unsubscription successful. Your notification settings were updated": "Unsubscription successful. Your notification settings were updated", "Manage when and where you receive email notifications.": "Manage when and where you receive email notifications.", "The email integration is currently disabled. Please set the associated environment variables and restart the server to enable notifications.": "The email integration is currently disabled. Please set the associated environment variables and restart the server to enable notifications.", "Preferences saved": "Beállítások elmentve", "Delete account": "Fiók törlése", "Manage settings that affect your personal experience.": "A személyes élményt befolyásoló beállítások kezelése.", "Language": "Nyelv", "Choose the interface language. Community translations are accepted though our <2>translation portal</2>.": "A felület nyelvének kiválasztása. A közösségi fordítások a <2>fordítási portálon</2> keresztül kerülnek elfogadásra.", "Choose your preferred interface color scheme.": "A felület előnyben részesített színsémájának kiválasztása.", "Use pointer cursor": "Mutató k<PERSON>zor has<PERSON>", "Show a hand cursor when hovering over interactive elements.": "Kéz alakú kurzor megjelenítése az interaktív elemekre történő rámutatáskor.", "Show line numbers": "Sorszámok megjelenítése", "Show line numbers on code blocks in documents.": "Sorszámok megjelenítése a dokumentumokban lévő kódblokkokban.", "When enabled, documents have a separate editing mode. When disabled, documents are always editable when you have permission.": "Ha engedélyez<PERSON> van, akkor a dokumentumok külön szerkesztési móddal rendelkeznek. <PERSON> le van tilt<PERSON>, akkor a dokumentumok mindig szerkeszthetők, ha van jogosultsága.", "Remember previous location": "Emlékezés az előző helyre", "Automatically return to the document you were last viewing when the app is re-opened.": "Automatikus visszatérés az utoljára megtekintett dokumentumhoz az alkalmazás újbóli megnyitásakor.", "Smart text replacements": "Intelligens szöveghelyettesítések", "Auto-format text by replacing shortcuts with symbols, dashes, smart quotes, and other typographical elements.": "Automatikus szövegformázás a rövidítések szimbólumokkal, kö<PERSON><PERSON><PERSON>lekkel, intelligens idézőjelekkel és más tipográfiai elemekkel való helyettesítésével.", "You may delete your account at any time, note that this is unrecoverable": "You may delete your account at any time, note that this is unrecoverable", "Profile saved": "<PERSON>il <PERSON>", "Profile picture updated": "Profilkép f<PERSON>sítve", "Unable to upload new profile picture": "<PERSON><PERSON> le<PERSON> profilk<PERSON>", "Manage how you appear to other members of the workspace.": "Manage how you appear to other members of the workspace.", "Photo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Choose a photo or image to represent yourself.": "<PERSON><PERSON><PERSON><PERSON><PERSON> fénykép vagy kép kiválasztása.", "This could be your real name, or a nickname — however you’d like people to refer to you.": "Ez lehet a valódi neve vagy egy becenév – ahogyan azt szeretné, hogy az emberek hivatkozzanak Önre.", "Email address": "E-mail cím", "Are you sure you want to require invites?": "Are you sure you want to require invites?", "New users will first need to be invited to create an account. <em>Default role</em> and <em>Allowed domains</em> will no longer apply.": "New users will first need to be invited to create an account. <em>Default role</em> and <em>Allowed domains</em> will no longer apply.", "Settings that impact the access, security, and content of your workspace.": "Settings that impact the access, security, and content of your workspace.", "Allow members to sign-in with {{ authProvider }}": "Allow members to sign-in with {{ authProvider }}", "Disabled": "Disabled", "Allow members to sign-in using their email address": "Allow members to sign-in using their email address", "The server must have SMTP configured to enable this setting": "The server must have SMTP configured to enable this setting", "Access": "Access", "Allow users to send invites": "Allow users to send invites", "Allow editors to invite other people to the workspace": "Allow editors to invite other people to the workspace", "Require invites": "Require invites", "Require members to be invited to the workspace before they can create an account using SSO.": "Require members to be invited to the workspace before they can create an account using SSO.", "Default role": "Alapértelmezett szerep", "The default user role for new accounts. Changing this setting does not affect existing user accounts.": "The default user role for new accounts. Changing this setting does not affect existing user accounts.", "When enabled, documents can be shared publicly on the internet by any member of the workspace": "When enabled, documents can be shared publicly on the internet by any member of the workspace", "Viewer document exports": "Viewer document exports", "When enabled, viewers can see download options for documents": "When enabled, viewers can see download options for documents", "Users can delete account": "Users can delete account", "When enabled, users can delete their own account from the workspace": "When enabled, users can delete their own account from the workspace", "Rich service embeds": "Rich service embeds", "Links to supported services are shown as rich embeds within your documents": "Links to supported services are shown as rich embeds within your documents", "Collection creation": "Gyűjtemény létrehozása", "Allow editors to create new collections within the workspace": "Allow editors to create new collections within the workspace", "Workspace creation": "Workspace creation", "Allow editors to create new workspaces": "Allow editors to create new workspaces", "Could not load shares": "Could not load shares", "Sharing is currently disabled.": "<PERSON>hari<PERSON> is currently disabled.", "You can globally enable and disable public document sharing in the <em>security settings</em>.": "You can globally enable and disable public document sharing in the <em>security settings</em>.", "Documents that have been shared are listed below. Anyone that has the public link can access a read-only version of the document until the link has been revoked.": "Documents that have been shared are listed below. Anyone that has the public link can access a read-only version of the document until the link has been revoked.", "You can create templates to help your team create consistent and accurate documentation.": "Sablonokat hozhat létre, hogy segítse a csapatát a következetes és pontos dokumentáció létrehozásában.", "Alphabetical": "Betűrendi", "There are no templates just yet.": "<PERSON><PERSON><PERSON> még nincsenek sab<PERSON>.", "A confirmation code has been sent to your email address, please enter the code below to permanently destroy this workspace.": "A confirmation code has been sent to your email address, please enter the code below to permanently destroy this workspace.", "Confirmation code": "Megerősítő kód", "Deleting the <1>{{workspaceName}}</1> workspace will destroy all collections, documents, users, and associated data. You will be immediately logged out of {{appName}}.": "Deleting the <1>{{workspaceName}}</1> workspace will destroy all collections, documents, users, and associated data. You will be immediately logged out of {{appName}}.", "Please note that workspaces are completely separated. They can have a different domain, settings, users, and billing.": "Please note that workspaces are completely separated. They can have a different domain, settings, users, and billing.", "You are creating a new workspace using your current account — <em>{{email}}</em>": "You are creating a new workspace using your current account — <em>{{email}}</em>", "To create a workspace under another email please sign up from the homepage": "To create a workspace under another email please sign up from the homepage", "Trash emptied": "Trash emptied", "Are you sure you want to permanently delete all the documents in Trash? This action is immediate and cannot be undone.": "Are you sure you want to permanently delete all the documents in Trash? This action is immediate and cannot be undone.", "Recently deleted": "Recently deleted", "Trash is empty at the moment.": "Trash is empty at the moment.", "A confirmation code has been sent to your email address, please enter the code below to permanently destroy your account.": "A confirmation code has been sent to your email address, please enter the code below to permanently destroy your account.", "Are you sure? Deleting your account will destroy identifying data associated with your user and cannot be undone. You will be immediately logged out of {{appName}} and all your API tokens will be revoked.": "Are you sure? Deleting your account will destroy identifying data associated with your user and cannot be undone. You will be immediately logged out of {{appName}} and all your API tokens will be revoked.", "Delete my account": "Delete my account", "Today": "Ma", "Yesterday": "Tegnap", "Last week": "Előző hét", "This month": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Last month": "Előző hónap", "This year": "This year", "Expired yesterday": "Expired yesterday", "Expired {{ date }}": "Expired {{ date }}", "Expires today": "Expires today", "Expires tomorrow": "Expires tomorrow", "Expires {{ date }}": "Expires {{ date }}", "Whoops, you need to accept the permissions in GitHub to connect {{appName}} to your workspace. Try again?": "Whoops, you need to accept the permissions in GitHub to connect {{appName}} to your workspace. Try again?", "Something went wrong while authenticating your request. Please try logging in again.": "Something went wrong while authenticating your request. Please try logging in again.", "The owner of GitHub account has been requested to install the {{githubAppName}} GitHub app. Once approved, previews will be shown for respective links.": "The owner of GitHub account has been requested to install the {{githubAppName}} GitHub app. Once approved, previews will be shown for respective links.", "Enable previews of GitHub issues and pull requests in documents by connecting a GitHub organization or specific repositories to {appName}.": "Enable previews of GitHub issues and pull requests in documents by connecting a GitHub organization or specific repositories to {appName}.", "Enabled by {{integrationCreatedBy}}": "Enabled by {{integrationCreated<PERSON>y}}", "Disconnecting will prevent previewing GitHub links from this organization in documents. Are you sure?": "Disconnecting will prevent previewing GitHub links from this organization in documents. Are you sure?", "The GitHub integration is currently disabled. Please set the associated environment variables and restart the server to enable the integration.": "The GitHub integration is currently disabled. Please set the associated environment variables and restart the server to enable the integration.", "Google Analytics": "Google Analytics", "Add a Google Analytics 4 measurement ID to send document views and analytics from the workspace to your own Google Analytics account.": "Add a Google Analytics 4 measurement ID to send document views and analytics from the workspace to your own Google Analytics account.", "Measurement ID": "Measurement ID", "Create a \"Web\" stream in your Google Analytics admin dashboard and copy the measurement ID from the generated code snippet to install.": "Create a \"Web\" stream in your Google Analytics admin dashboard and copy the measurement ID from the generated code snippet to install.", "Whoops, you need to accept the permissions in Linear to connect {{appName}} to your workspace. Try again?": "Whoops, you need to accept the permissions in Linear to connect {{appName}} to your workspace. Try again?", "Something went wrong while processing your request. Please try again.": "Something went wrong while processing your request. Please try again.", "Enable previews of Linear issues in documents by connecting a Linear workspace to {appName}.": "Enable previews of Linear issues in documents by connecting a Linear workspace to {appName}.", "Disconnecting will prevent previewing Linear links from this workspace in documents. Are you sure?": "Disconnecting will prevent previewing Linear links from this workspace in documents. Are you sure?", "The Linear integration is currently disabled. Please set the associated environment variables and restart the server to enable the integration.": "The Linear integration is currently disabled. Please set the associated environment variables and restart the server to enable the integration.", "Configure a Matomo installation to send views and analytics from the workspace to your own Matomo instance.": "Configure a Matomo installation to send views and analytics from the workspace to your own Matomo instance.", "Instance URL": "Instance URL", "The URL of your Matomo instance. If you are using Matomo Cloud it will end in matomo.cloud/": "The URL of your Matomo instance. If you are using Matomo Cloud it will end in matomo.cloud/", "Site ID": "Site ID", "An ID that uniquely identifies the website in your Matomo instance.": "An ID that uniquely identifies the website in your Matomo instance.", "Whoops, you need to accept the permissions in Notion to connect {{ appName }} to your workspace. Try again?": "Whoops, you need to accept the permissions in Notion to connect {{ appName }} to your workspace. Try again?", "Import pages from Notion": "Import pages from Notion", "Add to Slack": "Hozzáad<PERSON>", "document published": "dokumentum közzétéve", "document updated": "dokumentum frissítve", "Posting to the <em>{{ channelName }}</em> channel on": "Posting to the <em>{{ channelName }}</em> channel on", "These events should be posted to Slack": "These events should be posted to Slack", "This will prevent any future updates from being posted to this Slack channel. Are you sure?": "This will prevent any future updates from being posted to this Slack channel. Are you sure?", "Whoops, you need to accept the permissions in Slack to connect {{appName}} to your workspace. Try again?": "Whoops, you need to accept the permissions in Slack to connect {{appName}} to your workspace. Try again?", "Personal account": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Link your {{appName}} account to Slack to enable searching and previewing the documents you have access to, directly within chat.": "Link your {{appName}} account to Slack to enable searching and previewing the documents you have access to, directly within chat.", "Disconnecting your personal account will prevent searching for documents from Slack. Are you sure?": "Disconnecting your personal account will prevent searching for documents from S<PERSON>ck. Are you sure?", "Slash command": "Slash command", "Get rich previews of {{ appName }} links shared in Slack and use the <em>{{ command }}</em> slash command to search for documents without leaving your chat.": "Get rich previews of {{ appName }} links shared in Slack and use the <em>{{ command }}</em> slash command to search for documents without leaving your chat.", "This will remove the Outline slash command from your Slack workspace. Are you sure?": "This will remove the Outline slash command from your Slack workspace. Are you sure?", "Connect {{appName}} collections to Slack channels. Messages will be automatically posted to Slack when documents are published or updated.": "Connect {{appName}} collections to Slack channels. Messages will be automatically posted to Slack when documents are published or updated.", "Comment by {{ author }} on \"{{ title }}\"": "Comment by {{ author }} on \"{{ title }}\"", "How to use {{ command }}": "How to use {{ command }}", "To search your workspace use {{ command }}. \nType {{ command2 }} help to display this help text.": "To search your workspace use {{ command }}. \nType {{ command2 }} help to display this help text.", "Post to Channel": "Post to Channel", "This is what we found for \"{{ term }}\"": "This is what we found for \"{{ term }}\"", "No results for \"{{ term }}\"": "No results for \"{{ term }}\"", "It looks like you haven’t linked your {{ appName }} account to Slack yet": "It looks like you haven’t linked your {{ appName }} account to Slack yet", "Link your account": "Link your account", "Link your account in {{ appName }} settings to search from Slack": "Link your account in {{ appName }} settings to search from Slack", "Configure a Umami installation to send views and analytics from the workspace to your own Umami instance.": "Configure a Umami installation to send views and analytics from the workspace to your own Umami instance.", "The URL of your Umami instance. If you are using Umami Cloud it will begin with {{ url }}": "The URL of your Umami instance. If you are using Umami Cloud it will begin with {{ url }}", "Script name": "Script name", "The name of the script file that Umami uses to track analytics.": "The name of the script file that <PERSON><PERSON> uses to track analytics.", "An ID that uniquely identifies the website in your Umami instance.": "An ID that uniquely identifies the website in your Umami instance.", "Are you sure you want to delete the {{ name }} webhook?": "Biztosan törölni szeretné a(z) {{ name }} webhorgot?", "Webhook updated": "Webhorog frissítve", "Update": "Update", "Updating": "Updating", "Provide a descriptive name for this webhook and the URL we should send a POST request to when matching events are created.": "Adjon egy leíró nevet ennek a webhorognak és URL-nek, amelyre POST-kérést kell küldenünk, amikor illeszkedő események jönnek létre.", "A memorable identifer": "A memorable identifer", "URL": "URL", "Signing secret": "Signing secret", "Subscribe to all events, groups, or individual events. We recommend only subscribing to the minimum amount of events that your application needs to function.": "Subscribe to all events, groups, or individual events. We recommend only subscribing to the minimum amount of events that your application needs to function.", "All events": "<PERSON><PERSON> esem<PERSON>y", "All {{ groupName }} events": "All {{ groupName }} events", "Delete webhook": "Webhorog törlése", "Subscribed events": "Subscribed events", "Edit webhook": "Webhorog szerkesztése", "Webhook created": "Webhorog létrehozva", "Webhooks": "Webhorgok", "New webhook": "<PERSON><PERSON>", "Webhooks can be used to notify your application when events happen in {{appName}}. Events are sent as a https request with a JSON payload in near real-time.": "A webhorgok az alkalmazás értesítésére has<PERSON>lhatók, amikor az {{appName}} alkalmazásban események történnek. Az események HTTPS-kérésként kerülnek elküldésre JSON-tartalommal, közel valós időben.", "Inactive": "Inaktív", "Create a webhook": "Webhorog létrehozása", "Zapier is a platform that allows {{appName}} to easily integrate with thousands of other business tools. Automate your workflows, sync data, and more.": "Zapier is a platform that allows {{appName}} to easily integrate with thousands of other business tools. Automate your workflows, sync data, and more.", "Never logged in": "Never logged in", "Online now": "Online now", "Online {{ timeAgo }}": "Online {{ timeAgo }}", "Viewed just now": "Viewed just now", "You updated {{ timeAgo }}": "You updated {{ timeAgo }}", "{{ user }} updated {{ timeAgo }}": "{{ user }} updated {{ timeAgo }}", "You created {{ timeAgo }}": "<PERSON>n ho<PERSON> l<PERSON> {{ timeAgo }}", "{{ user }} created {{ timeAgo }}": "{{ user }} created {{ timeAgo }}", "Error loading data": "Error loading data"}
{"New API key": "کلید API جدید", "Open collection": "باز کردن مجموعه", "New collection": "مجموعه جدید", "Create a collection": "ایجاد مجموعه", "Edit": "ویرایش", "Edit collection": "ویرایش مجموعه", "Permissions": "دسترسی ها", "Collection permissions": "دسترسی‌های مجموعه", "Share this collection": "جستجو در مجموعه", "Search in collection": "جستجو در مجموعه", "Star": "ستاره‌گذاری", "Unstar": "برداشتن ستاره", "Subscribe": "اشتراک", "Subscribed to document notifications": "در اعلان‌های اسناد مشترک شد", "Unsubscribe": "لغو اشتراک", "Unsubscribed from document notifications": "اشتراک در اعلان‌های سند لغو شد", "Archive": "آرشیو", "Archive collection": "آرشیو مجموعه", "Collection archived": "مجموعه آرشیو شد", "Archiving": "در حال بایگانی", "Archiving this collection will also archive all documents within it. Documents from the collection will no longer be visible in search results.": "Archiving this collection will also archive all documents within it. Documents from the collection will no longer be visible in search results.", "Restore": "بازیا<PERSON>ی", "Collection restored": "ایجاد مجموعه", "Delete": "<PERSON><PERSON><PERSON>", "Delete collection": "حذ<PERSON> مجموعه", "New template": "<PERSON>ا<PERSON><PERSON> جدید", "Delete comment": "حذ<PERSON> نظر", "Mark as resolved": "", "Thread resolved": "Thread resolved", "Mark as unresolved": "<PERSON> as unresolved", "View reactions": "View reactions", "Reactions": "واکنش", "Copy ID": "کپی شناسه", "Clear IndexedDB cache": "<PERSON>ا<PERSON>ی کردن کش دیتابیس", "IndexedDB cache cleared": "کش دیتابیس پاکسازی شد", "Toggle debug logging": "فعالسازی لاگ اشکال یاب", "Debug logging enabled": "لاگ اشکال یاب روشن شد", "Debug logging disabled": "لاگ اشکال یاب خاموش شد", "Development": "م<PERSON><PERSON><PERSON> توسعه", "Open document": "باز کردن سند", "New document": "سند جدید", "New draft": "پیش‌نویس", "New from template": "<PERSON><PERSON><PERSON><PERSON> از قالب", "New nested document": "ایجاد زیر<PERSON>ند جدید", "Publish": "انتشار", "Published {{ documentName }}": "{{ documentName }} منتشر شد", "Publish document": "انتشار سند", "Unpublish": "لغو انتشار", "Unpublished {{ documentName }}": "{{ documentName }} از حالت انتشار خارج شد", "Share this document": "اشتراک‌گذاری این سند", "HTML": "HTML", "PDF": "PDF", "Exporting": "صدور", "Markdown": "MarkDown", "Download": "بارگیری", "Download document": "دریا<PERSON>ت سند", "Copy as Markdown": "کپی markdown", "Markdown copied to clipboard": "markdown ک<PERSON>ی شد", "Copy as text": "رونوشت مانند متن", "Text copied to clipboard": "متن در کلیپ‌بورد کپی شد", "Copy public link": "کپی کردن لینک عمومی", "Link copied to clipboard": "پیوند در بریده‌دان کپی شد", "Copy link": "کپی پیوند", "Copy": "کپی", "Duplicate": "ک<PERSON>ی کردن", "Duplicate document": "سند تکرار گردد", "Copy document": "ک<PERSON>ی سند", "collection": "مجموعه", "Pin to {{collectionName}}": "پین در {{collectionName}}", "Pinned to collection": "سنجاق کردن به مجموعه", "Pin to home": "سنجاق کردن به صفحه اول", "Pinned to home": "به صفحه اصلی پین شد", "Pin": "سنجاق", "Search in document": "جستجو در مستندات", "Print": "چاپ", "Print document": "چاپ سند", "Import document": "وارد کردن سند", "Templatize": "تبدیل شدن به قالب", "Create template": "ای<PERSON><PERSON> قالب", "Open random document": "بازکردن مستند تصادفی", "Search documents for \"{{searchQuery}}\"": "جستجوی اسناد برای \"{{searchQuery}}\"", "Move to workspace": "وارد شدن به workspace", "Move": "انتقال", "Move to collection": "سنجاق کردن به مجموعه", "Move {{ documentType }}": "انتقال {{ documentType }}", "Are you sure you want to archive this document?": "آیا اطمینان دارید که میخواهید این سند را بایگانی کنید ؟", "Document archived": "سند آرشیو شد", "Archiving this document will remove it from the collection and search results.": "بایگانی کردن این سند به حذف آن نتایج جستجو و مجموعه های خواهد انجامید.", "Delete {{ documentName }}": "حذف {{ documentName }}", "Permanently delete": "حذف برای همیشه", "Permanently delete {{ documentName }}": "حذ<PERSON> همیشگی {{ documentName }}", "Empty trash": "خالی کردن زباله دان", "Permanently delete documents in trash": "", "Comments": "نظرات", "History": "تاریخچه", "Insights": "بینش ها", "Disable viewer insights": "Disable viewer insights", "Enable viewer insights": "Enable viewer insights", "Leave document": "ذ<PERSON><PERSON><PERSON>ه سند", "You have left the shared document": "You have left the shared document", "Could not leave document": "Could not leave document", "Home": "خانه", "Drafts": "پیش‌نویس‌ها", "Search": "جستجو", "Trash": "زباله‌دان", "Settings": "تنظیمات", "Profile": "پروفایل", "Templates": "قالب‌ها", "Notifications": "اعلان‌ها", "Preferences": "تنظیمات", "Documentation": "مستندات", "API documentation": "مستندات API", "Toggle sidebar": "تغییر وضعیت نوار کنار صفحه", "Send us feedback": "ارسال بازخورد", "Report a bug": "گزارش مشکل", "Changelog": "سابقه تغییرات", "Keyboard shortcuts": "میان‌برهای صفحه کلید", "Download {{ platform }} app": "Download {{ platform }} app", "Log out": "خروج", "Mark notifications as read": "Mark notifications as read", "Archive all notifications": "Archive all notifications", "New App": "New App", "New Application": "New Application", "This version of the document was deleted": "This version of the document was deleted", "Link copied": "پیوند کپی شد", "Dark": "تیره", "Light": "روشن", "System": "سامانه", "Appearance": "ظاهر برنامه", "Change theme": "تغییر پوسته", "Change theme to": "تغییر پوسته به", "Switch workspace": "تغییر فضای کاری", "Select a workspace": "انتخاب فضای کاری", "New workspace": "فضای کار جدید", "Create a workspace": "فضای کاری ایجاد کنید", "Login to workspace": "وارد شدن به workspace", "Invite people": "دعوت از افراد", "Invite to workspace": "وارد شدن به workspace", "Promote to {{ role }}": "Promote to {{ role }}", "Demote to {{ role }}": "Demote to {{ role }}", "Update role": "به روزرسانی نقش", "Delete user": "<PERSON><PERSON><PERSON> کاربر", "Collection": "مجموعه", "Collections": "مجموعه‌ها", "Debug": "دیباگ", "Document": "سند", "Documents": "اسناد", "Recently viewed": "اخیراً دیده شده", "Revision": "بازنگری", "Navigation": "پیمایش", "Notification": "اعلانات", "People": "افراد", "Workspace": "فضای کار", "Recent searches": "جستجوهای اخیر", "currently editing": "در حال ویرایش", "currently viewing": "در حال مشاهده", "previously edited": "قبلاً ویرایش شده", "You": "You", "Viewers": "مشاهده کنندگان", "Collections are used to group documents and choose permissions": "Collections are used to group documents and choose permissions", "Name": "نام", "The default access for workspace members, you can share with more users or groups later.": "The default access for workspace members, you can share with more users or groups later.", "Public document sharing": "اشتراک‌گذاری عمومی سندها", "Allow documents within this collection to be shared publicly on the internet.": "در صورت فعال بودن، هر سندی در این مجموعه را می توان به صورت عمومی در اینترنت به اشتراک گذاشت.", "Commenting": "Commenting", "Allow commenting on documents within this collection.": "Allow commenting on documents within this collection.", "Saving": "در حال ذخیره", "Save": "ذخیره", "Creating": "در حال ساخت", "Create": "ساختن", "Collection deleted": "ایجاد مجموعه", "I’m sure – Delete": "مطمئن هستم - ح<PERSON><PERSON> شود", "Deleting": "در حال حذف کردن", "Are you sure about that? Deleting the <em>{{collectionName}}</em> collection is permanent and cannot be restored, however all published documents within will be moved to the trash.": "آیا مطمئن هستید؟ حذف مجموعه <em>{{collectionName}}</em> دائمی و غیرقابل بازیابی‌ست؛ هرچند اسناد داخل آن به سطل زباله منتقل می‌شوند.", "Also, <em>{{collectionName}}</em> is being used as the start view – deleting it will reset the start view to the Home page.": "همچنین، <em>{{collectionName}}</em> به عنوان نمای شروع استفاده می شود - با حذف آن، نمای شروع به صفحه اصلی بازگردانده می شود.", "Type a command or search": "دستوری تایپ و یا جستجو کنید", "Choose a template": "انت<PERSON>اب قالب", "Are you sure you want to permanently delete this entire comment thread?": "Are you sure you want to permanently delete this entire comment thread?", "Are you sure you want to permanently delete this comment?": "آیا مطمئنید که می‌خواهید کامنت را برای همیشه حذف کنید؟", "Confirm": "تا<PERSON><PERSON>د", "manage access": "مدیریت دسترسی", "view and edit access": "دسترسی مشاهده و ویرایش", "view only access": "دسترسی صرفا نمایش", "no access": "بدون دسترسی", "You do not have permission to move {{ documentName }} to the {{ collectionName }} collection": "You do not have permission to move {{ documentName }} to the {{ collectionName }} collection", "Move document": "انتقال سند", "Moving": "در حال حرکت", "Moving the document <em>{{ title }}</em> to the {{ newCollectionName }} collection will change permission for all workspace members from <em>{{ prevPermission }}</em> to <em>{{ newPermission }}</em>.": "Moving the document <em>{{ title }}</em> to the {{ newCollectionName }} collection will change permission for all workspace members from <em>{{ prevPermission }}</em> to <em>{{ newPermission }}</em>.", "Submenu": "زیرمنو", "Collections could not be loaded, please reload the app": "مجموعه‌ها بارگذاری نمی‌شوند، لطفاً برنامه را دوباره بارگیری کنید", "Default collection": "مجموعه پیش فرض", "Start view": "نمای شروع", "Install now": "اکنون نصب شود", "Deleted Collection": "مجموعه‌های حذف شده", "Untitled": "بدون عنوان", "Unpin": "برداشتن سنجاق", "{{ minutes }}m read": "{{ minutes }}m read", "Select a location to copy": "Select a location to copy", "Document copied": "فایل مورد نظر کپیشد", "Couldn’t copy the document, try again?": "امکان ایجاد سند وجود نداشت، دوباره تلاش شود؟", "Include nested documents": "Include nested documents", "Copy to <em>{{ location }}</em>": "Copy to <em>{{ location }}</em>", "Search collections & documents": "جستجوی مجموعه ها و اسناد", "No results found": "No results found", "New": "ج<PERSON><PERSON><PERSON>", "Only visible to you": "تنها قابل مشاهده برای شما", "Draft": "پیش‌نویس", "Template": "قالب", "You updated": "You updated", "{{ userName }} updated": "{{ userName }} updated", "You deleted": "<PERSON><PERSON><PERSON> ک<PERSON>دی", "{{ userName }} deleted": "{{ userName }} ح<PERSON><PERSON> شد", "You archived": "آرشیو کردی", "{{ userName }} archived": "{{ userName }} بایگانی شد", "Imported": "Imported", "You created": "شما ایجاد کردید", "{{ userName }} created": "{{ userName }} ایج<PERSON> شد", "You published": "منتشر کردی", "{{ userName }} published": "{{ userName }} منتشر کرد", "Never viewed": "هرگز مشاهده نشده", "Viewed": "Viewed", "in": "in", "nested document": "زیر<PERSON>ند", "nested document_plural": "nested documents", "{{ total }} task": "{{ total }} وظیفه", "{{ total }} task_plural": "{{ total }} tasks", "{{ completed }} task done": "کار {{ completed }} انجام شد", "{{ completed }} task done_plural": "{{ completed }} tasks done", "{{ completed }} of {{ total }} tasks": "{{ completed }} از کار {{ total }}", "Currently editing": "در حال ویرایش", "Currently viewing": "در حال مشاهده", "Viewed {{ timeAgo }}": "Viewed {{ timeAgo }}", "Module failed to load": "بارگیری ماژول با خطا مواجه شد", "Loading Failed": "بارگیری ناموفق", "Sorry, part of the application failed to load. This may be because it was updated since you opened the tab or because of a failed network request. Please try reloading.": "متأسفانه امکان بارگیری بخشی از برنامه وجود نداشت. این خطا ممکن است به خاطر به‌روزرسانی ماژول یا اختلال شبکه ایجاد شده باشد. لطفاً دوباره صفحه را بارگیری کنید.", "Reload": "بارگیری مجدد", "Something Unexpected Happened": "اتفاق غیر منتظره‌ای رخ داده است", "Sorry, an unrecoverable error occurred{{notified}}. Please try reloading the page, it may have been a temporary glitch.": "متأسفانه یک خطای غیرقابل بازیابی رخ داد {{notified}}. لطفاً دوباره صفحه را بارگیری کنید، خطا ممکن است موقت بوده باشد.", "our engineers have been notified": "به مهندسان ما اطلاع‌رسانی شد", "Show detail": "Show detail", "Revision deleted": "Revision deleted", "Current version": "Current version", "{{userName}} edited": "{{userName}} ویرایش شد", "{{userName}} archived": "{{userName}} بایگانی شد", "{{userName}} restored": "{{userName}} بازیا<PERSON>ی شد", "{{userName}} deleted": "{{userName}} ح<PERSON><PERSON> شد", "{{userName}} added {{addedUserName}}": "{{userName}} added {{addedUserName}}", "{{userName}} removed {{removedUserName}}": "{{userName}} removed {{removedUserName}}", "{{userName}} moved from trash": "{{userName}} از سطل زباله خارج شد", "{{userName}} published": "{{userName}} منتشر شد", "{{userName}} unpublished": "{{userName}} unpublished", "{{userName}} moved": "{{userName}} منتقل شد", "Export started": "Export started", "Your file will be available in {{ location }} soon": "Your file will be available in {{ location }} soon", "View": "View", "A ZIP file containing the images, and documents in the Markdown format.": "A ZIP file containing the images, and documents in the Markdown format.", "A ZIP file containing the images, and documents as HTML files.": "A ZIP file containing the images, and documents as HTML files.", "Structured data that can be used to transfer data to another compatible {{ appName }} instance.": "Structured data that can be used to transfer data to another compatible {{ appName }} instance.", "Export": "صدور", "Exporting the collection <em>{{collectionName}}</em> may take some time.": "Exporting the collection <em>{{collectionName}}</em> may take some time.", "You will receive an email when it's complete.": "You will receive an email when it's complete.", "Include attachments": "Include attachments", "Including uploaded images and files in the exported data": "Including uploaded images and files in the exported data", "{{count}} more user": "{{count}} more user", "{{count}} more user_plural": "{{count}} more users", "Filter": "فیلتر", "No results": "بدون نتیجه", "{{authorName}} created <3></3>": "{{authorName}} ایجاد شد>", "{{authorName}} opened <3></3>": "{{authorName}} opened <3></3>", "Search emoji": "جستجوی ایموجی", "Search icons": "جستجو آیکون", "Choose default skin tone": "Choose default skin tone", "Show menu": "نمایش منو", "Icon Picker": "انتخابگر آیکون", "Icons": "آیکون ها", "Emojis": "شکلک ها", "Remove": "<PERSON><PERSON><PERSON>", "All": "همه", "Frequently Used": "پر استفاده", "Search Results": "نتایج جستجو", "Smileys & People": "شکلک‌ها و مردم", "Animals & Nature": "حیوانات و طبیعت", "Food & Drink": "غذا و نوشیدنی", "Activity": "فعالیت", "Travel & Places": "سفر & مکان‌ها", "Objects": "اشیاء", "Symbols": "نمادها", "Flags": "علامت‌ها", "Select a color": "یک رنگ را انتخاب کنید", "Loading": "بارگذاری", "Permission": "مجوز", "View only": "فقط مشاهده", "Can edit": "قابل ویرایش", "No access": "بدون دسترسی", "Default access": "دسترسی پیش‌فرض", "Change Language": "تغییر زبان", "Dismiss": "ر<PERSON> کر<PERSON>ن", "You’re offline.": "شما آفلا<PERSON>ن هستید.", "Sorry, an error occurred.": "متاس<PERSON>یم، یک خطا رخ داد.", "Click to retry": "برای امتحان مجدد کلیک کنید", "Back": "بازگشت", "Unknown": "ناشناخته", "Mark all as read": "علامتگذاری همه بعنوان خوانده شده", "You're all caught up": "شما همه اعلانها و پیامها را دیده اید", "Icon": "Icon", "My App": "My App", "Tagline": "Tagline", "A short description": "A short description", "Callback URLs": "Callback URLs", "Published": "Published", "Allow this app to be installed by other workspaces": "Allow this app to be installed by other workspaces", "{{ username }} reacted with {{ emoji }}": "{{ username }} reacted with {{ emoji }}", "{{ firstUsername }} and {{ secondUsername }} reacted with {{ emoji }}": "{{ firstUsername }} and {{ secondUsername }} reacted with {{ emoji }}", "{{ firstUsername }} and {{ count }} others reacted with {{ emoji }}": "{{ firstUsername }} and {{ count }} other reacted with {{ emoji }}", "{{ firstUsername }} and {{ count }} others reacted with {{ emoji }}_plural": "{{ firstUsername }} and {{ count }} others reacted with {{ emoji }}", "Add reaction": "افزودن بازخورد", "Reaction picker": "Reaction picker", "Could not load reactions": "Could not load reactions", "Reaction": "واکنش", "Results": "نتایج", "No results for {{query}}": "No results for {{query}}", "Manage": "مدی<PERSON><PERSON>ت", "All members": "همه عضوها", "Everyone in the workspace": "Everyone in the workspace", "{{ count }} member": "{{ count }} member", "{{ count }} member_plural": "{{ count }} members", "Invite": "دعوت", "{{ userName }} was added to the collection": "{{ userName }} به مجموعه اضافه شد", "{{ count }} people added to the collection": "{{ count }} به مجموعه اضافه شد", "{{ count }} people added to the collection_plural": "{{ count }} به مجموعه اضافه شد", "{{ count }} people and {{ count2 }} groups added to the collection": "{{ count }} people and {{ count2 }} groups added to the collection", "{{ count }} people and {{ count2 }} groups added to the collection_plural": "{{ count }} people and {{ count2 }} groups added to the collection", "Add": "افزودن", "Add or invite": "Add or invite", "Viewer": "ناظر", "Editor": "ویرایشگر", "Suggestions for invitation": "Suggestions for invitation", "No matches": "موردی یافت نشد", "Can view": "می‌توان مشاهده کرد", "Everyone in the collection": "Everyone in the collection", "You have full access": "You have full access", "Created the document": "Created the document", "Other people": "Other people", "Other workspace members may have access": "Other workspace members may have access", "This document may be shared with more workspace members through a parent document or collection you do not have access to": "This document may be shared with more workspace members through a parent document or collection you do not have access to", "Access inherited from collection": "Access inherited from collection", "{{ userName }} was removed from the document": "{{ userName }} was removed from the document", "Could not remove user": "امکان حذف کاربر وجود نداشت", "Permissions for {{ userName }} updated": "Permissions for {{ userName }} updated", "Could not update user": "امکان به‌روزرسانی کاربر وجود نداشت", "Has access through <2>parent</2>": "Has access through <2>parent</2>", "Suspended": "معلق", "Invited": "دعوت شده", "Active <1></1> ago": "فعال در <1></1> پیش", "Never signed in": "تاکنون وارد نشده است", "Leave": "Leave", "Only lowercase letters, digits and dashes allowed": "Only lowercase letters, digits and dashes allowed", "Sorry, this link has already been used": "Sorry, this link has already been used", "Public link copied to clipboard": "Public link copied to clipboard", "Web": "Web", "Anyone with the link can access because the parent document, <2>{{documentTitle}}</2>, is shared": "Anyone with the link can access because the parent document, <2>{{documentTitle}}</2>, is shared", "Allow anyone with the link to access": "Allow anyone with the link to access", "Publish to internet": "انتشار روی اینترنت", "Search engine indexing": "Search engine indexing", "Disable this setting to discourage search engines from indexing the page": "Disable this setting to discourage search engines from indexing the page", "Show last modified": "Show last modified", "Display the last modified timestamp on the shared page": "Display the last modified timestamp on the shared page", "Nested documents are not shared on the web. Toggle sharing to enable access, this will be the default behavior in the future": "Nested documents are not shared on the web. Toggle sharing to enable access, this will be the default behavior in the future", "{{ userName }} was added to the document": "{{ userName }} was added to the document", "{{ count }} people added to the document": "{{ count }} people added to the document", "{{ count }} people added to the document_plural": "{{ count }} people added to the document", "{{ count }} groups added to the document": "{{ count }} groups added to the document", "{{ count }} groups added to the document_plural": "{{ count }} groups added to the document", "Logo": "لوگو", "Archived collections": "Archived collections", "New doc": "سند جدید", "Empty": "<PERSON>ا<PERSON><PERSON>", "Collapse": "جمع کردن", "Expand": "باز کردن", "Document not supported – try Markdown, Plain text, HTML, or Word": "نوع سند پشتیبانی نمی‌شود - ا<PERSON>down، متن ساده، HTML، یا Word استفاده کنید", "Go back": "Go back", "Go forward": "Go forward", "Could not load shared documents": "Could not load shared documents", "Shared with me": "Shared with me", "Show more": "اطلاعات بیشتر", "Could not load starred documents": "Could not load starred documents", "Starred": "ستاره‌دار", "Up to date": "به روز", "{{ releasesBehind }} versions behind": "{{ releasesBehind }} version behind", "{{ releasesBehind }} versions behind_plural": "{{ releasesBehind }} versions behind", "Change permissions?": "تغییر مجوز ها؟", "{{ documentName }} cannot be moved within {{ parentDocumentName }}": "{{ documentName }} cannot be moved within {{ parentDocumentName }}", "You can't reorder documents in an alphabetically sorted collection": "You can't reorder documents in an alphabetically sorted collection", "The {{ documentName }} cannot be moved here": "The {{ documentName }} cannot be moved here", "Return to App": "بازگشت به برنامه", "Installation": "نسخه", "Unstar document": "Unstar document", "Star document": "Star document", "Template created, go ahead and customize it": "الگو ایجاد شد، پیش بروید و آن را سفارشی کنید", "Creating a template from <em>{{titleWithDefault}}</em> is a non-destructive action – we'll make a copy of the document and turn it into a template that can be used as a starting point for new documents.": "ایجاد الگو از <em>{{titleWithDefault}}</em> باعث تخریب چیزی نمی‌شود - ما یک کپی از سند ایجاد می کنیم و آن را به الگویی تبدیل می کنیم که می تواند به عنوان نقطه شروع اسناد جدید استفاده شود.", "Enable other members to use the template immediately": "Enable other members to use the template immediately", "Location": "Location", "Admins can manage the workspace and access billing.": "Admins can manage the workspace and access billing.", "Editors can create, edit, and comment on documents.": "Editors can create, edit, and comment on documents.", "Viewers can only view and comment on documents.": "Viewers can only view and comment on documents.", "Are you sure you want to make {{ userName }} a {{ role }}?": "Are you sure you want to make {{ userName }} a {{ role }}?", "I understand, delete": "فهمیدم، حذف کن", "Are you sure you want to permanently delete {{ userName }}? This operation is unrecoverable, consider suspending the user instead.": "Are you sure you want to permanently delete {{ userName }}? This operation is unrecoverable, consider suspending the user instead.", "Are you sure you want to suspend {{ userName }}? Suspended users will be prevented from logging in.": "Are you sure you want to suspend {{ userName }}? Suspended users will be prevented from logging in.", "New name": "New name", "Name can't be empty": "Name can't be empty", "Check your email to verify the new address.": "Check your email to verify the new address.", "The email will be changed once verified.": "The email will be changed once verified.", "You will receive an email to verify your new address. It must be unique in the workspace.": "You will receive an email to verify your new address. It must be unique in the workspace.", "A confirmation email will be sent to the new address before it is changed.": "A confirmation email will be sent to the new address before it is changed.", "New email": "New email", "Email can't be empty": "Email can't be empty", "Your import completed": "Your import completed", "Previous match": "Previous match", "Next match": "Next match", "Find and replace": "Find and replace", "Find": "یافتن", "Match case": "Match case", "Enable regex": "Enable regex", "Replace options": "Replace options", "Replacement": "Replacement", "Replace": "Replace", "Replace all": "Replace all", "Profile picture": "تصویر پروفایل", "Create a new doc": "ایج<PERSON> سند جدید", "{{ userName }} won't be notified, as they do not have access to this document": "{{ userName }} won't be notified, as they do not have access to this document", "Keep as link": "Keep as link", "Mention": "Mention", "Embed": "Embed", "Add column after": "Add column after", "Add column before": "Add column before", "Add row after": "Add row after", "Add row before": "Add row before", "Align center": "تراز به مرکز", "Align left": "تراز به چپ", "Align right": "تراز به راست", "Default width": "Default width", "Full width": "تمام عرض", "Bulleted list": "لیست گلوله‌ای", "Todo list": "Task list", "Code block": "بلو<PERSON> کد", "Copied to clipboard": "در بریده‌دان کپی شد", "Code": "<PERSON><PERSON>", "Comment": "Comment", "Create link": "ایج<PERSON> پیوند", "Sorry, an error occurred creating the link": "متاسفانه خطایی در ذخیره‌سازی پیوند رخ داد", "Create a new child doc": "Create a new child doc", "Delete table": "<PERSON><PERSON><PERSON> جدول", "Delete file": "Delete file", "Width x Height": "Width x Height", "Download file": "Download file", "Replace file": "Replace file", "Delete image": "حذ<PERSON> تصویر", "Download image": "بارگیری تصویر", "Replace image": "تعویض عکس", "Italic": "مورب", "Sorry, that link won’t work for this embed type": "متاسفانه این پیوند برای این نوع جاسازی کار نمی‌کند", "File attachment": "File attachment", "Enter a link": "Enter a link", "Big heading": "عنوان بزرگ", "Medium heading": "عنوان متوسط", "Small heading": "عنوان کوچک", "Extra small heading": "Extra small heading", "Heading": "عنوان", "Divider": "جدا کننده", "Image": "تصویر", "Sorry, an error occurred uploading the file": "Sorry, an error occurred uploading the file", "Write a caption": "عنوانی بنویسید", "Info": "اطلاع‌رسانی", "Info notice": "اخطار اطلاع‌رسانی", "Link": "پیوند", "Highlight": "های<PERSON><PERSON><PERSON>ت", "Type '/' to insert": "Type '/' to insert", "Keep typing to filter": "برای جستجو به نوشتن ادامه دهید", "Open link": "باز کردن پیوند", "Go to link": "برو به لینک", "Sorry, that type of link is not supported": "Sorry, that type of link is not supported", "Ordered list": "لیست شماره‌دار", "Page break": "جداکننده صفحه", "Paste a link": "جایگذاری پیوند", "Paste a {{service}} link…": "جایگذاری پیوند {{service}}…", "Placeholder": "محل نگهدارنده", "Quote": "نقل قول", "Remove link": "<PERSON>ذ<PERSON> پیوند", "Search or paste a link": "جستجو کنید یا یک پیوند جایگذاری کنید", "Strikethrough": "<PERSON><PERSON> خورده", "Bold": "پررنگ", "Subheading": "زیرعنوان", "Sort ascending": "Sort ascending", "Sort descending": "Sort descending", "Table": "جدول", "Export as CSV": "Export as CSV", "Toggle header": "Toggle header", "Math inline (LaTeX)": "Math inline (LaTeX)", "Math block (LaTeX)": "Math block (LaTeX)", "Merge cells": "Merge cells", "Split cell": "Split cell", "Tip": "نکته", "Tip notice": "اخطار نکته", "Warning": "هشدار", "Warning notice": "اخطار هشدار", "Success": "Success", "Success notice": "Success notice", "Current date": "Current date", "Current time": "Current time", "Current date and time": "Current date and time", "Indent": "Indent", "Outdent": "Outdent", "Video": "Video", "None": "None", "Could not import file": "نمی‌توان فایل را وارد کرد", "Unsubscribed from document": "Unsubscribed from document", "Unsubscribed from collection": "Unsubscribed from collection", "Account": "ح<PERSON><PERSON><PERSON>", "API & Apps": "API & Apps", "Details": "جزئیات", "Security": "امنیت", "Features": "امکانات", "Members": "اعضا", "Groups": "گروه‌ها", "API Keys": "API Keys", "Applications": "Applications", "Shared Links": "Shared Links", "Import": "وارد کردن", "Install": "Install", "Integrations": "یکپارچه‌سازی‌ها", "Revoke token": "Revoke token", "Revoke": "Revoke", "Show path to document": "نمایش مسیر سند", "Path to document": "مسیر سند", "Group member options": "گزینه‌های عضو گروه", "Export collection": "صدور مجموعه", "Rename": "<PERSON><PERSON>", "Sort in sidebar": "مرتب‌سازی در نوار کناری", "A-Z sort": "A-Z sort", "Z-A sort": "Z-A sort", "Manual sort": "مرتب‌سازی دستی", "Comment options": "Comment options", "Show document menu": "Show document menu", "{{ documentName }} restored": "{{ documentName }} restored", "Document options": "گزینه‌های سند", "Choose a collection": "انتخاب یک مجموعه", "Subscription inherited from collection": "Subscription inherited from collection", "Apply template": "Apply template", "Enable embeds": "فعال‌سازی جاسازی‌ها", "Export options": "گزینه های صدور", "Group members": "اعضای گروه", "Edit group": "ویرایش گروه", "Delete group": "حذ<PERSON> گروه", "Group options": "گزینه‌های گروه", "Cancel": "لغو", "Import menu options": "Import menu options", "Member options": "گزینه‌های اعضا", "New document in <em>{{ collectionName }}</em>": "سند جدید در <em>{{ collectionName }}</em>", "New child document": "سند فرزند جدید", "Save in workspace": "Save in workspace", "Notification settings": "Notification settings", "Revoke {{ appName }}": "Revoke {{ appName }}", "Revoking": "Revoking", "Are you sure you want to revoke access?": "Are you sure you want to revoke access?", "Delete app": "Delete app", "Revision options": "گزینه‌های بازنگری‌ها", "Share link revoked": "پیوند اشتراک‌گذاری حذف شد", "Share link copied": "پیوند اشتراک‌گذاری کپی شد", "Share options": "گزینه‌های اشتراک‌گذاری", "Go to document": "رفتن به سند", "Revoke link": "<PERSON>ذ<PERSON> پیوند", "Contents": "محتوا", "Headings you add to the document will appear here": "عناوینی که به سند اضافه می‌کنید اینجا نمایش داده می‌شوند", "Table of contents": "فهرست مطالب", "Change name": "Change name", "Change email": "Change email", "Suspend user": "Suspend user", "An error occurred while sending the invite": "An error occurred while sending the invite", "User options": "گزینه‌های کاربر", "Change role": "Change role", "Resend invite": "Resend invite", "Revoke invite": "حذف دعوت", "Activate user": "Activate user", "template": "template", "document": "document", "published": "published", "edited": "edited", "created the collection": "created the collection", "mentioned you in": "mentioned you in", "left a comment on": "left a comment on", "resolved a comment on": "resolved a comment on", "shared": "shared", "invited you to": "invited you to", "Choose a date": "Choose a date", "API key created. Please copy the value now as it will not be shown again.": "API key created. Please copy the value now as it will not be shown again.", "Scopes": "<PERSON><PERSON><PERSON>", "Space-separated scopes restrict the access of this API key to specific parts of the API. Leave blank for full access": "Space-separated scopes restrict the access of this API key to specific parts of the API. Leave blank for full access", "Expiration": "Expiration", "Never expires": "Never expires", "7 days": "7 days", "30 days": "30 days", "60 days": "60 days", "90 days": "90 days", "Custom": "Custom", "No expiration": "No expiration", "The document archive is empty at the moment.": "بایگانی اسناد در حال حاضر خالی است.", "Collection menu": "Collection menu", "Drop documents to import": "اسناد را بکشید تا وارد شوند", "<em>{{ collectionName }}</em> doesn’t contain any\n                    documents yet.": "<em>{{ collectionName }}</em> هنوز هیچ سندی ندارد.", "{{ usersCount }} users and {{ groupsCount }} groups with access": "{{ usersCount }} user and {{ groupsCount }} groups with access", "{{ usersCount }} users and {{ groupsCount }} groups with access_plural": "{{ usersCount }} users and {{ groupsCount }} groups with access", "{{ usersCount }} users and a group have access": "{{ usersCount }} user and a group have access", "{{ usersCount }} users and a group have access_plural": "{{ usersCount }} users and a group have access", "{{ usersCount }} users with access": "{{ usersCount }} user with access", "{{ usersCount }} users with access_plural": "{{ usersCount }} users with access", "{{ groupsCount }} groups with access": "{{ groupsCount }} group with access", "{{ groupsCount }} groups with access_plural": "{{ groupsCount }} groups with access", "Archived by {{userName}}": "بایگانی شده توسط {{userName}}", "Sorry, an error occurred saving the collection": "متاسفانه خطایی در ذخیره‌سازی مجموعه رخ داد", "Add a description": "توضیحاتی اضافه کنید", "Share": "اشتراک‌گذاری", "Overview": "Overview", "Recently updated": "آخرین به‌روزرسانی", "Recently published": "آخرین انتشار", "Least recently updated": "اولین به‌روزرسانی", "A–Z": "الفبایی", "Signing in": "Signing in", "You can safely close this window once the Outline desktop app has opened": "You can safely close this window once the Outline desktop app has opened", "Error creating comment": "Error creating comment", "Add a comment": "Add a comment", "Add a reply": "Add a reply", "Reply": "Reply", "Post": "Post", "Upload image": "Upload image", "No resolved comments": "No resolved comments", "No comments yet": "No comments yet", "New comments": "New comments", "Most recent": "Most recent", "Order in doc": "Order in doc", "Resolved": "Resolved", "Sort comments": "Sort comments", "Show {{ count }} reply": "Show {{ count }} reply", "Show {{ count }} reply_plural": "Show {{ count }} replies", "Error updating comment": "Error updating comment", "Document is too large": "Document is too large", "This document has reached the maximum size and can no longer be edited": "This document has reached the maximum size and can no longer be edited", "Authentication failed": "تا<PERSON>ید هویت موفق نبود", "Please try logging out and back in again": "Please try logging out and back in again", "Authorization failed": "مجوز دسترسی رد شد", "You may have lost access to this document, try reloading": "You may have lost access to this document, try reloading", "Too many users connected to document": "Too many users connected to document", "Your edits will sync once other users leave the document": "Your edits will sync once other users leave the document", "Server connection lost": "اتصال سرور قطع شد", "Edits you make will sync once you’re online": "ویرایش هایی که انجام می دهید پس از آنلاین بودن همگام سازی می شوند", "Document restored": "سند بازیابی شد", "Images are still uploading.\nAre you sure you want to discard them?": "تصاویر هنوز در حال بارگذاری هستند.\nآیا مطمئن هستید که می خواهید آنها را نادیده بگیرید؟", "{{ count }} comment": "{{ count }} comment", "{{ count }} comment_plural": "{{ count }} comments", "Viewed by": "مشاهده شده توسط", "only you": "فقط شما", "person": "نفر", "people": "نفر", "Last updated": "Last updated", "Type '/' to insert, or start writing…": "Type '/' to insert, or start writing…", "Hide contents": "پنهان‌سازی محتوا", "Show contents": "نمایش محتوا", "available when headings are added": "available when headings are added", "Edit {{noun}}": "ویرایش {{noun}}", "Switch to dark": "تغییر به تیره", "Switch to light": "تغییر به روشن", "Archived": "بایگانی شده", "Save draft": "Save draft", "Done editing": "Done editing", "Restore version": "بازیابی نسخه", "No history yet": "No history yet", "Source": "Source", "Imported from {{ source }}": "Imported from {{ source }}", "Stats": "Stats", "{{ count }} minute read": "{{ count }} minute read", "{{ count }} minute read_plural": "{{ count }} minute read", "{{ count }} words": "{{ count }} word", "{{ count }} words_plural": "{{ count }} words", "{{ count }} characters": "{{ count }} character", "{{ count }} characters_plural": "{{ count }} characters", "{{ number }} emoji": "{{ number }} emoji", "No text selected": "No text selected", "{{ count }} words selected": "{{ count }} word selected", "{{ count }} words selected_plural": "{{ count }} words selected", "{{ count }} characters selected": "{{ count }} character selected", "{{ count }} characters selected_plural": "{{ count }} characters selected", "Contributors": "Contributors", "Created": "Created", "Creator": "Creator", "Last edited": "Last edited", "Previously edited": "Previously edited", "No one else has viewed yet": "No one else has viewed yet", "Viewed {{ count }} times by {{ teamMembers }} people": "Viewed {{ count }} time by {{ teamMembers }} people", "Viewed {{ count }} times by {{ teamMembers }} people_plural": "Viewed {{ count }} times by {{ teamMembers }} people", "Viewer insights are disabled.": "Viewer insights are disabled.", "Sorry, the last change could not be persisted – please reload the page": "Sorry, the last change could not be persisted – please reload the page", "{{ count }} days": "{{ count }} day", "{{ count }} days_plural": "{{ count }} days", "This template will be permanently deleted in <2></2> unless restored.": "این الگو برای همیشه در <2></2> حذف خواهد شد، مگر اینکه بازیابی شود.", "This document will be permanently deleted in <2></2> unless restored.": "این سند برای همیشه در <2></2> حذف خواهد شد، مگر اینکه بازیابی شود.", "Highlight some text and use the <1></1> control to add placeholders that can be filled out when creating new documents": "Highlight some text and use the <1></1> control to add placeholders that can be filled out when creating new documents", "You’re editing a template": "You’re editing a template", "Deleted by {{userName}}": "حذف شده توسط {{userName}}", "Observing {{ userName }}": "مشاهده {{ userName }}", "Backlinks": "Backlinks", "Close": "Close", "This document is large which may affect performance": "This document is large which may affect performance", "{{ teamName }} is using {{ appName }} to share documents, please login to continue.": "{{ teamName }} is using {{ appName }} to share documents, please login to continue.", "Are you sure you want to delete the <em>{{ documentTitle }}</em> template?": "آیا مطئمن هستید که قالب <em>{{ documentTitle }}</em> را حذف نمایید؟", "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history</em>.": "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history</em>.", "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history and <em>{{ any }} nested document</em>.": "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history and <em>{{ any }} nested document</em>.", "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history and <em>{{ any }} nested document</em>._plural": "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history and <em>{{ any }} nested documents</em>.", "If you’d like the option of referencing or restoring the {{noun}} in the future, consider archiving it instead.": "اگر می‌خواهید امکان بازیابی یا ارجاع به {{noun}} را در آینده داشته باشید، بهتر است آن را بایگانی نمایید.", "Select a location to move": "Select a location to move", "Document moved": "سند منتقل شد", "Couldn’t move the document, try again?": "Couldn’t move the document, try again?", "Move to <em>{{ location }}</em>": "Move to <em>{{ location }}</em>", "Couldn’t create the document, try again?": "امکان ایجاد سند وجود نداشت، دوباره تلاش شود؟", "Document permanently deleted": "سند برای همیشه حذف شد", "Are you sure you want to permanently delete the <em>{{ documentTitle }}</em> document? This action is immediate and cannot be undone.": "آیا مطمئن هستید که سند <em>{{ documentTitle }}</em> را حذف نمایید؟ این اقدام در لحظه انجام می‌شود و قابل واگرد نیست.", "Select a location to publish": "Select a location to publish", "Document published": "انتشار سند", "Couldn’t publish the document, try again?": "Couldn’t publish the document, try again?", "Publish in <em>{{ location }}</em>": "Publish in <em>{{ location }}</em>", "Search documents": "جستجوی اسناد", "No documents found for your filters.": "سندی با فیلترهای شما پیدا نشد.", "You’ve not got any drafts at the moment.": "در حال حاضر پیش‌نویسی ندارید.", "Payment Required": "Payment Required", "No access to this doc": "No access to this doc", "It doesn’t look like you have permission to access this document.": "It doesn’t look like you have permission to access this document.", "Please request access from the document owner.": "Please request access from the document owner.", "Not found": "Not found", "The page you’re looking for cannot be found. It might have been deleted or the link is incorrect.": "The page you’re looking for cannot be found. It might have been deleted or the link is incorrect.", "Offline": "آف<PERSON><PERSON><PERSON>ن", "We were unable to load the document while offline.": "امکان بارگیری سند در حالت آفلاین وجود نداشت.", "Your account has been suspended": "حساب شما معلق شده است", "Warning Sign": "Warning Sign", "A workspace admin (<em>{{ suspendedContactEmail }}</em>) has suspended your account. To re-activate your account, please reach out to them directly.": "A workspace admin (<em>{{ suspendedContactEmail }}</em>) has suspended your account. To re-activate your account, please reach out to them directly.", "Created by me": "ایجاد شده توسط من", "Weird, this shouldn’t ever be empty": "عجیب ، این هرگز نباید خالی باشد", "You haven’t created any documents yet": "شما هنوز هیچ سندی ایجاد نکرده اید", "Documents you’ve recently viewed will be here for easy access": "اسنادی که اخیراً مشاهده کرده اید برای دسترسی آسان در اینجا خواهند بود", "We sent out your invites!": "دعوت‌نامه‌های شما ارسال شد!", "Those email addresses are already invited": "Those email addresses are already invited", "Sorry, you can only send {{MAX_INVITES}} invites at a time": "متأسفانه شما تنها می‌توانید {{MAX_INVITES}} دعوت‌نامه به صورت همزمان ارسال نمایید", "Invited {{roleName}} will receive access to": "Invited {{roleName}} will receive access to", "{{collectionCount}} collections": "{{collectionCount}} collections", "Admin": "مدیر", "Can manage all workspace settings": "Can manage all workspace settings", "Can create, edit, and delete documents": "Can create, edit, and delete documents", "Can view and comment": "Can view and comment", "Invite people to join your workspace. They can sign in with {{signinMethods}} or use their email address.": "Invite people to join your workspace. They can sign in with {{signinMethods}} or use their email address.", "Invite members to join your workspace. They will need to sign in with {{signinMethods}}.": "Invite members to join your workspace. They will need to sign in with {{signinMethods}}.", "As an admin you can also <2>enable email sign-in</2>.": "به عنوان مدیر همچنین می‌توانید <2>ورود به سیستم با ایمیل را فعال نمایید</2>.", "Invite as": "Invite as", "Role": "نقش", "Email": "ایمیل", "Add another": "افزودن یک مورد دیگر", "Inviting": "در حال دعوت", "Send Invites": "ارسال دعوت‌نامه‌ها", "Open command menu": "Open command menu", "Forward": "Forward", "Edit current document": "ویرایش سند فعلی", "Move current document": "انتقال سند فعلی", "Open document history": "باز کردن گذشته‌ی سند", "Jump to search": "پرش به جستجو", "Jump to home": "پرش به خانه", "Focus search input": "انتخاب ورودی جستجو", "Open this guide": "باز کردن این راهنما", "Enter": "Enter", "Publish document and exit": "انتشار سند و خروج", "Save document": "ذ<PERSON><PERSON><PERSON>ه سند", "Cancel editing": "لغو ویرایش", "Collaboration": "Collaboration", "Formatting": "قالب‌بندی", "Paragraph": "پاراگراف", "Large header": "عنوان بزرگ", "Medium header": "عنوان متوسط", "Small header": "عنوان کوچک", "Underline": "<PERSON><PERSON> زیر", "Undo": "واگرد", "Redo": "از نو", "Move block up": "Move block up", "Move block down": "Move block down", "Lists": "فهرست‌ها", "Toggle task list item": "Toggle task list item", "Tab": "Tab", "Indent list item": "توبردن مورد فهرست", "Outdent list item": "بیرون آوردن مورد فهرست", "Move list item up": "انتقال مورد فهرست به بالا", "Move list item down": "پایین آوردن مورد", "Tables": "Tables", "Insert row": "Insert row", "Next cell": "Next cell", "Previous cell": "Previous cell", "Space": "Space", "Numbered list": "فهرست شماره‌دار", "Blockquote": "نقل قول", "Horizontal divider": "جداکننده افقی", "LaTeX block": "LaTeX block", "Inline code": "کد درون خطی", "Inline LaTeX": "Inline LaTeX", "Triggers": "Triggers", "Mention users and more": "Mention users and more", "Emoji": "<PERSON><PERSON><PERSON>", "Insert block": "Insert block", "Sign In": "ورود", "Continue with Email": "ادامه با ایمیل", "Continue with {{ authProviderName }}": "با {{ authProviderName }} ادامه دهید", "Back to home": "بازگشت به خانه", "The workspace could not be found": "The workspace could not be found", "To continue, enter your workspace’s subdomain.": "To continue, enter your workspace’s subdomain.", "subdomain": "subdomain", "Continue": "ادامه", "The domain associated with your email address has not been allowed for this workspace.": "The domain associated with your email address has not been allowed for this workspace.", "Unable to sign-in. Please navigate to your workspace's custom URL, then try to sign-in again.<1></1>If you were invited to a workspace, you will find a link to it in the invite email.": "Unable to sign-in. Please navigate to your workspace's custom URL, then try to sign-in again.<1></1>If you were invited to a workspace, you will find a link to it in the invite email.", "Sorry, a new account cannot be created with a personal Gmail address.<1></1>Please use a Google Workspaces account instead.": "Sorry, a new account cannot be created with a personal Gmail address.<1></1>Please use a Google Workspaces account instead.", "The workspace associated with your user is scheduled for deletion and cannot be accessed at this time.": "The workspace associated with your user is scheduled for deletion and cannot be accessed at this time.", "The workspace you authenticated with is not authorized on this installation. Try another?": "The workspace you authenticated with is not authorized on this installation. Try another?", "We could not read the user info supplied by your identity provider.": "We could not read the user info supplied by your identity provider.", "Your account uses email sign-in, please sign-in with email to continue.": "Your account uses email sign-in, please sign-in with email to continue.", "An email sign-in link was recently sent, please check your inbox or try again in a few minutes.": "An email sign-in link was recently sent, please check your inbox or try again in a few minutes.", "Authentication failed – we were unable to sign you in at this time. Please try again.": "Authentication failed – we were unable to sign you in at this time. Please try again.", "Authentication failed – you do not have permission to access this workspace.": "Authentication failed – you do not have permission to access this workspace.", "Sorry, it looks like that sign-in link is no longer valid, please try requesting another.": "Sorry, it looks like that sign-in link is no longer valid, please try requesting another.", "Your account has been suspended. To re-activate your account, please contact a workspace admin.": "Your account has been suspended. To re-activate your account, please contact a workspace admin.", "This workspace has been suspended. Please contact support to restore access.": "This workspace has been suspended. Please contact support to restore access.", "Authentication failed – this login method was disabled by a workspace admin.": "Authentication failed – this login method was disabled by a workspace admin.", "The workspace you are trying to join requires an invite before you can create an account.<1></1>Please request an invite from your workspace admin and try again.": "The workspace you are trying to join requires an invite before you can create an account.<1></1>Please request an invite from your workspace admin and try again.", "Sorry, an unknown error occurred.": "Sorry, an unknown error occurred.", "Choose a workspace": "Choose a workspace", "Choose an {{ appName }} workspace or login to continue connecting this app": "Choose an {{ appName }} workspace or login to continue connecting this app", "Create workspace": "Create workspace", "Setup your workspace by providing a name and details for admin login. You can change these later.": "Setup your workspace by providing a name and details for admin login. You can change these later.", "Workspace name": "Workspace name", "Admin name": "Admin name", "Admin email": "Admin email", "Login": "ورود", "Error": "Error", "Failed to load configuration.": "پیکربندی بارگیری نشد.", "Check the network requests and server logs for full details of the error.": "Check the network requests and server logs for full details of the error.", "Custom domain setup": "Custom domain setup", "Almost there": "Almost there", "Your custom domain is successfully pointing at Outline. To complete the setup process please contact support.": "Your custom domain is successfully pointing at Outline. To complete the setup process please contact support.", "Choose workspace": "Choose workspace", "This login method requires choosing your workspace to continue": "This login method requires choosing your workspace to continue", "Check your email": "ایمی<PERSON> خود را بررسی کنید", "A magic sign-in link has been sent to the email <em>{{ emailLinkSentTo }}</em> if an account exists.": "A magic sign-in link has been sent to the email <em>{{ emailLinkSentTo }}</em> if an account exists.", "Back to login": "بازگشت به صفحه ورود", "Get started by choosing a sign-in method for your new workspace below…": "Get started by choosing a sign-in method for your new workspace below…", "Login to {{ authProviderName }}": "با {{ authProviderName }} وارد شوید", "You signed in with {{ authProviderName }} last time.": "شما آخرین بار با {{ authProviderName }} وارد شدید.", "Or": "یا", "Already have an account? Go to <1>login</1>.": "حساب کاربری دارید؟ به بخش <1>ورود</1> بروید.", "An error occurred": "An error occurred", "The OAuth client could not be found, please check the provided client ID": "The OAuth client could not be found, please check the provided client ID", "The OAuth client could not be loaded, please check the redirect URI is valid": "The OAuth client could not be loaded, please check the redirect URI is valid", "Required OAuth parameters are missing": "Required OAuth parameters are missing", "Authorize": "Authorize", "{{ appName }} wants to access {{ teamName }}": "{{ appName }} wants to access {{ teamName }}", "By <em>{{ developerName }}</em>": "By <em>{{ developerName }}</em>", "{{ appName }} will be able to access your account and perform the following actions": "{{ appName }} will be able to access your account and perform the following actions", "read": "read", "write": "write", "read and write": "read and write", "API keys": "API keys", "attachments": "attachments", "collections": "collections", "comments": "comments", "documents": "documents", "events": "events", "groups": "groups", "integrations": "integrations", "notifications": "notifications", "reactions": "reactions", "pins": "pins", "shares": "shares", "users": "users", "teams": "teams", "workspace": "workspace", "Read all data": "Read all data", "Write all data": "Write all data", "Any collection": "هر مجموعه‌ای", "All time": "All time", "Past day": "روز گذشته", "Past week": "هفته گذشته", "Past month": "ماه گذشته", "Past year": "سال گذشته", "Any time": "ه<PERSON> زمانی", "Remove document filter": "Remove document filter", "Any status": "Any status", "Remove search": "جستجو را حذف کن", "Any author": "هر نگارنده", "Search titles only": "Search titles only", "Something went wrong": "Something went wrong", "Please try again or contact support if the problem persists": "Please try again or contact support if the problem persists", "No documents found for your search filters.": "سندی برای فیلترهای جستجوی شما پیدا نشد.", "Create personal API keys to authenticate with the API and programatically control\n      your workspace's data. For more details see the <em>developer documentation</em>.": "Create personal API keys to authenticate with the API and programatically control\n      your workspace's data. For more details see the <em>developer documentation</em>.", "API keys have been disabled by an admin for your account": "API keys have been disabled by an admin for your account", "Application access": "Application access", "Manage which third-party and internal applications have been granted access to your {{ appName }} account.": "Manage which third-party and internal applications have been granted access to your {{ appName }} account.", "API": "API", "API keys can be used to authenticate with the API and programatically control\n          your workspace's data. For more details see the <em>developer documentation</em>.": "API keys can be used to authenticate with the API and programatically control\n          your workspace's data. For more details see the <em>developer documentation</em>.", "Application published": "Application published", "Application updated": "Application updated", "Client secret rotated": "Client secret rotated", "Rotate secret": "Rotate secret", "Rotating the client secret will invalidate the current secret. Make sure to update any applications using these credentials.": "Rotating the client secret will invalidate the current secret. Make sure to update any applications using these credentials.", "Displayed to users when authorizing": "Displayed to users when authorizing", "Developer information shown to users when authorizing": "Developer information shown to users when authorizing", "Developer name": "Developer name", "Developer URL": "Developer URL", "Allow users from other workspaces to authorize this app": "Allow users from other workspaces to authorize this app", "Credentials": "Credentials", "OAuth client ID": "OAuth client ID", "The public identifier for this app": "The public identifier for this app", "OAuth client secret": "OAuth client secret", "Store this value securely, do not expose it publicly": "Store this value securely, do not expose it publicly", "Where users are redirected after authorizing this app": "Where users are redirected after authorizing this app", "Authorization URL": "Authorization URL", "Where users are redirected to authorize this app": "Where users are redirected to authorize this app", "Applications allow you to build internal or public integrations with Outline and provide secure access via OAuth. For more details see the <em>developer documentation</em>.": "Applications allow you to build internal or public integrations with Outline and provide secure access via OAuth. For more details see the <em>developer documentation</em>.", "by {{ name }}": "by {{ name }}", "Last used": "Last used", "No expiry": "No expiry", "Restricted scope": "Restricted scope", "API key copied to clipboard": "API key copied to clipboard", "Copied": "<PERSON>pied", "Are you sure you want to revoke the {{ tokenName }} token?": "Are you sure you want to revoke the {{ tokenName }} token?", "Disconnect integration": "Disconnect integration", "Connected": "Connected", "Disconnect": "قطع اتصال", "Disconnecting": "Disconnecting", "Allowed domains": "Allowed domains", "The domains which should be allowed to create new accounts using SSO. Changing this setting does not affect existing user accounts.": "The domains which should be allowed to create new accounts using SSO. Changing this setting does not affect existing user accounts.", "Remove domain": "Remove domain", "Add a domain": "Add a domain", "Save changes": "Save changes", "Please choose a single file to import": "Please choose a single file to import", "Your import is being processed, you can safely leave this page": "وارد کردن شما در حال پردازش است، می توانید با خیال راحت این صفحه را ترک کنید", "File not supported – please upload a valid ZIP file": "File not supported – please upload a valid ZIP file", "Set the default permission level for collections created from the import": "Set the default permission level for collections created from the import", "Uploading": "در حال بارگذاری", "Start import": "Start import", "Processing": "در حال پردازش", "Expired": "منقضی شده", "Completed": "Completed", "Failed": "ناموفق", "All collections": "همه مجموعه ها", "Import deleted": "Import deleted", "Export deleted": "خروجی حذف شد", "Are you sure you want to delete this import?": "Are you sure you want to delete this import?", "Deleting this import will also delete all collections and documents that were created from it. This cannot be undone.": "Deleting this import will also delete all collections and documents that were created from it. This cannot be undone.", "Check server logs for more details.": "Check server logs for more details.", "{{userName}} requested": "{{userName}} درخواست شد", "Groups are for organizing your team. They work best when centered around a function or a responsibility — Support or Engineering for example.": "گروه‌ها برای سازمان‌دهی تیم شما هستند. بهترین حالت ایجاد گروه‌ها برای هر کارکرد یا مسئولیت است - برای نمونه «پشتیبانی» یا «مهندسی».", "You’ll be able to add people to the group next.": "در ادامه می‌توانید افراد را به گروه اضافه کنید.", "You can edit the name of this group at any time, however doing so too often might confuse your team mates.": "همیشه می‌توانید نام گروه را ویرایش نمایید، ولی این کار ممکن است باعث ابهام هم‌تیمی‌هایتان شود.", "Are you sure about that? Deleting the <em>{{groupName}}</em> group will cause its members to lose access to collections and documents that it is associated with.": "آیا از این اقدام مطمئن هستید؟ حذف گروه <em>{{groupName}}</em> باعث می‌شود اعضای آن دسترسی خود به مجموعه‌ها و سندهای گروه را از دست بدهند.", "Add people to {{groupName}}": "افزودن افراد به {{groupName}}", "{{userName}} was removed from the group": "{{userName}} از گروه حذف شد", "Add and remove members to the <em>{{groupName}}</em> group. Members of the group will have access to any collections this group has been added to.": "Add and remove members to the <em>{{groupName}}</em> group. Members of the group will have access to any collections this group has been added to.", "Add people": "افزودن افراد", "Listing members of the <em>{{groupName}}</em> group.": "Listing members of the <em>{{groupName}}</em> group.", "This group has no members.": "این گروه عضوی ندارد.", "{{userName}} was added to the group": "{{userName}} به گروه اضافه شد", "Could not add user": "امکان افزودن کاربر وجود نداشت", "Add members below to give them access to the group. Need to add someone who’s not yet a member?": "Add members below to give them access to the group. Need to add someone who’s not yet a member?", "Invite them to {{teamName}}": "دعوت افراد به {{teamName}}", "Ask an admin to invite them first": "Ask an admin to invite them first", "Search by name": "جستجو با نام", "Search people": "جستجوی افراد", "No people matching your search": "فردی با جستجوی شما مطابقت ندارد", "No people left to add": "فردی برای افزودن باقی نمانده است", "Date created": "Date created", "Crop Image": "Crop Image", "Crop image": "Crop image", "How does this work?": "How does this work?", "You can import a zip file that was previously exported from the JSON option in another instance. In {{ appName }}, open <em>Export</em> in the Settings sidebar and click on <em>Export Data</em>.": "You can import a zip file that was previously exported from the JSON option in another instance. In {{ appName }}, open <em>Export</em> in the Settings sidebar and click on <em>Export Data</em>.", "Drag and drop the zip file from the JSON export option in {{appName}}, or click to upload": "Drag and drop the zip file from the JSON export option in {{appName}}, or click to upload", "Canceled": "Canceled", "Import canceled": "Import canceled", "Are you sure you want to cancel this import?": "Are you sure you want to cancel this import?", "Canceling": "Canceling", "Canceling this import will discard any progress made. This cannot be undone.": "Canceling this import will discard any progress made. This cannot be undone.", "{{ count }} document imported": "{{ count }} document imported", "{{ count }} document imported_plural": "{{ count }} documents imported", "You can import a zip file that was previously exported from an Outline installation – collections, documents, and images will be imported. In Outline, open <em>Export</em> in the Settings sidebar and click on <em>Export Data</em>.": "You can import a zip file that was previously exported from an Outline installation – collections, documents, and images will be imported. In Outline, open <em>Export</em> in the Settings sidebar and click on <em>Export Data</em>.", "Drag and drop the zip file from the Markdown export option in {{appName}}, or click to upload": "Drag and drop the zip file from the Markdown export option in {{appName}}, or click to upload", "Configure": "Configure", "Connect": "اتصال", "Last active": "آخرین فعالیت", "Guest": "Guest", "Never used": "Never used", "Are you sure you want to delete the {{ appName }} application? This cannot be undone.": "Are you sure you want to delete the {{ appName }} application? This cannot be undone.", "Shared by": "Shared by", "Date shared": "Date shared", "Last accessed": "آخرین دسترسی", "Domain": "Domain", "Views": "Views", "All roles": "All roles", "Admins": "مدی<PERSON>ان", "Editors": "Editors", "All status": "All status", "Active": "فعال", "Left": "Left", "Right": "Right", "Settings saved": "تنظیمات ذخیره شد", "Logo updated": "لوگو به روز شد", "Unable to upload new logo": "امکان بارگذاری لوگوی جدید وجود نداشت", "Delete workspace": "Delete workspace", "These settings affect the way that your workspace appears to everyone on the team.": "These settings affect the way that your workspace appears to everyone on the team.", "Display": "Display", "The logo is displayed at the top left of the application.": "The logo is displayed at the top left of the application.", "The workspace name, usually the same as your company name.": "The workspace name, usually the same as your company name.", "Description": "Description", "A short description of your workspace.": "A short description of your workspace.", "Theme": "Theme", "Customize the interface look and feel.": "Customize the interface look and feel.", "Reset theme": "Reset theme", "Accent color": "Accent color", "Accent text color": "Accent text color", "Public branding": "Public branding", "Show your workspace logo, description, and branding on publicly shared pages.": "Show your workspace logo, description, and branding on publicly shared pages.", "Table of contents position": "Table of contents position", "The side to display the table of contents in relation to the main content.": "The side to display the table of contents in relation to the main content.", "Behavior": "Behavior", "Subdomain": "زیردامنه", "Your workspace will be accessible at": "Your workspace will be accessible at", "Choose a subdomain to enable a login page just for your team.": "Choose a subdomain to enable a login page just for your team.", "This is the screen that workspace members will first see when they sign in.": "This is the screen that workspace members will first see when they sign in.", "Danger": "Danger", "You can delete this entire workspace including collections, documents, and users.": "You can delete this entire workspace including collections, documents, and users.", "Export data": "Export data", "A full export might take some time, consider exporting a single document or collection. You may leave this page once the export has started – if you have notifications enabled, we will email a link to <em>{{ userEmail }}</em> when it’s complete.": "A full export might take some time, consider exporting a single document or collection. You may leave this page once the export has started – if you have notifications enabled, we will email a link to <em>{{ userEmail }}</em> when it’s complete.", "Recent exports": "صادر شده های اخیر", "Manage optional and beta features. Changing these settings will affect the experience for all members of the workspace.": "Manage optional and beta features. Changing these settings will affect the experience for all members of the workspace.", "Separate editing": "Separate editing", "When enabled documents have a separate editing mode by default instead of being always editable. This setting can be overridden by user preferences.": "When enabled documents have a separate editing mode by default instead of being always editable. This setting can be overridden by user preferences.", "When enabled team members can add comments to documents.": "When enabled team members can add comments to documents.", "Create a group": "ایجاد گروه", "Could not load groups": "Could not load groups", "New group": "گروه جدید", "Groups can be used to organize and manage the people on your team.": "از گروه‌ها برای دسته‌بندی و مدیریت افراد تیم خود استفاده نمایید.", "No groups have been created yet": "تاکنون گروهی ایجاد نشده است", "Import a zip file of Markdown documents (exported from version 0.67.0 or earlier)": "Import a zip file of Markdown documents (exported from version 0.67.0 or earlier)", "Import data": "Import data", "Import a JSON data file exported from another {{ appName }} instance": "Import a JSON data file exported from another {{ appName }} instance", "Import pages from a Confluence instance": "وارد کردن صفحات از یک نمونه Confluence", "Enterprise": "Enterprise", "Quickly transfer your existing documents, pages, and files from other tools and services into {{appName}}. You can also drag and drop any HTML, Markdown, and text documents directly into Collections in the app.": "Quickly transfer your existing documents, pages, and files from other tools and services into {{appName}}. You can also drag and drop any HTML, Markdown, and text documents directly into Collections in the app.", "Recent imports": "وارد شده‌های اخیر", "Configure a variety of integrations with third-party services.": "Configure a variety of integrations with third-party services.", "Could not load members": "Could not load members", "Everyone that has signed into {{appName}} is listed here. It’s possible that there are other users who have access through {{signinMethods}} but haven’t signed in yet.": "Everyone that has signed into {{appName}} is listed here. It’s possible that there are other users who have access through {{signinMethods}} but haven’t signed in yet.", "Receive a notification whenever a new document is published": "هر زمان سند جدیدی منتشر شد، اعلانی دریافت کنید", "Document updated": "به‌روزرسانی سند", "Receive a notification when a document you are subscribed to is edited": "Receive a notification when a document you are subscribed to is edited", "Comment posted": "Comment posted", "Receive a notification when a document you are subscribed to or a thread you participated in receives a comment": "Receive a notification when a document you are subscribed to or a thread you participated in receives a comment", "Mentioned": "Mentioned", "Receive a notification when someone mentions you in a document or comment": "Receive a notification when someone mentions you in a document or comment", "Receive a notification when a comment thread you were involved in is resolved": "Receive a notification when a comment thread you were involved in is resolved", "Collection created": "ایجاد مجموعه", "Receive a notification whenever a new collection is created": "هر زمان مجموعه جدیدی ایجاد شد، اعلانی دریافت کنید", "Invite accepted": "<PERSON><PERSON><PERSON> accepted", "Receive a notification when someone you invited creates an account": "Receive a notification when someone you invited creates an account", "Invited to document": "Invited to document", "Receive a notification when a document is shared with you": "Receive a notification when a document is shared with you", "Invited to collection": "Invited to collection", "Receive a notification when you are given access to a collection": "Receive a notification when you are given access to a collection", "Export completed": "Export completed", "Receive a notification when an export you requested has been completed": "Receive a notification when an export you requested has been completed", "Getting started": "راهنمای شروع به کار", "Tips on getting started with features and functionality": "Tips on getting started with features and functionality", "New features": "ویژگی‌های جدید", "Receive an email when new features of note are added": "هر زمان ویژگی قابل توجهی جدیدی معرفی می‌شود، اعلانی دریافت کنید", "Notifications saved": "اعلان‌ها ذخیره شد", "Unsubscription successful. Your notification settings were updated": "لغو اشتراک موفقیت‌آمیز بود. تنظیمات اعلان شما به روز شد", "Manage when and where you receive email notifications.": "Manage when and where you receive email notifications.", "The email integration is currently disabled. Please set the associated environment variables and restart the server to enable notifications.": "ادغام ایمیلی غیرفعال است. لطفاً متغیرهای محیطی مربوطه را تنظیم کرده و سرور را دوباره راه‌اندازی کنید تا اعلان‌ها را فعال کنید.", "Preferences saved": "Preferences saved", "Delete account": "<PERSON><PERSON><PERSON> حساب", "Manage settings that affect your personal experience.": "Manage settings that affect your personal experience.", "Language": "زبان", "Choose the interface language. Community translations are accepted though our <2>translation portal</2>.": "Choose the interface language. Community translations are accepted though our <2>translation portal</2>.", "Choose your preferred interface color scheme.": "Choose your preferred interface color scheme.", "Use pointer cursor": "Use pointer cursor", "Show a hand cursor when hovering over interactive elements.": "Show a hand cursor when hovering over interactive elements.", "Show line numbers": "Show line numbers", "Show line numbers on code blocks in documents.": "Show line numbers on code blocks in documents.", "When enabled, documents have a separate editing mode. When disabled, documents are always editable when you have permission.": "When enabled, documents have a separate editing mode. When disabled, documents are always editable when you have permission.", "Remember previous location": "Remember previous location", "Automatically return to the document you were last viewing when the app is re-opened.": "Automatically return to the document you were last viewing when the app is re-opened.", "Smart text replacements": "Smart text replacements", "Auto-format text by replacing shortcuts with symbols, dashes, smart quotes, and other typographical elements.": "Auto-format text by replacing shortcuts with symbols, dashes, smart quotes, and other typographical elements.", "You may delete your account at any time, note that this is unrecoverable": "همیشه می‌توانید حساب‌تان را حذف کنید، ولی توجه داشته باشید که این عملیات قابل بازگشت نیست", "Profile saved": "پروفایل ذخیره شد", "Profile picture updated": "تصویر پروفایل به‌روز شد", "Unable to upload new profile picture": "امکان بارگذاری تصویر جدید پروفایل وجود نداشت", "Manage how you appear to other members of the workspace.": "Manage how you appear to other members of the workspace.", "Photo": "<PERSON><PERSON><PERSON>", "Choose a photo or image to represent yourself.": "Choose a photo or image to represent yourself.", "This could be your real name, or a nickname — however you’d like people to refer to you.": "This could be your real name, or a nickname — however you’d like people to refer to you.", "Email address": "آدرس ایمیل", "Are you sure you want to require invites?": "Are you sure you want to require invites?", "New users will first need to be invited to create an account. <em>Default role</em> and <em>Allowed domains</em> will no longer apply.": "New users will first need to be invited to create an account. <em>Default role</em> and <em>Allowed domains</em> will no longer apply.", "Settings that impact the access, security, and content of your workspace.": "Settings that impact the access, security, and content of your workspace.", "Allow members to sign-in with {{ authProvider }}": "Allow members to sign-in with {{ authProvider }}", "Disabled": "Disabled", "Allow members to sign-in using their email address": "Allow members to sign-in using their email address", "The server must have SMTP configured to enable this setting": "برای استفاده از این تنظیمات، سرور باید تنظیمات SMTP را داشته باشد", "Access": "Access", "Allow users to send invites": "Allow users to send invites", "Allow editors to invite other people to the workspace": "Allow editors to invite other people to the workspace", "Require invites": "Require invites", "Require members to be invited to the workspace before they can create an account using SSO.": "Require members to be invited to the workspace before they can create an account using SSO.", "Default role": "نقش پیش‌فرض", "The default user role for new accounts. Changing this setting does not affect existing user accounts.": "The default user role for new accounts. Changing this setting does not affect existing user accounts.", "When enabled, documents can be shared publicly on the internet by any member of the workspace": "When enabled, documents can be shared publicly on the internet by any member of the workspace", "Viewer document exports": "Viewer document exports", "When enabled, viewers can see download options for documents": "When enabled, viewers can see download options for documents", "Users can delete account": "Users can delete account", "When enabled, users can delete their own account from the workspace": "When enabled, users can delete their own account from the workspace", "Rich service embeds": "جاسازی‌های غنی سرویس‌ها", "Links to supported services are shown as rich embeds within your documents": "پیوندها به سایت‌ها و سرویس‌های پشتیبانی شده به صورت جاسازی‌های غنی در سندها نمایش داده می‌شود", "Collection creation": "Collection creation", "Allow editors to create new collections within the workspace": "Allow editors to create new collections within the workspace", "Workspace creation": "Workspace creation", "Allow editors to create new workspaces": "Allow editors to create new workspaces", "Could not load shares": "Could not load shares", "Sharing is currently disabled.": "اشتراک‌گذاری در حال حاضر غیرفعال است.", "You can globally enable and disable public document sharing in the <em>security settings</em>.": "از <em>تنظیمات امنیتی</em> می‌توانید امکان اشتراک‌گذاری عمومی سندها را در سطح کل سیستم فعال و غیرفعال کنید.", "Documents that have been shared are listed below. Anyone that has the public link can access a read-only version of the document until the link has been revoked.": "سندهای اشتراک‌گذاری شده در اینجا فهرست شده‌اند. هر فردی با پیوند عمومی تا پیش از حذف پیوند می‌تواند به صورت فقط خواندنی به سند مربوطه دسترسی داشته باشد.", "You can create templates to help your team create consistent and accurate documentation.": "با ساخت قالب‌ها به تیم خود کمک می‌کنید که مستنداتی سازگار و درست ایجاد کنند.", "Alphabetical": "الفبایی", "There are no templates just yet.": "هنوز هیچ قالبی وجود ندارد.", "A confirmation code has been sent to your email address, please enter the code below to permanently destroy this workspace.": "A confirmation code has been sent to your email address, please enter the code below to permanently destroy this workspace.", "Confirmation code": "Confirmation code", "Deleting the <1>{{workspaceName}}</1> workspace will destroy all collections, documents, users, and associated data. You will be immediately logged out of {{appName}}.": "Deleting the <1>{{workspaceName}}</1> workspace will destroy all collections, documents, users, and associated data. You will be immediately logged out of {{appName}}.", "Please note that workspaces are completely separated. They can have a different domain, settings, users, and billing.": "Please note that workspaces are completely separated. They can have a different domain, settings, users, and billing.", "You are creating a new workspace using your current account — <em>{{email}}</em>": "You are creating a new workspace using your current account — <em>{{email}}</em>", "To create a workspace under another email please sign up from the homepage": "To create a workspace under another email please sign up from the homepage", "Trash emptied": "Trash emptied", "Are you sure you want to permanently delete all the documents in Trash? This action is immediate and cannot be undone.": "Are you sure you want to permanently delete all the documents in Trash? This action is immediate and cannot be undone.", "Recently deleted": "Recently deleted", "Trash is empty at the moment.": "سطل زباله در حال حاضر خالی است.", "A confirmation code has been sent to your email address, please enter the code below to permanently destroy your account.": "A confirmation code has been sent to your email address, please enter the code below to permanently destroy your account.", "Are you sure? Deleting your account will destroy identifying data associated with your user and cannot be undone. You will be immediately logged out of {{appName}} and all your API tokens will be revoked.": "Are you sure? Deleting your account will destroy identifying data associated with your user and cannot be undone. You will be immediately logged out of {{appName}} and all your API tokens will be revoked.", "Delete my account": "Delete my account", "Today": "امروز", "Yesterday": "دی<PERSON><PERSON><PERSON>", "Last week": "ه<PERSON><PERSON><PERSON> قبل", "This month": "این ماه", "Last month": "ماه قبل", "This year": "امسال", "Expired yesterday": "Expired yesterday", "Expired {{ date }}": "Expired {{ date }}", "Expires today": "Expires today", "Expires tomorrow": "Expires tomorrow", "Expires {{ date }}": "Expires {{ date }}", "Whoops, you need to accept the permissions in GitHub to connect {{appName}} to your workspace. Try again?": "Whoops, you need to accept the permissions in GitHub to connect {{appName}} to your workspace. Try again?", "Something went wrong while authenticating your request. Please try logging in again.": "Something went wrong while authenticating your request. Please try logging in again.", "The owner of GitHub account has been requested to install the {{githubAppName}} GitHub app. Once approved, previews will be shown for respective links.": "The owner of GitHub account has been requested to install the {{githubAppName}} GitHub app. Once approved, previews will be shown for respective links.", "Enable previews of GitHub issues and pull requests in documents by connecting a GitHub organization or specific repositories to {appName}.": "Enable previews of GitHub issues and pull requests in documents by connecting a GitHub organization or specific repositories to {appName}.", "Enabled by {{integrationCreatedBy}}": "Enabled by {{integrationCreated<PERSON>y}}", "Disconnecting will prevent previewing GitHub links from this organization in documents. Are you sure?": "Disconnecting will prevent previewing GitHub links from this organization in documents. Are you sure?", "The GitHub integration is currently disabled. Please set the associated environment variables and restart the server to enable the integration.": "The GitHub integration is currently disabled. Please set the associated environment variables and restart the server to enable the integration.", "Google Analytics": "Google Analytics", "Add a Google Analytics 4 measurement ID to send document views and analytics from the workspace to your own Google Analytics account.": "Add a Google Analytics 4 measurement ID to send document views and analytics from the workspace to your own Google Analytics account.", "Measurement ID": "Measurement ID", "Create a \"Web\" stream in your Google Analytics admin dashboard and copy the measurement ID from the generated code snippet to install.": "Create a \"Web\" stream in your Google Analytics admin dashboard and copy the measurement ID from the generated code snippet to install.", "Whoops, you need to accept the permissions in Linear to connect {{appName}} to your workspace. Try again?": "Whoops, you need to accept the permissions in Linear to connect {{appName}} to your workspace. Try again?", "Something went wrong while processing your request. Please try again.": "Something went wrong while processing your request. Please try again.", "Enable previews of Linear issues in documents by connecting a Linear workspace to {appName}.": "Enable previews of Linear issues in documents by connecting a Linear workspace to {appName}.", "Disconnecting will prevent previewing Linear links from this workspace in documents. Are you sure?": "Disconnecting will prevent previewing Linear links from this workspace in documents. Are you sure?", "The Linear integration is currently disabled. Please set the associated environment variables and restart the server to enable the integration.": "The Linear integration is currently disabled. Please set the associated environment variables and restart the server to enable the integration.", "Configure a Matomo installation to send views and analytics from the workspace to your own Matomo instance.": "Configure a Matomo installation to send views and analytics from the workspace to your own Matomo instance.", "Instance URL": "Instance URL", "The URL of your Matomo instance. If you are using Matomo Cloud it will end in matomo.cloud/": "The URL of your Matomo instance. If you are using Matomo Cloud it will end in matomo.cloud/", "Site ID": "Site ID", "An ID that uniquely identifies the website in your Matomo instance.": "An ID that uniquely identifies the website in your Matomo instance.", "Whoops, you need to accept the permissions in Notion to connect {{ appName }} to your workspace. Try again?": "Whoops, you need to accept the permissions in Notion to connect {{ appName }} to your workspace. Try again?", "Import pages from Notion": "Import pages from Notion", "Add to Slack": "افزودن به <PERSON>ck", "document published": "سند منتشر شد", "document updated": "سند به روز شد", "Posting to the <em>{{ channelName }}</em> channel on": "پست کردن در کانال <em>{{ channelName }}</em>", "These events should be posted to Slack": "این رویدادها باید در Slack پست شوند", "This will prevent any future updates from being posted to this Slack channel. Are you sure?": "This will prevent any future updates from being posted to this Slack channel. Are you sure?", "Whoops, you need to accept the permissions in Slack to connect {{appName}} to your workspace. Try again?": "Whoops, you need to accept the permissions in Slack to connect {{appName}} to your workspace. Try again?", "Personal account": "Personal account", "Link your {{appName}} account to Slack to enable searching and previewing the documents you have access to, directly within chat.": "Link your {{appName}} account to Slack to enable searching and previewing the documents you have access to, directly within chat.", "Disconnecting your personal account will prevent searching for documents from Slack. Are you sure?": "Disconnecting your personal account will prevent searching for documents from S<PERSON>ck. Are you sure?", "Slash command": "Slash command", "Get rich previews of {{ appName }} links shared in Slack and use the <em>{{ command }}</em> slash command to search for documents without leaving your chat.": "Get rich previews of {{ appName }} links shared in Slack and use the <em>{{ command }}</em> slash command to search for documents without leaving your chat.", "This will remove the Outline slash command from your Slack workspace. Are you sure?": "This will remove the Outline slash command from your Slack workspace. Are you sure?", "Connect {{appName}} collections to Slack channels. Messages will be automatically posted to Slack when documents are published or updated.": "Connect {{appName}} collections to Slack channels. Messages will be automatically posted to Slack when documents are published or updated.", "Comment by {{ author }} on \"{{ title }}\"": "Comment by {{ author }} on \"{{ title }}\"", "How to use {{ command }}": "How to use {{ command }}", "To search your workspace use {{ command }}. \nType {{ command2 }} help to display this help text.": "To search your workspace use {{ command }}. \nType {{ command2 }} help to display this help text.", "Post to Channel": "Post to Channel", "This is what we found for \"{{ term }}\"": "This is what we found for \"{{ term }}\"", "No results for \"{{ term }}\"": "No results for \"{{ term }}\"", "It looks like you haven’t linked your {{ appName }} account to Slack yet": "It looks like you haven’t linked your {{ appName }} account to Slack yet", "Link your account": "Link your account", "Link your account in {{ appName }} settings to search from Slack": "Link your account in {{ appName }} settings to search from Slack", "Configure a Umami installation to send views and analytics from the workspace to your own Umami instance.": "Configure a Umami installation to send views and analytics from the workspace to your own Umami instance.", "The URL of your Umami instance. If you are using Umami Cloud it will begin with {{ url }}": "The URL of your Umami instance. If you are using Umami Cloud it will begin with {{ url }}", "Script name": "Script name", "The name of the script file that Umami uses to track analytics.": "The name of the script file that <PERSON><PERSON> uses to track analytics.", "An ID that uniquely identifies the website in your Umami instance.": "An ID that uniquely identifies the website in your Umami instance.", "Are you sure you want to delete the {{ name }} webhook?": "Are you sure you want to delete the {{ name }} webhook?", "Webhook updated": "Webhook updated", "Update": "Update", "Updating": "Updating", "Provide a descriptive name for this webhook and the URL we should send a POST request to when matching events are created.": "Provide a descriptive name for this webhook and the URL we should send a POST request to when matching events are created.", "A memorable identifer": "A memorable identifer", "URL": "URL", "Signing secret": "Signing secret", "Subscribe to all events, groups, or individual events. We recommend only subscribing to the minimum amount of events that your application needs to function.": "Subscribe to all events, groups, or individual events. We recommend only subscribing to the minimum amount of events that your application needs to function.", "All events": "همه رویدادها", "All {{ groupName }} events": "همه رویدادهای {{ groupName }}", "Delete webhook": "حذف وب هوک", "Subscribed events": "رویدادهای مشترک شده", "Edit webhook": "ویرایش وب هوک", "Webhook created": "وب هوک ایجاد شد", "Webhooks": "وب هوک ها", "New webhook": "Webhook جدید", "Webhooks can be used to notify your application when events happen in {{appName}}. Events are sent as a https request with a JSON payload in near real-time.": "از Webhooks می توان برای اطلاع دادن به برنامه شما در صورت وقوع رویدادها در {{appName}}استفاده کرد. رویدادها به عنوان یک درخواست https با یک بار JSON در زمان واقعی ارسال می شوند.", "Inactive": "Inactive", "Create a webhook": "ساخت وب هوک", "Zapier is a platform that allows {{appName}} to easily integrate with thousands of other business tools. Automate your workflows, sync data, and more.": "Zapier is a platform that allows {{appName}} to easily integrate with thousands of other business tools. Automate your workflows, sync data, and more.", "Never logged in": "Never logged in", "Online now": "Online now", "Online {{ timeAgo }}": "Online {{ timeAgo }}", "Viewed just now": "Viewed just now", "You updated {{ timeAgo }}": "You updated {{ timeAgo }}", "{{ user }} updated {{ timeAgo }}": "{{ user }} updated {{ timeAgo }}", "You created {{ timeAgo }}": "You created {{ timeAgo }}", "{{ user }} created {{ timeAgo }}": "{{ user }} created {{ timeAgo }}", "Error loading data": "Error loading data"}
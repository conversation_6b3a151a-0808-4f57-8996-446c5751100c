<!DOCTYPE html>
<html lang="{lang}">
  <head>
    <title>{title}</title>
    <meta name="theme-color" content="#FFF" />
    <meta name="slack-app-id" content="{slack-app-id}" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, interactive-widget=resizes-content" />
    <meta name="color-scheme" content="light dark" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="description" content="{description}" />
    <meta name="darkreader-lock" />
    {noindex}
    <link rel="manifest" href="{manifest-url}" />
    <link rel="canonical" href="{canonical-url}" data-react-helmet="true" />
    {prefetch}
    <link rel="shortcut icon" type="image/png" href="{shortcut-icon-url}" sizes="32x32" />
    <link rel="apple-touch-icon" type="image/png" href="{cdn-url}/static/images/apple-touch-icon.png" sizes="192x192" />
    <link rel="search" type="application/opensearchdescription+xml" href="/opensearch.xml" title="Outline" />
    <style>
      @font-face {
        font-family: "Inter";
        font-display: swap;
        font-feature-settings: "ss03", "cv05";
        src: url("{cdn-url}/fonts/Inter.var.woff2") format("woff2");
      }

      @font-face {
        font-family: "Inter";
        font-display: swap;
        font-feature-settings: "ss03", "cv05";
        font-style: italic;
        src: url("{cdn-url}/fonts/Inter-italic.var.woff2") format("woff2");
      }

      body,
      html {
        margin: 0;
        padding: 0;
      }

      body {
        display: flex;
        width: 100%;
        height: 100%;
      }

      #root {
        flex: 1;
        min-height: 100%;
        width: 100%;
      }

      .screenreader-only {
        clip: rect(0 0 0 0);
        clip-path: inset(50%);
        height: 1px;
        overflow: hidden;
        position: absolute;
        white-space: nowrap;
        width: 1px;
      }
    </style>
  </head>

  <body>
    <div id="root"></div>
    {env}
    <script nonce="{csp-nonce}">
      if (window.localStorage && window.localStorage.getItem("theme") === "dark") {
        var color = "#111319";
        document.querySelector("#root").style.background = color;
        document.querySelector('meta[name="theme-color"]').setAttribute("content", color);
      }
    </script>
    {script-tags}
    <div class="screenreader-only">
      {content}
    </div>
  </body>
</html>
